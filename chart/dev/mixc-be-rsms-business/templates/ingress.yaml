{{- if .Values.ingress.enabled }}
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: {{ .Values.name }}
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 100m
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
spec:
  {{- if .Values.ingress.tls.enabled }}
  tls:
    {{- range .Values.ingress.tls.httpsHosts}}
    {{- with .}}
    - secretName: {{.keyName}}
      hosts:
        {{- range .hosts}}
        - {{.}}
        {{- end}}
    {{- end}}
    {{- end}}
  {{- end}}
  rules:
    {{- range .Values.ingress.hosts}}
    {{- with .}}
    - host: {{ .name }}
      http:
        paths:
          {{- range .route }}
          {{- with .}}
          - path: {{.path}}
            backend:
              serviceName: {{.backendService}}
              servicePort: {{.backendServicePort}}
          {{- end}}
          {{- end}}
    {{- end }}
    {{- end }}


{{- end }}

