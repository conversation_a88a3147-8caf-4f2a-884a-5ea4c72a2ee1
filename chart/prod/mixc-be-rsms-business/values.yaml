################################### Pod基础信息
# 应用名称
name: rsms-business                                               # Pod 名称、Service 名称、Ingress 名称
deployment:
  image: registry.steam.crcloud.com/crmixclifestyle-rentsale/rsms-business  # Pod 使用的镜像
  replicaCount: 1                                         # Pod 副本数
  configMaps: 
    enabled: false                                         # 是否启用文件映射，开启后下列映射关系会生效
    pathMaps:

  storage:
    enabled: false                                         # 使用启用存储映射，开启后下列映射关系会生效
    pathMaps:
      k8s-test-pvc: /data                                 # 将名称为 k8s-test-pvc 的PVC存储卷，挂载到Pod 内 /data 目录
  hostAliase:
    enabled: false
    hostMaps:
      - ip: "***********"
        hostnames:
          - "web01.example.com"
          - "web02.example.com"
      - ip: "***********"
        hostnames:
          - "web03.example.com"
          - "web04.example.com"

################################### Pod 健康检测
healthCheck:
  livenessProbe:                                          # 存活性探测，若检测不通过，会导致Pod 被重启
    initialDelaySeconds: 30                               # Pod 运行多长时间后，开始进行探测
    failureThreshold: 3                                   # 几次探测失败，则认为Pod 探测失败
    periodSeconds: 30                                     # 每次探测时间间隔
    successThreshold: 1                                   # 几次探测成功，则认为Pod 探测成功
    timeoutSeconds: 3                                     # 每次探测的超时时间
    httpGet:                                              # 每次探测使用Http 方式进行
      path: /actuator/health                                      # 目标URL
      port: 7904                                            # 目标端口

  readinessProbe:                                         # 就绪性探测，若不通过，则请求不会分配给该Pod
    initialDelaySeconds: 60                               # 参数意义同上
    failureThreshold: 3
    periodSeconds: 30
    successThreshold: 1
    timeoutSeconds: 3
    httpGet:
      path: /actuator/health
      port: 7904


################################### Pod 额外标签    
extraLabels:
  enabled: true
  items:
    project: rsms      #项目名称
    service: rsms-business       #服务名称
    env: prod
    topic: log-rsms-business      #topic名称

################################### pod亲和性规则
matchExpressions:
  - key: project
    operator: In
    values:
      - rsms


################################### Pod 环境变量
env:
  enabled: true
  items:
    spring.profiles.active: prod
    SERVER_PORT: 7904
    NACOS_NAMESPACE: rsms-prod
    NACOS_ADDR: mixc-be-rsms-nacos:8848

################################### Pod 资源限制 （1000m == 1coreCPU） requests配的值必须小于limits
resources:
  requests:
    memory: "1G"
    cpu: "1000m"
  limits:
    memory: "2Gi"
    cpu: "2000m"

################################### Service 定义
service:
  enabled: true
  type: ClusterIP
  port: 7904                                              # service 监听在这个端口，接收请求
  targetPort: 7904                                          # service 将接收到的请求，丢给Pod 的哪个端口去处理

################################### Ingress 定义
ingress:
  enabled: false                                           # 是否需要ingress 资源
  tls:      
    enabled: true                                         # 是否开启HTTPS
    httpsHosts:
      - keyName: crc-key                                  # HTTPS 证书文件
        hosts:
          - web01.crc.com.cn                              # 为该域名启用HTTPS，要求域名必须存在于下方 hosts 定义中
  hosts: 
    - name: web01.crc.com.cn                              # ingress 域名
      route:
        - path: /                                         # 前缀匹配，接到的请求若以 / 开头，则丢给后端 名称为 app01 的service 的8080 去处理
          backendService: app01
          backendServicePort: 8080

    - name: web02.crc.com.cn
      route:
        - path: /web02
          backendService: app01
          backendServicePort: 8080
