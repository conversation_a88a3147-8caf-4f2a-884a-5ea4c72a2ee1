apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.name }}
spec:
  replicas: {{ .Values.deployment.replicaCount }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1 
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: {{ .Values.name }}
      {{- if .Values.extraLabels.enabled}}
      {{- range $k, $v := .Values.extraLabels.items }}
      {{$k}}: "{{$v}}"
      {{- end}}
      {{- end}}
  template:
    metadata:
      labels:
        app: {{ .Values.name }}
        {{- if .Values.extraLabels.enabled}}
        {{- range $k, $v := .Values.extraLabels.items }}
        {{$k}}: "{{$v}}"
        {{- end}}
        {{- end}}
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  {{- toYaml .Values.matchExpressions | nindent 16 }}
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - {{ .Values.name }}
              topologyKey: kubernetes.io/hostname
            weight: 100
 

      {{- if or .Values.deployment.configMaps.enabled  .Values.deployment.storage.enabled}}
      volumes:
        {{- if .Values.deployment.storage.enabled}}
        {{- range $k, $v := .Values.deployment.storage.pathMaps }}
        - name: {{$k}}
          persistentVolumeClaim:
            claimName: {{$k}}
        {{- end}}
        {{- end}}
        {{- if .Values.deployment.configMaps.enabled}}
        - name: config-data
          configMap:
            name: {{.Values.name}}
        {{- end}}
      {{- end}}

      {{- if  .Values.deployment.hostAliase.enabled}}
      hostAliases:
      {{- range $item := .Values.deployment.hostAliase.hostMaps }}
      - ip: {{ $item.ip}}
        hostnames:
        {{- range $host := $item.hostnames}}
        - "{{$host}}"
        {{- end}}
      {{- end}}
      {{- end}}

      restartPolicy: Always
      containers:
        - name: {{ .Values.name }}
          image: "{{ .Values.deployment.image }}:{{.Chart.Version}}"
          imagePullPolicy: IfNotPresent
          {{- if .Values.env.enabled  }}
          env:
            {{- range $k, $v := .Values.env.items }}
            - name: "{{$k}}"
              value: "{{$v}}"
            {{- end }}
          {{- end }}
          {{- if or .Values.deployment.storage.enabled  .Values.deployment.configMaps.enabled}}
          volumeMounts:
            {{- if .Values.deployment.storage.enabled}}
            {{- range $k, $v := .Values.deployment.storage.pathMaps}}
            - name: {{ $k}}
              mountPath: {{$v}}
            {{- end}}
            {{- end}}
            {{- if .Values.deployment.configMaps.enabled}}
            {{- range $k, $v := .Values.deployment.configMaps.pathMaps}}
            - name: config-data
              subPath: {{ $k }}
              mountPath: {{ $v }}
              readOnly: true
            {{- end}}
            {{- end}}
          {{- end}}
          resources:
            requests:
              cpu: {{ .Values.resources.requests.cpu }}
              memory: {{ .Values.resources.requests.memory }}
            limits:
              cpu: {{ .Values.resources.limits.cpu }}
              memory: {{ .Values.resources.limits.memory }}
          
{{ toYaml .Values.healthCheck |indent 10}}






