{{- if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.name }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    app: {{ .Values.name}}
    {{- if .Values.extraLabels.enabled}}
    {{- range $k, $v := .Values.extraLabels.items }}
    {{$k}}: "{{$v}}"
    {{- end}}
    {{- end}}
{{- end }}
