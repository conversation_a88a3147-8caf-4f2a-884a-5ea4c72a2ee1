# 指定基础镜像
FROM registry.steam.crcloud.com/crmixclifestyle-rentsale/rsms/jdk-base:21.0.5
# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime; \
    echo 'Asia/Shanghai' >/etc/timezone
# 设置JVM运行参数
ENV JAVA_OPTS ""
ENV JVM_DOCKER_OPTS -XX:MaxRAMPercentage=90.0 -XX:InitialRAMPercentage=25.0
# 设置Application环境变量
ENV NACOS_NAMESPACE: rsms-dev
ENV NACOS_ADDR: mixc-be-rsms-nacos:8848

# 复制工程包
COPY business.jar business.jar

ENTRYPOINT exec java $JVM_DOCKER_OPTS $JAVA_OPTS -jar business.jar