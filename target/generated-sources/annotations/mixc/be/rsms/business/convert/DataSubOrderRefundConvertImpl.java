package mixc.be.rsms.business.convert;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.SubOrderRefundAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessSubOrderRefund;
import mixc.be.rsms.business.vo.OrderRefundVo;
import mixc.be.rsms.pojo.business.OrderRefundResp;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:43+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class DataSubOrderRefundConvertImpl implements DataSubOrderRefundConvert {

    @Override
    public OrderRefundVo data2Vo(TbBusinessSubOrderRefund orderRefund) {
        if ( orderRefund == null ) {
            return null;
        }

        OrderRefundVo orderRefundVo = new OrderRefundVo();

        orderRefundVo.setId( orderRefund.getId() );
        orderRefundVo.setParentOrderId( orderRefund.getParentOrderId() );
        orderRefundVo.setSubOrderCode( orderRefund.getSubOrderCode() );
        orderRefundVo.setReceiveSubOrderId( orderRefund.getReceiveSubOrderId() );
        orderRefundVo.setAmount( orderRefund.getAmount() );
        orderRefundVo.setRefundChannel( orderRefund.getRefundChannel() );
        orderRefundVo.setBankNum( orderRefund.getBankNum() );
        orderRefundVo.setAccountName( orderRefund.getAccountName() );
        orderRefundVo.setBankName( orderRefund.getBankName() );
        orderRefundVo.setCity( orderRefund.getCity() );
        orderRefundVo.setOrderStatus( orderRefund.getOrderStatus() );
        orderRefundVo.setCreateTime( orderRefund.getCreateTime() );
        orderRefundVo.setCreateUser( orderRefund.getCreateUser() );

        return orderRefundVo;
    }

    @Override
    public OrderRefundResp data2Resp(TbBusinessSubOrderRefund orderRefund) {
        if ( orderRefund == null ) {
            return null;
        }

        OrderRefundResp orderRefundResp = new OrderRefundResp();

        orderRefundResp.setId( orderRefund.getId() );
        orderRefundResp.setParentOrderId( orderRefund.getParentOrderId() );
        orderRefundResp.setSubOrderCode( orderRefund.getSubOrderCode() );
        orderRefundResp.setReceiveSubOrderId( orderRefund.getReceiveSubOrderId() );
        orderRefundResp.setAmount( orderRefund.getAmount() );
        orderRefundResp.setRefundChannel( orderRefund.getRefundChannel() );
        orderRefundResp.setBankNum( orderRefund.getBankNum() );
        orderRefundResp.setAccountName( orderRefund.getAccountName() );
        orderRefundResp.setBankName( orderRefund.getBankName() );
        orderRefundResp.setCity( orderRefund.getCity() );
        orderRefundResp.setOrderStatus( orderRefund.getOrderStatus() );
        orderRefundResp.setCreateTime( orderRefund.getCreateTime() );

        return orderRefundResp;
    }

    @Override
    public TbBusinessSubOrderRefund add2Data(SubOrderRefundAddParams params) {
        if ( params == null ) {
            return null;
        }

        TbBusinessSubOrderRefund tbBusinessSubOrderRefund = new TbBusinessSubOrderRefund();

        tbBusinessSubOrderRefund.setReceiveSubOrderId( params.getReceiveSubOrderId() );
        tbBusinessSubOrderRefund.setAmount( params.getAmount() );
        tbBusinessSubOrderRefund.setRefundChannel( params.getRefundChannel() );
        tbBusinessSubOrderRefund.setBankNum( params.getBankNum() );
        tbBusinessSubOrderRefund.setAccountName( params.getAccountName() );
        tbBusinessSubOrderRefund.setBankName( params.getBankName() );
        tbBusinessSubOrderRefund.setCity( params.getCity() );
        tbBusinessSubOrderRefund.setRemark( params.getRemark() );

        return tbBusinessSubOrderRefund;
    }

    @Override
    public List<OrderRefundResp> vo2RespList(List<OrderRefundVo> orderRefundVos) {
        if ( orderRefundVos == null ) {
            return null;
        }

        List<OrderRefundResp> list = new ArrayList<OrderRefundResp>( orderRefundVos.size() );
        for ( OrderRefundVo orderRefundVo : orderRefundVos ) {
            list.add( orderRefundVoToOrderRefundResp( orderRefundVo ) );
        }

        return list;
    }

    protected OrderRefundResp orderRefundVoToOrderRefundResp(OrderRefundVo orderRefundVo) {
        if ( orderRefundVo == null ) {
            return null;
        }

        OrderRefundResp orderRefundResp = new OrderRefundResp();

        orderRefundResp.setId( orderRefundVo.getId() );
        orderRefundResp.setParentOrderId( orderRefundVo.getParentOrderId() );
        orderRefundResp.setSubOrderCode( orderRefundVo.getSubOrderCode() );
        orderRefundResp.setReceiveSubOrderId( orderRefundVo.getReceiveSubOrderId() );
        orderRefundResp.setAmount( orderRefundVo.getAmount() );
        orderRefundResp.setRefundChannel( orderRefundVo.getRefundChannel() );
        orderRefundResp.setBankNum( orderRefundVo.getBankNum() );
        orderRefundResp.setAccountName( orderRefundVo.getAccountName() );
        orderRefundResp.setBankName( orderRefundVo.getBankName() );
        orderRefundResp.setCity( orderRefundVo.getCity() );
        orderRefundResp.setOrderStatus( orderRefundVo.getOrderStatus() );
        orderRefundResp.setCreateTime( orderRefundVo.getCreateTime() );

        return orderRefundResp;
    }
}
