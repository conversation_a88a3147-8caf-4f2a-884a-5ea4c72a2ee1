package mixc.be.rsms.business.convert;

import java.time.LocalDateTime;
import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.ReceiveCommissionPlanAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessReceiveCommissionPlan;
import mixc.be.rsms.business.vo.ReceiveCommissionPlanVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:43+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class DataReceiveCommissionPlanConvertImpl implements DataReceiveCommissionPlanConvert {

    @Override
    public TbBusinessReceiveCommissionPlan addParamsToData(ReceiveCommissionPlanAddParams params) {
        if ( params == null ) {
            return null;
        }

        TbBusinessReceiveCommissionPlan tbBusinessReceiveCommissionPlan = new TbBusinessReceiveCommissionPlan();

        tbBusinessReceiveCommissionPlan.setCostItemDesc( params.getCostItemDesc() );
        tbBusinessReceiveCommissionPlan.setCostItemId( params.getCostItemId() );
        tbBusinessReceiveCommissionPlan.setPayer( params.getPayer() );
        tbBusinessReceiveCommissionPlan.setReceivableCommission( params.getReceivableCommission() );
        tbBusinessReceiveCommissionPlan.setExpectPayment( params.getExpectPayment() );
        if ( params.getActualPayment() != null ) {
            tbBusinessReceiveCommissionPlan.setActualPayment( params.getActualPayment().atStartOfDay() );
        }
        tbBusinessReceiveCommissionPlan.setReceivedCommission( params.getReceivedCommission() );
        tbBusinessReceiveCommissionPlan.setRemark( params.getRemark() );
        tbBusinessReceiveCommissionPlan.setDepositId( params.getDepositId() );
        tbBusinessReceiveCommissionPlan.setTransactionInfoId( params.getTransactionInfoId() );
        tbBusinessReceiveCommissionPlan.setOrderId( params.getOrderId() );
        tbBusinessReceiveCommissionPlan.setOrderCode( params.getOrderCode() );
        tbBusinessReceiveCommissionPlan.setOrderStatus( params.getOrderStatus() );
        tbBusinessReceiveCommissionPlan.setSubOrderId( params.getSubOrderId() );
        tbBusinessReceiveCommissionPlan.setSubOrderCode( params.getSubOrderCode() );

        return tbBusinessReceiveCommissionPlan;
    }

    @Override
    public ReceiveCommissionPlanVo dataToVo(TbBusinessReceiveCommissionPlan data) {
        if ( data == null ) {
            return null;
        }

        ReceiveCommissionPlanVo receiveCommissionPlanVo = new ReceiveCommissionPlanVo();

        receiveCommissionPlanVo.setId( data.getId() );
        receiveCommissionPlanVo.setReportId( data.getReportId() );
        receiveCommissionPlanVo.setCostItemId( data.getCostItemId() );
        receiveCommissionPlanVo.setCostItemDesc( data.getCostItemDesc() );
        receiveCommissionPlanVo.setPayer( data.getPayer() );
        receiveCommissionPlanVo.setReceivableCommission( data.getReceivableCommission() );
        receiveCommissionPlanVo.setExpectPayment( data.getExpectPayment() );
        receiveCommissionPlanVo.setActualPayment( data.getActualPayment() );
        receiveCommissionPlanVo.setReceivedCommission( data.getReceivedCommission() );
        receiveCommissionPlanVo.setRemark( data.getRemark() );
        receiveCommissionPlanVo.setDepositId( data.getDepositId() );
        receiveCommissionPlanVo.setTransactionInfoId( data.getTransactionInfoId() );
        receiveCommissionPlanVo.setOrderId( data.getOrderId() );
        receiveCommissionPlanVo.setOrderCode( data.getOrderCode() );
        receiveCommissionPlanVo.setOrderStatus( data.getOrderStatus() );
        receiveCommissionPlanVo.setSubOrderId( data.getSubOrderId() );
        receiveCommissionPlanVo.setSubOrderCode( data.getSubOrderCode() );

        return receiveCommissionPlanVo;
    }
}
