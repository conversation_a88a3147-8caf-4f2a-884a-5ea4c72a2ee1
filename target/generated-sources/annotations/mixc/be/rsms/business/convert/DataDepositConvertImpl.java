package mixc.be.rsms.business.convert;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.DepositAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessDeposit;
import mixc.be.rsms.business.vo.DepositVo;
import mixc.be.rsms.pojo.business.DepositResp;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:43+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class DataDepositConvertImpl implements DataDepositConvert {

    @Override
    public DepositVo dataToVo(TbBusinessDeposit deposit) {
        if ( deposit == null ) {
            return null;
        }

        DepositVo.DepositVoBuilder depositVo = DepositVo.builder();

        depositVo.id( deposit.getId() );
        depositVo.depositCode( deposit.getDepositCode() );
        depositVo.type( deposit.getType() );
        depositVo.category( deposit.getCategory() );
        depositVo.transactionContractNum( deposit.getTransactionContractNum() );
        depositVo.customerId( deposit.getCustomerId() );
        depositVo.customerMobile( deposit.getCustomerMobile() );
        depositVo.brokerId( deposit.getBrokerId() );
        depositVo.status( deposit.getStatus() );
        depositVo.auditTime( deposit.getAuditTime() );
        depositVo.remark( deposit.getRemark() );
        depositVo.orderId( deposit.getOrderId() );
        depositVo.orderCode( deposit.getOrderCode() );

        return depositVo.build();
    }

    @Override
    public TbBusinessDeposit addParamToData(DepositAddParams depositAddParams) {
        if ( depositAddParams == null ) {
            return null;
        }

        TbBusinessDeposit tbBusinessDeposit = new TbBusinessDeposit();

        tbBusinessDeposit.setId( depositAddParams.getId() );
        tbBusinessDeposit.setType( depositAddParams.getType() );
        tbBusinessDeposit.setCategory( depositAddParams.getCategory() );
        tbBusinessDeposit.setCustomerId( depositAddParams.getCustomerId() );
        tbBusinessDeposit.setCustomerMobile( depositAddParams.getCustomerMobile() );
        tbBusinessDeposit.setBrokerId( depositAddParams.getBrokerId() );
        tbBusinessDeposit.setRemark( depositAddParams.getRemark() );

        return tbBusinessDeposit;
    }

    @Override
    public List<DepositResp> data2RespList(List<TbBusinessDeposit> tbBusinessDeposits) {
        if ( tbBusinessDeposits == null ) {
            return null;
        }

        List<DepositResp> list = new ArrayList<DepositResp>( tbBusinessDeposits.size() );
        for ( TbBusinessDeposit tbBusinessDeposit : tbBusinessDeposits ) {
            list.add( data2Resp( tbBusinessDeposit ) );
        }

        return list;
    }

    @Override
    public DepositResp data2Resp(TbBusinessDeposit tbBusinessDeposits) {
        if ( tbBusinessDeposits == null ) {
            return null;
        }

        DepositResp depositResp = new DepositResp();

        depositResp.setRevision( tbBusinessDeposits.getRevision() );
        depositResp.setCreateUser( tbBusinessDeposits.getCreateUser() );
        depositResp.setCreateTime( tbBusinessDeposits.getCreateTime() );
        depositResp.setUpdateUser( tbBusinessDeposits.getUpdateUser() );
        depositResp.setUpdateTime( tbBusinessDeposits.getUpdateTime() );
        depositResp.setIsDeleted( tbBusinessDeposits.getIsDeleted() );
        depositResp.setId( tbBusinessDeposits.getId() );
        depositResp.setDepositCode( tbBusinessDeposits.getDepositCode() );
        depositResp.setType( tbBusinessDeposits.getType() );
        depositResp.setCategory( tbBusinessDeposits.getCategory() );
        depositResp.setTransactionContractNum( tbBusinessDeposits.getTransactionContractNum() );
        depositResp.setHouseDesc( tbBusinessDeposits.getHouseDesc() );
        depositResp.setCustomerId( tbBusinessDeposits.getCustomerId() );
        depositResp.setCustomerMobile( tbBusinessDeposits.getCustomerMobile() );
        depositResp.setBrokerId( tbBusinessDeposits.getBrokerId() );
        depositResp.setStatus( tbBusinessDeposits.getStatus() );
        depositResp.setAuditTime( tbBusinessDeposits.getAuditTime() );
        depositResp.setRemark( tbBusinessDeposits.getRemark() );

        return depositResp;
    }

    @Override
    public TbBusinessDeposit resp2Data(DepositResp depositResp) {
        if ( depositResp == null ) {
            return null;
        }

        TbBusinessDeposit tbBusinessDeposit = new TbBusinessDeposit();

        tbBusinessDeposit.setRevision( depositResp.getRevision() );
        tbBusinessDeposit.setCreateUser( depositResp.getCreateUser() );
        tbBusinessDeposit.setCreateTime( depositResp.getCreateTime() );
        tbBusinessDeposit.setUpdateUser( depositResp.getUpdateUser() );
        tbBusinessDeposit.setUpdateTime( depositResp.getUpdateTime() );
        tbBusinessDeposit.setIsDeleted( depositResp.getIsDeleted() );
        tbBusinessDeposit.setId( depositResp.getId() );
        tbBusinessDeposit.setDepositCode( depositResp.getDepositCode() );
        tbBusinessDeposit.setType( depositResp.getType() );
        tbBusinessDeposit.setCategory( depositResp.getCategory() );
        tbBusinessDeposit.setTransactionContractNum( depositResp.getTransactionContractNum() );
        tbBusinessDeposit.setHouseDesc( depositResp.getHouseDesc() );
        tbBusinessDeposit.setCustomerId( depositResp.getCustomerId() );
        tbBusinessDeposit.setCustomerMobile( depositResp.getCustomerMobile() );
        tbBusinessDeposit.setBrokerId( depositResp.getBrokerId() );
        tbBusinessDeposit.setStatus( depositResp.getStatus() );
        tbBusinessDeposit.setAuditTime( depositResp.getAuditTime() );
        tbBusinessDeposit.setRemark( depositResp.getRemark() );

        return tbBusinessDeposit;
    }
}
