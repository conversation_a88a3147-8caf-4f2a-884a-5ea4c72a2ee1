package mixc.be.rsms.business.convert;

import javax.annotation.processing.Generated;
import mixc.be.rsms.business.vo.HouseInfoVo;
import mixc.be.rsms.pojo.data.HouseInfoResp;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:43+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class DataHouseConvertImpl implements DataHouseConvert {

    @Override
    public HouseInfoResp vo2Resp(HouseInfoVo vo) {
        if ( vo == null ) {
            return null;
        }

        HouseInfoResp.HouseInfoRespBuilder houseInfoResp = HouseInfoResp.builder();

        houseInfoResp.id( vo.getId() );
        houseInfoResp.location( vo.getLocation() );
        houseInfoResp.houseNumber( vo.getHouseNumber() );
        houseInfoResp.building( vo.getBuilding() );
        houseInfoResp.unit( vo.getUnit() );
        houseInfoResp.floor( vo.getFloor() );
        houseInfoResp.communityId( vo.getCommunityId() );
        houseInfoResp.communityName( vo.getCommunityName() );
        houseInfoResp.rentStatus( vo.getRentStatus() );
        houseInfoResp.houseType( vo.getHouseType() );
        houseInfoResp.sellPrice( vo.getSellPrice() );
        houseInfoResp.rentPrice( vo.getRentPrice() );
        houseInfoResp.layoutCode( vo.getLayoutCode() );
        houseInfoResp.createUser( vo.getCreateUser() );
        houseInfoResp.maintenanceBy( vo.getMaintenanceBy() );
        houseInfoResp.openedBy( vo.getOpenedBy() );
        houseInfoResp.picBy( vo.getPicBy() );
        houseInfoResp.videoBy( vo.getVideoBy() );
        houseInfoResp.keyBy( vo.getKeyBy() );
        houseInfoResp.delegateBy( vo.getDelegateBy() );
        houseInfoResp.dickBy( vo.getDickBy() );
        houseInfoResp.concatUser( vo.getConcatUser() );

        return houseInfoResp.build();
    }
}
