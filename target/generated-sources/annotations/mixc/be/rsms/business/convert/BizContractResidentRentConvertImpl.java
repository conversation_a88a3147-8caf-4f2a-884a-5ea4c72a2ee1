package mixc.be.rsms.business.convert;

import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.ContractSignAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessContractResidentRent;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:43+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class BizContractResidentRentConvertImpl implements BizContractResidentRentConvert {

    @Override
    public TbBusinessContractResidentRent convert(ContractSignAddParams params) {
        if ( params == null ) {
            return null;
        }

        TbBusinessContractResidentRent tbBusinessContractResidentRent = new TbBusinessContractResidentRent();

        tbBusinessContractResidentRent.setId( params.getId() );
        tbBusinessContractResidentRent.setLeaserProxyUserName( params.getLeaserProxyUserName() );
        tbBusinessContractResidentRent.setLeaserProxyUserMobile( params.getLeaserProxyUserMobile() );
        tbBusinessContractResidentRent.setLeaserProxyUserIdNumber( params.getLeaserProxyUserIdNumber() );
        tbBusinessContractResidentRent.setCustomerProxyUserName( params.getCustomerProxyUserName() );
        tbBusinessContractResidentRent.setCustomerProxyUserMobile( params.getCustomerProxyUserMobile() );
        tbBusinessContractResidentRent.setCustomerProxyUserIdNumber( params.getCustomerProxyUserIdNumber() );
        tbBusinessContractResidentRent.setCoPurchaser( params.getCoPurchaser() );

        return tbBusinessContractResidentRent;
    }
}
