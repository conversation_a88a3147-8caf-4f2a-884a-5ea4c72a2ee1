package mixc.be.rsms.business.convert;

import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.ContractInfoAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessContractInfo;
import mixc.be.rsms.business.vo.ContractInfoVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:43+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class DataContractInfoConvertImpl implements DataContractInfoConvert {

    @Override
    public TbBusinessContractInfo addParamsToData(ContractInfoAddParams params) {
        if ( params == null ) {
            return null;
        }

        TbBusinessContractInfo tbBusinessContractInfo = new TbBusinessContractInfo();

        tbBusinessContractInfo.setId( params.getId() );
        tbBusinessContractInfo.setOwnerMobile( params.getOwnerMobile() );
        tbBusinessContractInfo.setOwnerName( params.getOwnerName() );
        tbBusinessContractInfo.setOwnerIcardNum( params.getOwnerIcardNum() );
        tbBusinessContractInfo.setOwnerAddress( params.getOwnerAddress() );
        tbBusinessContractInfo.setCustomerMobile( params.getCustomerMobile() );
        tbBusinessContractInfo.setCustomerName( params.getCustomerName() );
        tbBusinessContractInfo.setCustomerIcardNum( params.getCustomerIcardNum() );
        tbBusinessContractInfo.setCustomerAddress( params.getCustomerAddress() );

        return tbBusinessContractInfo;
    }

    @Override
    public ContractInfoVo dataToVo(TbBusinessContractInfo info) {
        if ( info == null ) {
            return null;
        }

        ContractInfoVo contractInfoVo = new ContractInfoVo();

        contractInfoVo.setId( info.getId() );
        contractInfoVo.setReportId( info.getReportId() );
        contractInfoVo.setOwnerMobile( info.getOwnerMobile() );
        contractInfoVo.setOwnerName( info.getOwnerName() );
        contractInfoVo.setOwnerIcardNum( info.getOwnerIcardNum() );
        contractInfoVo.setOwnerAddress( info.getOwnerAddress() );
        contractInfoVo.setCustomerMobile( info.getCustomerMobile() );
        contractInfoVo.setCustomerName( info.getCustomerName() );
        contractInfoVo.setCustomerIcardNum( info.getCustomerIcardNum() );
        contractInfoVo.setCustomerAddress( info.getCustomerAddress() );

        return contractInfoVo;
    }
}
