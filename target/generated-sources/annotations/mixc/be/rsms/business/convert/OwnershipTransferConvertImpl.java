package mixc.be.rsms.business.convert;

import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.OwnershipTransferUpdateParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessOwnershipTransfer;
import mixc.be.rsms.business.vo.OwnershipTransferVo;
import mixc.be.rsms.pojo.business.OwnershipTransferResp;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:42+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class OwnershipTransferConvertImpl implements OwnershipTransferConvert {

    @Override
    public TbBusinessOwnershipTransfer convert(OwnershipTransferUpdateParams dto) {
        if ( dto == null ) {
            return null;
        }

        TbBusinessOwnershipTransfer tbBusinessOwnershipTransfer = new TbBusinessOwnershipTransfer();

        tbBusinessOwnershipTransfer.setRevision( dto.getRevision() );
        tbBusinessOwnershipTransfer.setId( dto.getId() );
        tbBusinessOwnershipTransfer.setTransferContractNumber( dto.getTransferContractNumber() );
        tbBusinessOwnershipTransfer.setExpectTransferDate( dto.getExpectTransferDate() );
        tbBusinessOwnershipTransfer.setCurrentTransferStage( dto.getCurrentTransferStage() );
        tbBusinessOwnershipTransfer.setCurrentStageFinishDate( dto.getCurrentStageFinishDate() );
        tbBusinessOwnershipTransfer.setNextTransferStage( dto.getNextTransferStage() );
        tbBusinessOwnershipTransfer.setRemarks( dto.getRemarks() );

        return tbBusinessOwnershipTransfer;
    }

    @Override
    public OwnershipTransferVo dataToVo(TbBusinessOwnershipTransfer transfer) {
        if ( transfer == null ) {
            return null;
        }

        OwnershipTransferVo ownershipTransferVo = new OwnershipTransferVo();

        ownershipTransferVo.setId( transfer.getId() );
        ownershipTransferVo.setTransferContractNumber( transfer.getTransferContractNumber() );
        ownershipTransferVo.setTransferStatus( transfer.getTransferStatus() );
        ownershipTransferVo.setExpectTransferDate( transfer.getExpectTransferDate() );
        ownershipTransferVo.setCurrentTransferStage( transfer.getCurrentTransferStage() );
        ownershipTransferVo.setCurrentStageFinishDate( transfer.getCurrentStageFinishDate() );
        ownershipTransferVo.setNextTransferStage( transfer.getNextTransferStage() );
        ownershipTransferVo.setRemarks( transfer.getRemarks() );
        ownershipTransferVo.setCreateUser( transfer.getCreateUser() );

        return ownershipTransferVo;
    }

    @Override
    public OwnershipTransferResp data2Resp(TbBusinessOwnershipTransfer transfer) {
        if ( transfer == null ) {
            return null;
        }

        OwnershipTransferResp ownershipTransferResp = new OwnershipTransferResp();

        ownershipTransferResp.setId( transfer.getId() );
        ownershipTransferResp.setTransferContractNumber( transfer.getTransferContractNumber() );
        ownershipTransferResp.setTransferStatus( transfer.getTransferStatus() );
        ownershipTransferResp.setExpectTransferDate( transfer.getExpectTransferDate() );
        ownershipTransferResp.setCurrentTransferStage( transfer.getCurrentTransferStage() );
        ownershipTransferResp.setCurrentStageFinishDate( transfer.getCurrentStageFinishDate() );
        ownershipTransferResp.setNextTransferStage( transfer.getNextTransferStage() );
        ownershipTransferResp.setRemarks( transfer.getRemarks() );
        ownershipTransferResp.setCreateUser( transfer.getCreateUser() );

        return ownershipTransferResp;
    }
}
