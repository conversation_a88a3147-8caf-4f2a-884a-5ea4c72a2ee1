package mixc.be.rsms.business.convert;

import java.time.LocalDateTime;
import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.ReceiveCommissionDetailAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessReceiveCommissionDetail;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:43+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class DataReceiveCommissionDetailConvertImpl implements DataReceiveCommissionDetailConvert {

    @Override
    public TbBusinessReceiveCommissionDetail addParamsToData(ReceiveCommissionDetailAddParams params) {
        if ( params == null ) {
            return null;
        }

        TbBusinessReceiveCommissionDetail tbBusinessReceiveCommissionDetail = new TbBusinessReceiveCommissionDetail();

        tbBusinessReceiveCommissionDetail.setReceiveCommissionPlanId( params.getReceiveCommissionPlanId() );
        tbBusinessReceiveCommissionDetail.setActualCommission( params.getActualCommission() );
        if ( params.getActualPayment() != null ) {
            tbBusinessReceiveCommissionDetail.setActualPayment( params.getActualPayment().atStartOfDay() );
        }
        tbBusinessReceiveCommissionDetail.setRemark( params.getRemark() );

        return tbBusinessReceiveCommissionDetail;
    }
}
