package mixc.be.rsms.business.convert;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.TransactionReportAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionReport;
import mixc.be.rsms.business.vo.TransactionReportVo;
import mixc.be.rsms.pojo.business.TransactionReportResp;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:43+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class DataTransactionReportConvertImpl implements DataTransactionReportConvert {

    @Override
    public TransactionReportVo dataToVo(TbBusinessTransactionReport report) {
        if ( report == null ) {
            return null;
        }

        TransactionReportVo transactionReportVo = new TransactionReportVo();

        transactionReportVo.setId( report.getId() );
        transactionReportVo.setReportCode( report.getReportCode() );
        transactionReportVo.setTransactionContractNum( report.getTransactionContractNum() );
        transactionReportVo.setStatus( report.getStatus() );
        transactionReportVo.setCategory( report.getCategory() );
        transactionReportVo.setCustomerId( report.getCustomerId() );
        transactionReportVo.setOwnerInfo( report.getOwnerInfo() );
        transactionReportVo.setDealDate( report.getDealDate() );
        transactionReportVo.setDealAmount( report.getDealAmount() );
        transactionReportVo.setBrokerId( report.getBrokerId() );
        transactionReportVo.setOwnerCommission( report.getOwnerCommission() );
        transactionReportVo.setCustomerCommission( report.getCustomerCommission() );
        transactionReportVo.setLandCertificateNum( report.getLandCertificateNum() );
        transactionReportVo.setPropertyRightNum( report.getPropertyRightNum() );
        transactionReportVo.setWarrantHolder( report.getWarrantHolder() );
        transactionReportVo.setDepositId( report.getDepositId() );
        transactionReportVo.setOtherExpenses( report.getOtherExpenses() );
        transactionReportVo.setSupplementaryTerms( report.getSupplementaryTerms() );
        transactionReportVo.setSignDate( report.getSignDate() );
        transactionReportVo.setCollectAllDate( report.getCollectAllDate() );
        transactionReportVo.setAwaitCommission( report.getAwaitCommission() );

        return transactionReportVo;
    }

    @Override
    public TbBusinessTransactionReport addParamsToData(TransactionReportAddParams params) {
        if ( params == null ) {
            return null;
        }

        TbBusinessTransactionReport tbBusinessTransactionReport = new TbBusinessTransactionReport();

        tbBusinessTransactionReport.setId( params.getId() );
        tbBusinessTransactionReport.setTransactionContractNum( params.getTransactionContractNum() );
        tbBusinessTransactionReport.setCategory( params.getCategory() );
        tbBusinessTransactionReport.setCustomerId( params.getCustomerId() );
        tbBusinessTransactionReport.setHouseId( params.getHouseId() );
        tbBusinessTransactionReport.setOwnerInfo( params.getOwnerInfo() );
        tbBusinessTransactionReport.setDealDate( params.getDealDate() );
        tbBusinessTransactionReport.setDealAmount( params.getDealAmount() );
        tbBusinessTransactionReport.setBrokerId( params.getBrokerId() );
        tbBusinessTransactionReport.setOwnerCommission( params.getOwnerCommission() );
        tbBusinessTransactionReport.setCustomerCommission( params.getCustomerCommission() );
        tbBusinessTransactionReport.setLandCertificateNum( params.getLandCertificateNum() );
        tbBusinessTransactionReport.setPropertyRightNum( params.getPropertyRightNum() );
        tbBusinessTransactionReport.setWarrantHolder( params.getWarrantHolder() );
        tbBusinessTransactionReport.setDepositId( params.getDepositId() );
        tbBusinessTransactionReport.setOtherExpenses( params.getOtherExpenses() );
        tbBusinessTransactionReport.setSupplementaryTerms( params.getSupplementaryTerms() );
        tbBusinessTransactionReport.setSignDate( params.getSignDate() );

        return tbBusinessTransactionReport;
    }

    @Override
    public List<TransactionReportResp> data2Resp(List<TbBusinessTransactionReport> tbBusinessTransactionReports) {
        if ( tbBusinessTransactionReports == null ) {
            return null;
        }

        List<TransactionReportResp> list = new ArrayList<TransactionReportResp>( tbBusinessTransactionReports.size() );
        for ( TbBusinessTransactionReport tbBusinessTransactionReport : tbBusinessTransactionReports ) {
            list.add( tbBusinessTransactionReportToTransactionReportResp( tbBusinessTransactionReport ) );
        }

        return list;
    }

    @Override
    public TbBusinessTransactionReport resp2Data(TransactionReportResp transactionReportResp) {
        if ( transactionReportResp == null ) {
            return null;
        }

        TbBusinessTransactionReport tbBusinessTransactionReport = new TbBusinessTransactionReport();

        tbBusinessTransactionReport.setRevision( transactionReportResp.getRevision() );
        tbBusinessTransactionReport.setCreateUser( transactionReportResp.getCreateUser() );
        tbBusinessTransactionReport.setCreateTime( transactionReportResp.getCreateTime() );
        tbBusinessTransactionReport.setUpdateUser( transactionReportResp.getUpdateUser() );
        tbBusinessTransactionReport.setUpdateTime( transactionReportResp.getUpdateTime() );
        tbBusinessTransactionReport.setIsDeleted( transactionReportResp.getIsDeleted() );
        tbBusinessTransactionReport.setId( transactionReportResp.getId() );
        tbBusinessTransactionReport.setReportCode( transactionReportResp.getReportCode() );
        tbBusinessTransactionReport.setTransactionContractNum( transactionReportResp.getTransactionContractNum() );
        tbBusinessTransactionReport.setCategory( transactionReportResp.getCategory() );
        tbBusinessTransactionReport.setCustomerId( transactionReportResp.getCustomerId() );
        tbBusinessTransactionReport.setHouseId( transactionReportResp.getHouseId() );
        tbBusinessTransactionReport.setOwnerInfo( transactionReportResp.getOwnerInfo() );
        tbBusinessTransactionReport.setDealDate( transactionReportResp.getDealDate() );
        tbBusinessTransactionReport.setDealAmount( transactionReportResp.getDealAmount() );
        tbBusinessTransactionReport.setStatus( transactionReportResp.getStatus() );
        tbBusinessTransactionReport.setBrokerId( transactionReportResp.getBrokerId() );
        tbBusinessTransactionReport.setOwnerCommission( transactionReportResp.getOwnerCommission() );
        tbBusinessTransactionReport.setCustomerCommission( transactionReportResp.getCustomerCommission() );
        tbBusinessTransactionReport.setLandCertificateNum( transactionReportResp.getLandCertificateNum() );
        tbBusinessTransactionReport.setPropertyRightNum( transactionReportResp.getPropertyRightNum() );
        tbBusinessTransactionReport.setWarrantHolder( transactionReportResp.getWarrantHolder() );
        tbBusinessTransactionReport.setDepositId( transactionReportResp.getDepositId() );
        tbBusinessTransactionReport.setOtherExpenses( transactionReportResp.getOtherExpenses() );
        tbBusinessTransactionReport.setSupplementaryTerms( transactionReportResp.getSupplementaryTerms() );
        tbBusinessTransactionReport.setSignDate( transactionReportResp.getSignDate() );
        tbBusinessTransactionReport.setCanDivideAchievement( transactionReportResp.getCanDivideAchievement() );
        tbBusinessTransactionReport.setRemark( transactionReportResp.getRemark() );

        return tbBusinessTransactionReport;
    }

    protected TransactionReportResp tbBusinessTransactionReportToTransactionReportResp(TbBusinessTransactionReport tbBusinessTransactionReport) {
        if ( tbBusinessTransactionReport == null ) {
            return null;
        }

        TransactionReportResp transactionReportResp = new TransactionReportResp();

        transactionReportResp.setRevision( tbBusinessTransactionReport.getRevision() );
        transactionReportResp.setCreateUser( tbBusinessTransactionReport.getCreateUser() );
        transactionReportResp.setCreateTime( tbBusinessTransactionReport.getCreateTime() );
        transactionReportResp.setUpdateUser( tbBusinessTransactionReport.getUpdateUser() );
        transactionReportResp.setUpdateTime( tbBusinessTransactionReport.getUpdateTime() );
        transactionReportResp.setIsDeleted( tbBusinessTransactionReport.getIsDeleted() );
        transactionReportResp.setId( tbBusinessTransactionReport.getId() );
        transactionReportResp.setReportCode( tbBusinessTransactionReport.getReportCode() );
        transactionReportResp.setTransactionContractNum( tbBusinessTransactionReport.getTransactionContractNum() );
        transactionReportResp.setCategory( tbBusinessTransactionReport.getCategory() );
        transactionReportResp.setCustomerId( tbBusinessTransactionReport.getCustomerId() );
        transactionReportResp.setHouseId( tbBusinessTransactionReport.getHouseId() );
        transactionReportResp.setOwnerInfo( tbBusinessTransactionReport.getOwnerInfo() );
        transactionReportResp.setDealDate( tbBusinessTransactionReport.getDealDate() );
        transactionReportResp.setDealAmount( tbBusinessTransactionReport.getDealAmount() );
        transactionReportResp.setStatus( tbBusinessTransactionReport.getStatus() );
        transactionReportResp.setBrokerId( tbBusinessTransactionReport.getBrokerId() );
        transactionReportResp.setOwnerCommission( tbBusinessTransactionReport.getOwnerCommission() );
        transactionReportResp.setCustomerCommission( tbBusinessTransactionReport.getCustomerCommission() );
        transactionReportResp.setLandCertificateNum( tbBusinessTransactionReport.getLandCertificateNum() );
        transactionReportResp.setPropertyRightNum( tbBusinessTransactionReport.getPropertyRightNum() );
        transactionReportResp.setWarrantHolder( tbBusinessTransactionReport.getWarrantHolder() );
        transactionReportResp.setDepositId( tbBusinessTransactionReport.getDepositId() );
        transactionReportResp.setOtherExpenses( tbBusinessTransactionReport.getOtherExpenses() );
        transactionReportResp.setSupplementaryTerms( tbBusinessTransactionReport.getSupplementaryTerms() );
        transactionReportResp.setSignDate( tbBusinessTransactionReport.getSignDate() );
        transactionReportResp.setCanDivideAchievement( tbBusinessTransactionReport.getCanDivideAchievement() );
        transactionReportResp.setRemark( tbBusinessTransactionReport.getRemark() );

        return transactionReportResp;
    }
}
