package mixc.be.rsms.business.convert;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.OrderAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessOrder;
import mixc.be.rsms.business.vo.HouseInfoVo;
import mixc.be.rsms.business.vo.OrderCommissionVo;
import mixc.be.rsms.business.vo.OrderReceiveVo;
import mixc.be.rsms.business.vo.OrderRefundVo;
import mixc.be.rsms.business.vo.OrderVo;
import mixc.be.rsms.pojo.business.OrderCommissionResp;
import mixc.be.rsms.pojo.business.OrderReceiveResp;
import mixc.be.rsms.pojo.business.OrderRefundResp;
import mixc.be.rsms.pojo.business.OrderResp;
import mixc.be.rsms.pojo.data.HouseInfoResp;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:43+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class DataOrderConvertImpl implements DataOrderConvert {

    @Override
    public OrderVo data2Vo(TbBusinessOrder order) {
        if ( order == null ) {
            return null;
        }

        OrderVo orderVo = new OrderVo();

        orderVo.setId( order.getId() );
        orderVo.setOrderCode( order.getOrderCode() );
        orderVo.setStatus( order.getStatus() );
        orderVo.setContractNum( order.getContractNum() );
        orderVo.setCustomerName( order.getCustomerName() );
        orderVo.setCustomerMobile( order.getCustomerMobile() );
        orderVo.setOrderDate( order.getOrderDate() );
        orderVo.setAmount( order.getAmount() );
        orderVo.setTransactionType( order.getTransactionType() );
        orderVo.setCreateUser( order.getCreateUser() );

        return orderVo;
    }

    @Override
    public OrderResp data2Resp(TbBusinessOrder order) {
        if ( order == null ) {
            return null;
        }

        OrderResp orderResp = new OrderResp();

        orderResp.setId( order.getId() );
        orderResp.setOrderCode( order.getOrderCode() );
        orderResp.setStatus( order.getStatus() );
        orderResp.setContractNum( order.getContractNum() );
        orderResp.setCustomerName( order.getCustomerName() );
        orderResp.setCustomerMobile( order.getCustomerMobile() );
        orderResp.setOrderDate( order.getOrderDate() );
        orderResp.setAmount( order.getAmount() );
        orderResp.setTransactionType( order.getTransactionType() );

        return orderResp;
    }

    @Override
    public OrderResp vo2Resp(OrderVo orderVo) {
        if ( orderVo == null ) {
            return null;
        }

        OrderResp orderResp = new OrderResp();

        orderResp.setId( orderVo.getId() );
        orderResp.setOrderCode( orderVo.getOrderCode() );
        orderResp.setStatus( orderVo.getStatus() );
        orderResp.setContractNum( orderVo.getContractNum() );
        orderResp.setDocumentNum( orderVo.getDocumentNum() );
        orderResp.setHouseInfo( houseInfoVoListToHouseInfoRespList( orderVo.getHouseInfo() ) );
        List<Object> list1 = orderVo.getParking();
        if ( list1 != null ) {
            orderResp.setParking( new ArrayList<Object>( list1 ) );
        }
        orderResp.setCustomerName( orderVo.getCustomerName() );
        orderResp.setCustomerMobile( orderVo.getCustomerMobile() );
        orderResp.setOrderDate( orderVo.getOrderDate() );
        orderResp.setAmount( orderVo.getAmount() );
        orderResp.setTransactionType( orderVo.getTransactionType() );
        orderResp.setReceiveOrder( orderReceiveVoListToOrderReceiveRespList( orderVo.getReceiveOrder() ) );
        orderResp.setRefundOrder( orderRefundVoListToOrderRefundRespList( orderVo.getRefundOrder() ) );
        orderResp.setCommissionOrder( orderCommissionVoListToOrderCommissionRespList( orderVo.getCommissionOrder() ) );
        List<String> list5 = orderVo.getPermissions();
        if ( list5 != null ) {
            orderResp.setPermissions( new ArrayList<String>( list5 ) );
        }

        return orderResp;
    }

    @Override
    public TbBusinessOrder add2Data(OrderAddParams params) {
        if ( params == null ) {
            return null;
        }

        TbBusinessOrder tbBusinessOrder = new TbBusinessOrder();

        tbBusinessOrder.setContractNum( params.getContractNum() );
        tbBusinessOrder.setHouseIds( params.getHouseIds() );
        tbBusinessOrder.setParkingIds( params.getParkingIds() );
        tbBusinessOrder.setCommunityIds( params.getCommunityIds() );
        tbBusinessOrder.setCustomerName( params.getCustomerName() );
        tbBusinessOrder.setCustomerMobile( params.getCustomerMobile() );
        tbBusinessOrder.setAmount( params.getAmount() );
        tbBusinessOrder.setTransactionType( params.getTransactionType() );
        tbBusinessOrder.setRemark( params.getRemark() );

        return tbBusinessOrder;
    }

    protected HouseInfoResp houseInfoVoToHouseInfoResp(HouseInfoVo houseInfoVo) {
        if ( houseInfoVo == null ) {
            return null;
        }

        HouseInfoResp.HouseInfoRespBuilder houseInfoResp = HouseInfoResp.builder();

        houseInfoResp.id( houseInfoVo.getId() );
        houseInfoResp.location( houseInfoVo.getLocation() );
        houseInfoResp.houseNumber( houseInfoVo.getHouseNumber() );
        houseInfoResp.building( houseInfoVo.getBuilding() );
        houseInfoResp.unit( houseInfoVo.getUnit() );
        houseInfoResp.floor( houseInfoVo.getFloor() );
        houseInfoResp.communityId( houseInfoVo.getCommunityId() );
        houseInfoResp.communityName( houseInfoVo.getCommunityName() );
        houseInfoResp.rentStatus( houseInfoVo.getRentStatus() );
        houseInfoResp.houseType( houseInfoVo.getHouseType() );
        houseInfoResp.sellPrice( houseInfoVo.getSellPrice() );
        houseInfoResp.rentPrice( houseInfoVo.getRentPrice() );
        houseInfoResp.layoutCode( houseInfoVo.getLayoutCode() );
        houseInfoResp.createUser( houseInfoVo.getCreateUser() );
        houseInfoResp.maintenanceBy( houseInfoVo.getMaintenanceBy() );
        houseInfoResp.openedBy( houseInfoVo.getOpenedBy() );
        houseInfoResp.picBy( houseInfoVo.getPicBy() );
        houseInfoResp.videoBy( houseInfoVo.getVideoBy() );
        houseInfoResp.keyBy( houseInfoVo.getKeyBy() );
        houseInfoResp.delegateBy( houseInfoVo.getDelegateBy() );
        houseInfoResp.dickBy( houseInfoVo.getDickBy() );
        houseInfoResp.concatUser( houseInfoVo.getConcatUser() );

        return houseInfoResp.build();
    }

    protected List<HouseInfoResp> houseInfoVoListToHouseInfoRespList(List<HouseInfoVo> list) {
        if ( list == null ) {
            return null;
        }

        List<HouseInfoResp> list1 = new ArrayList<HouseInfoResp>( list.size() );
        for ( HouseInfoVo houseInfoVo : list ) {
            list1.add( houseInfoVoToHouseInfoResp( houseInfoVo ) );
        }

        return list1;
    }

    protected OrderReceiveResp orderReceiveVoToOrderReceiveResp(OrderReceiveVo orderReceiveVo) {
        if ( orderReceiveVo == null ) {
            return null;
        }

        OrderReceiveResp orderReceiveResp = new OrderReceiveResp();

        orderReceiveResp.setId( orderReceiveVo.getId() );
        orderReceiveResp.setParentOrderId( orderReceiveVo.getParentOrderId() );
        orderReceiveResp.setSubOrderCode( orderReceiveVo.getSubOrderCode() );
        orderReceiveResp.setPayMode( orderReceiveVo.getPayMode() );
        orderReceiveResp.setReceiveCommunityId( orderReceiveVo.getReceiveCommunityId() );
        orderReceiveResp.setReceiveCommunityName( orderReceiveVo.getReceiveCommunityName() );
        orderReceiveResp.setOrderStatus( orderReceiveVo.getOrderStatus() );
        orderReceiveResp.setPayAmount( orderReceiveVo.getPayAmount() );
        orderReceiveResp.setGoodName( orderReceiveVo.getGoodName() );
        orderReceiveResp.setDiscountAmount( orderReceiveVo.getDiscountAmount() );
        orderReceiveResp.setPayChannel( orderReceiveVo.getPayChannel() );
        orderReceiveResp.setCreateTime( orderReceiveVo.getCreateTime() );

        return orderReceiveResp;
    }

    protected List<OrderReceiveResp> orderReceiveVoListToOrderReceiveRespList(List<OrderReceiveVo> list) {
        if ( list == null ) {
            return null;
        }

        List<OrderReceiveResp> list1 = new ArrayList<OrderReceiveResp>( list.size() );
        for ( OrderReceiveVo orderReceiveVo : list ) {
            list1.add( orderReceiveVoToOrderReceiveResp( orderReceiveVo ) );
        }

        return list1;
    }

    protected OrderRefundResp orderRefundVoToOrderRefundResp(OrderRefundVo orderRefundVo) {
        if ( orderRefundVo == null ) {
            return null;
        }

        OrderRefundResp orderRefundResp = new OrderRefundResp();

        orderRefundResp.setId( orderRefundVo.getId() );
        orderRefundResp.setParentOrderId( orderRefundVo.getParentOrderId() );
        orderRefundResp.setSubOrderCode( orderRefundVo.getSubOrderCode() );
        orderRefundResp.setReceiveSubOrderId( orderRefundVo.getReceiveSubOrderId() );
        orderRefundResp.setAmount( orderRefundVo.getAmount() );
        orderRefundResp.setRefundChannel( orderRefundVo.getRefundChannel() );
        orderRefundResp.setBankNum( orderRefundVo.getBankNum() );
        orderRefundResp.setAccountName( orderRefundVo.getAccountName() );
        orderRefundResp.setBankName( orderRefundVo.getBankName() );
        orderRefundResp.setCity( orderRefundVo.getCity() );
        orderRefundResp.setOrderStatus( orderRefundVo.getOrderStatus() );
        orderRefundResp.setCreateTime( orderRefundVo.getCreateTime() );

        return orderRefundResp;
    }

    protected List<OrderRefundResp> orderRefundVoListToOrderRefundRespList(List<OrderRefundVo> list) {
        if ( list == null ) {
            return null;
        }

        List<OrderRefundResp> list1 = new ArrayList<OrderRefundResp>( list.size() );
        for ( OrderRefundVo orderRefundVo : list ) {
            list1.add( orderRefundVoToOrderRefundResp( orderRefundVo ) );
        }

        return list1;
    }

    protected OrderCommissionResp orderCommissionVoToOrderCommissionResp(OrderCommissionVo orderCommissionVo) {
        if ( orderCommissionVo == null ) {
            return null;
        }

        OrderCommissionResp orderCommissionResp = new OrderCommissionResp();

        orderCommissionResp.setId( orderCommissionVo.getId() );
        orderCommissionResp.setParentOrderId( orderCommissionVo.getParentOrderId() );
        orderCommissionResp.setSubOrderCode( orderCommissionVo.getSubOrderCode() );
        orderCommissionResp.setExportAmount( orderCommissionVo.getExportAmount() );
        orderCommissionResp.setImportAmount( orderCommissionVo.getImportAmount() );
        orderCommissionResp.setOrderStatus( orderCommissionVo.getOrderStatus() );
        orderCommissionResp.setCreateTime( orderCommissionVo.getCreateTime() );

        return orderCommissionResp;
    }

    protected List<OrderCommissionResp> orderCommissionVoListToOrderCommissionRespList(List<OrderCommissionVo> list) {
        if ( list == null ) {
            return null;
        }

        List<OrderCommissionResp> list1 = new ArrayList<OrderCommissionResp>( list.size() );
        for ( OrderCommissionVo orderCommissionVo : list ) {
            list1.add( orderCommissionVoToOrderCommissionResp( orderCommissionVo ) );
        }

        return list1;
    }
}
