package mixc.be.rsms.business.convert;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.SubOrderReceiveAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessSubOrderReceive;
import mixc.be.rsms.business.vo.OrderReceiveVo;
import mixc.be.rsms.pojo.business.OrderReceiveResp;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:43+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class DataSubOrderReceiveConvertImpl implements DataSubOrderReceiveConvert {

    @Override
    public OrderReceiveVo data2Vo(TbBusinessSubOrderReceive orderReceive) {
        if ( orderReceive == null ) {
            return null;
        }

        OrderReceiveVo orderReceiveVo = new OrderReceiveVo();

        orderReceiveVo.setId( orderReceive.getId() );
        orderReceiveVo.setParentOrderId( orderReceive.getParentOrderId() );
        orderReceiveVo.setSubOrderCode( orderReceive.getSubOrderCode() );
        orderReceiveVo.setPayMode( orderReceive.getPayMode() );
        orderReceiveVo.setReceiveCommunityId( orderReceive.getReceiveCommunityId() );
        orderReceiveVo.setReceiveCommunityName( orderReceive.getReceiveCommunityName() );
        orderReceiveVo.setOrderStatus( orderReceive.getOrderStatus() );
        orderReceiveVo.setPayAmount( orderReceive.getPayAmount() );
        orderReceiveVo.setGoodName( orderReceive.getGoodName() );
        orderReceiveVo.setDiscountAmount( orderReceive.getDiscountAmount() );
        orderReceiveVo.setPayChannel( orderReceive.getPayChannel() );
        orderReceiveVo.setBusinessId( orderReceive.getBusinessId() );
        orderReceiveVo.setCreateTime( orderReceive.getCreateTime() );
        orderReceiveVo.setCreateUser( orderReceive.getCreateUser() );

        return orderReceiveVo;
    }

    @Override
    public OrderReceiveResp data2Resp(TbBusinessSubOrderReceive orderReceive) {
        if ( orderReceive == null ) {
            return null;
        }

        OrderReceiveResp orderReceiveResp = new OrderReceiveResp();

        orderReceiveResp.setId( orderReceive.getId() );
        orderReceiveResp.setParentOrderId( orderReceive.getParentOrderId() );
        orderReceiveResp.setSubOrderCode( orderReceive.getSubOrderCode() );
        orderReceiveResp.setPayMode( orderReceive.getPayMode() );
        orderReceiveResp.setReceiveCommunityId( orderReceive.getReceiveCommunityId() );
        orderReceiveResp.setReceiveCommunityName( orderReceive.getReceiveCommunityName() );
        orderReceiveResp.setOrderStatus( orderReceive.getOrderStatus() );
        orderReceiveResp.setPayAmount( orderReceive.getPayAmount() );
        orderReceiveResp.setGoodName( orderReceive.getGoodName() );
        orderReceiveResp.setDiscountAmount( orderReceive.getDiscountAmount() );
        orderReceiveResp.setPayChannel( orderReceive.getPayChannel() );
        orderReceiveResp.setCreateTime( orderReceive.getCreateTime() );

        return orderReceiveResp;
    }

    @Override
    public TbBusinessSubOrderReceive add2Data(SubOrderReceiveAddParams params) {
        if ( params == null ) {
            return null;
        }

        TbBusinessSubOrderReceive tbBusinessSubOrderReceive = new TbBusinessSubOrderReceive();

        tbBusinessSubOrderReceive.setPayMode( params.getPayMode() );
        tbBusinessSubOrderReceive.setReceiveCommunityId( params.getReceiveCommunityId() );
        tbBusinessSubOrderReceive.setReceiveCommunityName( params.getReceiveCommunityName() );
        tbBusinessSubOrderReceive.setPayAmount( params.getPayAmount() );
        tbBusinessSubOrderReceive.setGoodName( params.getGoodName() );
        tbBusinessSubOrderReceive.setDiscountAmount( params.getDiscountAmount() );
        tbBusinessSubOrderReceive.setPayChannel( params.getPayChannel() );
        tbBusinessSubOrderReceive.setRemark( params.getRemark() );
        tbBusinessSubOrderReceive.setBusinessId( params.getBusinessId() );

        return tbBusinessSubOrderReceive;
    }

    @Override
    public List<OrderReceiveResp> vo2RespList(List<OrderReceiveVo> orderReceiveVos) {
        if ( orderReceiveVos == null ) {
            return null;
        }

        List<OrderReceiveResp> list = new ArrayList<OrderReceiveResp>( orderReceiveVos.size() );
        for ( OrderReceiveVo orderReceiveVo : orderReceiveVos ) {
            list.add( orderReceiveVoToOrderReceiveResp( orderReceiveVo ) );
        }

        return list;
    }

    protected OrderReceiveResp orderReceiveVoToOrderReceiveResp(OrderReceiveVo orderReceiveVo) {
        if ( orderReceiveVo == null ) {
            return null;
        }

        OrderReceiveResp orderReceiveResp = new OrderReceiveResp();

        orderReceiveResp.setId( orderReceiveVo.getId() );
        orderReceiveResp.setParentOrderId( orderReceiveVo.getParentOrderId() );
        orderReceiveResp.setSubOrderCode( orderReceiveVo.getSubOrderCode() );
        orderReceiveResp.setPayMode( orderReceiveVo.getPayMode() );
        orderReceiveResp.setReceiveCommunityId( orderReceiveVo.getReceiveCommunityId() );
        orderReceiveResp.setReceiveCommunityName( orderReceiveVo.getReceiveCommunityName() );
        orderReceiveResp.setOrderStatus( orderReceiveVo.getOrderStatus() );
        orderReceiveResp.setPayAmount( orderReceiveVo.getPayAmount() );
        orderReceiveResp.setGoodName( orderReceiveVo.getGoodName() );
        orderReceiveResp.setDiscountAmount( orderReceiveVo.getDiscountAmount() );
        orderReceiveResp.setPayChannel( orderReceiveVo.getPayChannel() );
        orderReceiveResp.setCreateTime( orderReceiveVo.getCreateTime() );

        return orderReceiveResp;
    }
}
