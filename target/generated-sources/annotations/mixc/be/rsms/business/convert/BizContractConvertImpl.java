package mixc.be.rsms.business.convert;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.ContractSignAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessContract;
import mixc.be.rsms.business.vo.ContractDetailVo;
import mixc.be.rsms.business.vo.HouseInfoVo;
import mixc.be.rsms.pojo.mobile.ContractResp;
import mixc.be.rsms.pojo.mobile.CustomerAssetDetailResp;
import mixc.be.rsms.pojo.mobile.CustomerAssetResp;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:43+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class BizContractConvertImpl implements BizContractConvert {

    @Override
    public TbBusinessContract convert(ContractSignAddParams params) {
        if ( params == null ) {
            return null;
        }

        TbBusinessContract tbBusinessContract = new TbBusinessContract();

        tbBusinessContract.setId( params.getId() );
        tbBusinessContract.setContractNumber( params.getContractNumber() );
        tbBusinessContract.setContractType( params.getContractType() );
        tbBusinessContract.setHouseId( params.getHouseId() );
        tbBusinessContract.setIntermediaryCompanyId( params.getIntermediaryCompanyId() );
        tbBusinessContract.setLeaserUserName( params.getLeaserUserName() );
        tbBusinessContract.setLeaserMobile( params.getLeaserMobile() );
        tbBusinessContract.setLeaserIdNumber( params.getLeaserIdNumber() );
        tbBusinessContract.setLeaserAddress( params.getLeaserAddress() );
        tbBusinessContract.setCustomerId( params.getCustomerId() );
        tbBusinessContract.setCustomerMobile( params.getCustomerMobile() );
        tbBusinessContract.setCustomerIdNumber( params.getCustomerIdNumber() );
        tbBusinessContract.setCustomerAddress( params.getCustomerAddress() );
        if ( params.getContractSignDate() != null ) {
            tbBusinessContract.setContractSignDate( params.getContractSignDate().atStartOfDay() );
        }
        tbBusinessContract.setTransactionPrice( params.getTransactionPrice() );
        tbBusinessContract.setLeaserCommission( params.getLeaserCommission() );
        tbBusinessContract.setCustomerCommission( params.getCustomerCommission() );

        return tbBusinessContract;
    }

    @Override
    public ContractDetailVo convertVo(TbBusinessContract bizContract) {
        if ( bizContract == null ) {
            return null;
        }

        ContractDetailVo contractDetailVo = new ContractDetailVo();

        contractDetailVo.setContractType( bizContract.getContractType() );
        contractDetailVo.setJoyContractId( bizContract.getJoyContractId() );
        contractDetailVo.setContractNumber( bizContract.getContractNumber() );
        contractDetailVo.setIntermediaryCompanyId( bizContract.getIntermediaryCompanyId() );
        contractDetailVo.setContractStatus( bizContract.getContractStatus() );
        contractDetailVo.setHouseId( bizContract.getHouseId() );
        contractDetailVo.setCustomerId( bizContract.getCustomerId() );
        contractDetailVo.setCustomerMobile( bizContract.getCustomerMobile() );
        contractDetailVo.setCustomerIdNumber( bizContract.getCustomerIdNumber() );
        contractDetailVo.setCustomerAddress( bizContract.getCustomerAddress() );
        contractDetailVo.setLeaserUserName( bizContract.getLeaserUserName() );
        contractDetailVo.setLeaserMobile( bizContract.getLeaserMobile() );
        contractDetailVo.setLeaserIdNumber( bizContract.getLeaserIdNumber() );
        contractDetailVo.setLeaserAddress( bizContract.getLeaserAddress() );
        contractDetailVo.setContractSignDate( bizContract.getContractSignDate() );
        contractDetailVo.setTransactionPrice( bizContract.getTransactionPrice() );
        contractDetailVo.setLeaserCommission( bizContract.getLeaserCommission() );
        contractDetailVo.setCustomerCommission( bizContract.getCustomerCommission() );
        contractDetailVo.setCreateUser( bizContract.getCreateUser() );

        return contractDetailVo;
    }

    @Override
    public CustomerAssetResp contract2Asset(TbBusinessContract bizContract) {
        if ( bizContract == null ) {
            return null;
        }

        CustomerAssetResp customerAssetResp = new CustomerAssetResp();

        customerAssetResp.setCustomerId( bizContract.getCustomerId() );

        return customerAssetResp;
    }

    @Override
    public CustomerAssetDetailResp houseInfo2AssetDetail(HouseInfoVo houseInfoVo) {
        if ( houseInfoVo == null ) {
            return null;
        }

        CustomerAssetDetailResp customerAssetDetailResp = new CustomerAssetDetailResp();

        customerAssetDetailResp.setCommunityId( houseInfoVo.getCommunityId() );
        customerAssetDetailResp.setCommunityName( houseInfoVo.getCommunityName() );
        customerAssetDetailResp.setLocation( houseInfoVo.getLocation() );
        customerAssetDetailResp.setHouseNumber( houseInfoVo.getHouseNumber() );
        customerAssetDetailResp.setLayoutCode( houseInfoVo.getLayoutCode() );
        customerAssetDetailResp.setArea( houseInfoVo.getArea() );

        return customerAssetDetailResp;
    }

    @Override
    public List<ContractResp> bizContractList2ConractRespList(List<TbBusinessContract> bizContractList) {
        if ( bizContractList == null ) {
            return null;
        }

        List<ContractResp> list = new ArrayList<ContractResp>( bizContractList.size() );
        for ( TbBusinessContract tbBusinessContract : bizContractList ) {
            list.add( tbBusinessContractToContractResp( tbBusinessContract ) );
        }

        return list;
    }

    protected ContractResp tbBusinessContractToContractResp(TbBusinessContract tbBusinessContract) {
        if ( tbBusinessContract == null ) {
            return null;
        }

        ContractResp contractResp = new ContractResp();

        contractResp.setId( tbBusinessContract.getId() );
        contractResp.setJoyContractId( tbBusinessContract.getJoyContractId() );
        contractResp.setContractType( tbBusinessContract.getContractType() );
        contractResp.setContractNumber( tbBusinessContract.getContractNumber() );
        contractResp.setContractStatus( tbBusinessContract.getContractStatus() );
        contractResp.setCustomerId( tbBusinessContract.getCustomerId() );
        contractResp.setCustomerMobile( tbBusinessContract.getCustomerMobile() );
        contractResp.setLeaserUserName( tbBusinessContract.getLeaserUserName() );
        contractResp.setLeaserMobile( tbBusinessContract.getLeaserMobile() );
        if ( tbBusinessContract.getContractSignDate() != null ) {
            contractResp.setContractSignDate( tbBusinessContract.getContractSignDate().toLocalDate() );
        }
        contractResp.setTransactionPrice( tbBusinessContract.getTransactionPrice() );

        return contractResp;
    }
}
