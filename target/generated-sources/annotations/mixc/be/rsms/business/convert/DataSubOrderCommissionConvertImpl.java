package mixc.be.rsms.business.convert;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.SubOrderCommissionAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessSubOrderCommission;
import mixc.be.rsms.business.vo.OrderCommissionVo;
import mixc.be.rsms.pojo.business.OrderCommissionResp;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:43+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class DataSubOrderCommissionConvertImpl implements DataSubOrderCommissionConvert {

    @Override
    public OrderCommissionVo data2Vo(TbBusinessSubOrderCommission orderCommission) {
        if ( orderCommission == null ) {
            return null;
        }

        OrderCommissionVo orderCommissionVo = new OrderCommissionVo();

        orderCommissionVo.setId( orderCommission.getId() );
        orderCommissionVo.setParentOrderId( orderCommission.getParentOrderId() );
        orderCommissionVo.setSubOrderCode( orderCommission.getSubOrderCode() );
        orderCommissionVo.setExportAmount( orderCommission.getExportAmount() );
        orderCommissionVo.setOrderStatus( orderCommission.getOrderStatus() );
        orderCommissionVo.setBusinessId( orderCommission.getBusinessId() );
        orderCommissionVo.setCreateTime( orderCommission.getCreateTime() );
        orderCommissionVo.setCreateUser( orderCommission.getCreateUser() );

        return orderCommissionVo;
    }

    @Override
    public OrderCommissionResp data2Resp(TbBusinessSubOrderCommission orderCommission) {
        if ( orderCommission == null ) {
            return null;
        }

        OrderCommissionResp orderCommissionResp = new OrderCommissionResp();

        orderCommissionResp.setId( orderCommission.getId() );
        orderCommissionResp.setParentOrderId( orderCommission.getParentOrderId() );
        orderCommissionResp.setSubOrderCode( orderCommission.getSubOrderCode() );
        orderCommissionResp.setExportSubOrderId( orderCommission.getExportSubOrderId() );
        orderCommissionResp.setExportSubOrderCode( orderCommission.getExportSubOrderCode() );
        orderCommissionResp.setExportAmount( orderCommission.getExportAmount() );
        orderCommissionResp.setOrderStatus( orderCommission.getOrderStatus() );
        orderCommissionResp.setCreateTime( orderCommission.getCreateTime() );

        return orderCommissionResp;
    }

    @Override
    public TbBusinessSubOrderCommission add2Data(SubOrderCommissionAddParams params) {
        if ( params == null ) {
            return null;
        }

        TbBusinessSubOrderCommission tbBusinessSubOrderCommission = new TbBusinessSubOrderCommission();

        tbBusinessSubOrderCommission.setExportSubOrderId( params.getExportSubOrderId() );
        tbBusinessSubOrderCommission.setExportSubOrderCode( params.getExportSubOrderCode() );
        tbBusinessSubOrderCommission.setExportAmount( params.getExportAmount() );
        tbBusinessSubOrderCommission.setRemark( params.getRemark() );
        tbBusinessSubOrderCommission.setBusinessId( params.getBusinessId() );

        return tbBusinessSubOrderCommission;
    }

    @Override
    public List<OrderCommissionResp> vo2RespList(List<OrderCommissionVo> orderCommissionVos) {
        if ( orderCommissionVos == null ) {
            return null;
        }

        List<OrderCommissionResp> list = new ArrayList<OrderCommissionResp>( orderCommissionVos.size() );
        for ( OrderCommissionVo orderCommissionVo : orderCommissionVos ) {
            list.add( orderCommissionVoToOrderCommissionResp( orderCommissionVo ) );
        }

        return list;
    }

    protected OrderCommissionResp orderCommissionVoToOrderCommissionResp(OrderCommissionVo orderCommissionVo) {
        if ( orderCommissionVo == null ) {
            return null;
        }

        OrderCommissionResp orderCommissionResp = new OrderCommissionResp();

        orderCommissionResp.setId( orderCommissionVo.getId() );
        orderCommissionResp.setParentOrderId( orderCommissionVo.getParentOrderId() );
        orderCommissionResp.setSubOrderCode( orderCommissionVo.getSubOrderCode() );
        orderCommissionResp.setExportAmount( orderCommissionVo.getExportAmount() );
        orderCommissionResp.setImportAmount( orderCommissionVo.getImportAmount() );
        orderCommissionResp.setOrderStatus( orderCommissionVo.getOrderStatus() );
        orderCommissionResp.setCreateTime( orderCommissionVo.getCreateTime() );

        return orderCommissionResp;
    }
}
