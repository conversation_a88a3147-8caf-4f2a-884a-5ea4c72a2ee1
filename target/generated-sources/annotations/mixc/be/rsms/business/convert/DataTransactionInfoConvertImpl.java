package mixc.be.rsms.business.convert;

import javax.annotation.processing.Generated;
import mixc.be.rsms.business.domain.dto.TransactionInfoAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionInfo;
import mixc.be.rsms.business.vo.TransactionInfoVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-01-10T16:25:42+0800",
    comments = "version: 1.6.0, compiler: javac, environment: Java 21.0.4 (Azul Systems, Inc.)"
)
@Component
public class DataTransactionInfoConvertImpl implements DataTransactionInfoConvert {

    @Override
    public TransactionInfoVo dataToVo(TbBusinessTransactionInfo transactionInfo) {
        if ( transactionInfo == null ) {
            return null;
        }

        TransactionInfoVo transactionInfoVo = new TransactionInfoVo();

        transactionInfoVo.setId( transactionInfo.getId() );
        transactionInfoVo.setTransactionDateTime( transactionInfo.getTransactionDateTime() );
        transactionInfoVo.setTransactionType( transactionInfo.getTransactionType() );
        transactionInfoVo.setTransactionAmount( transactionInfo.getTransactionAmount() );
        transactionInfoVo.setContractNum( transactionInfo.getContractNum() );
        transactionInfoVo.setRemark( transactionInfo.getRemark() );
        transactionInfoVo.setStatus( transactionInfo.getStatus() );
        transactionInfoVo.setOrderStatus( transactionInfo.getOrderStatus() );
        transactionInfoVo.setSubOrderId( transactionInfo.getSubOrderId() );
        transactionInfoVo.setSubOrderCode( transactionInfo.getSubOrderCode() );

        return transactionInfoVo;
    }

    @Override
    public TbBusinessTransactionInfo addParamToData(TransactionInfoAddParams transactionInfoAddParams) {
        if ( transactionInfoAddParams == null ) {
            return null;
        }

        TbBusinessTransactionInfo tbBusinessTransactionInfo = new TbBusinessTransactionInfo();

        tbBusinessTransactionInfo.setId( transactionInfoAddParams.getId() );
        tbBusinessTransactionInfo.setDepositId( transactionInfoAddParams.getDepositId() );
        tbBusinessTransactionInfo.setTransactionType( transactionInfoAddParams.getTransactionType() );
        tbBusinessTransactionInfo.setTransactionAmount( transactionInfoAddParams.getTransactionAmount() );
        tbBusinessTransactionInfo.setContractNum( transactionInfoAddParams.getContractNum() );
        tbBusinessTransactionInfo.setRemark( transactionInfoAddParams.getRemark() );
        tbBusinessTransactionInfo.setStatus( transactionInfoAddParams.getStatus() );
        tbBusinessTransactionInfo.setOrderStatus( transactionInfoAddParams.getOrderStatus() );
        tbBusinessTransactionInfo.setSubOrderId( transactionInfoAddParams.getSubOrderId() );
        tbBusinessTransactionInfo.setSubOrderCode( transactionInfoAddParams.getSubOrderCode() );

        return tbBusinessTransactionInfo;
    }
}
