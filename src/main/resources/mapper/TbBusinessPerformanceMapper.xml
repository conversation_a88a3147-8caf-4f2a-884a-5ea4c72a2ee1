<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessPerformanceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessPerformance">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="transaction_id" jdbcType="BIGINT" property="transactionId" />
        <result column="performance_code" jdbcType="VARCHAR" property="performanceCode" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="store_id" jdbcType="BIGINT" property="storeId" />
        <result column="performance_period" jdbcType="VARCHAR" property="performancePeriod" />
        <result column="performance_type" jdbcType="VARCHAR" property="performanceType" />
        <result column="performance_stage" jdbcType="VARCHAR" property="performanceStage" />
        <result column="performance_time" jdbcType="TIMESTAMP" property="performanceTime" />
        <result column="revision" jdbcType="INTEGER" property="revision" />
        <result column="create_user" jdbcType="BIGINT" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user" jdbcType="BIGINT" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, transaction_id, performance_code, user_id, store_id, performance_period, performance_type,
        performance_stage, performance_time,
        revision, create_user, create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
