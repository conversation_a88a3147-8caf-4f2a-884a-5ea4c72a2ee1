<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessOrderLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessOrderLog">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="sub_order_id" property="subOrderId" />
        <result column="order_code" property="orderCode" />
        <result column="sub_order_code" property="subOrderCode" />
        <result column="req_body" property="reqBody" />
        <result column="req_time" property="reqTime" />
        <result column="resp_body" property="respBody" />
        <result column="resp_time" property="respTime" />
        <result column="resp_status" property="respStatus" />
        <result column="retry" property="retry" />
        <result column="remark" property="remark"/>
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, sub_order_id, order_code, sub_order_code, req_body, req_time, resp_body, resp_time, resp_status, retry,  remark, revision, create_user,
        create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
