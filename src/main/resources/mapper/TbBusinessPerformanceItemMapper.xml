<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessPerformanceItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessPerformanceItem">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="performance_id" jdbcType="BIGINT" property="performanceId" />
        <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
        <result column="receivable_excluding_tax_amount" jdbcType="DECIMAL" property="receivableExcludingTaxAmount" />
        <result column="real_time_excluding_tax_amount" jdbcType="DECIMAL" property="realTimeExcludingTaxAmount" />
        <result column="revision" jdbcType="INTEGER" property="revision" />
        <result column="create_user" jdbcType="BIGINT" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user" jdbcType="BIGINT" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, performance_id, tax_rate, receivable_excluding_tax_amount, real_time_excluding_tax_amount,
        revision, create_user, create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
