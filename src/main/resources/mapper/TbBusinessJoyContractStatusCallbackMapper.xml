<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessJoyContractStatusCallbackMapper">
  <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessJoyContractStatusCallback">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="joy_contract_id" jdbcType="VARCHAR" property="joyContractId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="body" jdbcType="VARCHAR" property="body" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="create_user" property="createUser" />
    <result column="create_time" property="createTime" />
    <result column="update_user" property="updateUser" />
    <result column="update_time" property="updateTime" />
    <result column="is_deleted" property="isDeleted" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, contract_number, contract_type, contract_status, house_id, intermediary_company_id, 
    leaser_id, leaser_user_name, leaser_mobile, leaser_id_number, leaser_address,
    tenant_id, tenant_mobile, tenant_id_number, tenant_address, contract_sign_date, 
    transaction_price, leaser_commission, customer_commission, revision, create_user,
    create_time, update_user, update_time, is_deleted
  </sql>
  
</mapper>