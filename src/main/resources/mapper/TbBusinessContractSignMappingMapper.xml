<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessContractSignMappingMapper">
  <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessContractSignMapping">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="contract_type" jdbcType="VARCHAR" property="contractType" />
    <result column="template_id" jdbcType="INTEGER" property="templateId" />
    <result column="template_code" jdbcType="VARCHAR" property="templateCode" />
    <result column="scope" jdbcType="VARCHAR" property="scope" />
    <result column="transaction_fee_type" jdbcType="VARCHAR" property="transactionFeeType" />
    <result column="transaction_fee_type_code" jdbcType="VARCHAR" property="transactionFeeTypeCode" />
    <result column="transaction_category" jdbcType="VARCHAR" property="transactionCategory" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="create_user" property="createUser" />
    <result column="create_time" property="createTime" />
    <result column="update_user" property="updateUser" />
    <result column="update_time" property="updateTime" />
    <result column="is_deleted" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, city_id, business_type, contract_type, 
    template_id, template_code, scope, transaction_fee_type, transaction_fee_type_code,
    transaction_category, remarks,
    revision, create_user,
    create_time, update_user, update_time, is_deleted
  </sql>

  
  
</mapper>