<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessOwnershipTransferMapper">
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessOwnershipTransfer">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="transfer_contract_number" jdbcType="VARCHAR" property="transferContractNumber"/>
        <result column="transaction_report_id" jdbcType="BIGINT" property="transactionReportId"/>
        <result column="transfer_status" jdbcType="VARCHAR" property="transferStatus"/>
        <result column="house_id" jdbcType="BIGINT" property="houseId"/>
        <result column="customer_id" jdbcType="BIGINT" property="customerId"/>
        <result column="parking_space_id" property="parkingSpaceId"/>
        <result column="building" property="building" />
        <result column="unit" property="unit" />
        <result column="house_number" property="houseNumber" />
        <result column="new_house_community_id" property="newHouseCommunityId" />
        <result column="current_transfer_stage" jdbcType="VARCHAR" property="currentTransferStage"/>
        <result column="next_transfer_stage" jdbcType="VARCHAR" property="nextTransferStage"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , transfer_contract_number, transaction_report_id, transfer_status, house_id, customer_id,
        parking_space_id, building, unit, house_number, new_house_community_id,
    current_transfer_stage, next_transfer_stage,
    remarks, create_user,
    create_time, update_user, update_time, is_deleted
    </sql>

    <!-- 权证过户分页查询 -->
    <select id="pageOwnershipTransfer" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        tb_business_ownership_transfer tdof
        WHERE
        tdof.is_deleted = 0
        <if test="userIdList != null and userIdList.size > 0">
            AND tdof.create_user IN
            <foreach collection="userIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.transferContractNumber != null and params.transferContractNumber != ''">
            AND tdof.transfer_contract_number like concat('%', #{params.transferContractNumber}, '%')
        </if>
        <if test="keyWordHouseIds != null and keyWordHouseIds.size() > 0">
            AND
            (
            tdof.house_id in
            <foreach collection="keyWordHouseIds" item="keyWordHouseId" open="(" close=")" separator=",">
                #{keyWordHouseId}
            </foreach>
            )
        </if>
        <if test="customerIds != null and customerIds.size() > 0">
            AND
            (
            tdof.customer_id in
            <foreach collection="customerIds" item="customerId" close=")" open="(" separator=",">
                #{customerId}
            </foreach>
            )
        </if>
        <if test="params.transferStatus != null and params.transferStatus != ''">
            AND tdof.transfer_status = #{params.transferStatus}
        </if>
        <if test="params.currentTransferStage != null and params.currentTransferStage != ''">
            AND tdof.current_transfer_stage = #{params.currentTransferStage}
        </if>
        ORDER BY
        tdof.create_time DESC
    </select>

</mapper>