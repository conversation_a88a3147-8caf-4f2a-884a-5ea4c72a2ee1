<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessTransactionReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessTransactionReport">
        <result column="report_code" property="reportCode" />
        <result column="transaction_contract_num" property="transactionContractNum" />
        <result column="category" property="category" />
        <result column="customer_id" property="customerId" />
        <result column="house_id" property="houseId" />
        <result column="parking_space_id" property="parkingSpaceId" />
        <result column="owner_info" property="ownerInfo" />
        <result column="deal_date" property="dealDate" />
        <result column="deal_amount" property="dealAmount" />
        <result column="status" property="status" />
        <result column="broker_id" property="brokerId" />
        <result column="owner_commission" property="ownerCommission" />
        <result column="customer_commission" property="customerCommission" />
        <result column="marketing_fee" property="marketingFee" />
        <result column="land_certificate_num" property="landCertificateNum" />
        <result column="property_right_num" property="propertyRightNum" />
        <result column="warrant_holder" property="warrantHolder" />
        <result column="deposit_id" property="depositId" />
        <result column="other_expenses" property="otherExpenses" />
        <result column="supplementary_terms" property="supplementaryTerms" />
        <result column="sign_date" property="signDate" />
        <result column="building" property="building" />
        <result column="unit" property="unit" />
        <result column="house_number" property="houseNumber" />
        <result column="new_house_community_id" property="newHouseCommunityId" />
        <result column="status_sort" property="statusSort" />
        <result column="await_commission" property="awaitCommission" />
        <result column="collect_all_date" property="collectAllDate" />
        <result column="can_divide_achievement" property="canDivideAchievement" />
        <result column="commission_settle_status" property="commissionSettleStatus" />
        <result column="contract_per_settle_status" property="contractPerSettleStatus" />
        <result column="actual_per_settle_status" property="actualPerSettleStatus" />
        <result column="accounting_per_settle_status" property="accountingPerSettleStatus" />
        <result column="review_date_time" property="reviewDateTime" />
        <result column="remark" property="remark" />
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, report_code, transaction_contract_num, category, customer_id, house_id, parking_space_id, owner_info, deal_date, deal_amount, status, broker_id, owner_commission, customer_commission,
        marketing_fee,
          land_certificate_num, property_right_num, warrant_holder, deposit_id, other_expenses, supplementary_terms,
        sign_date, building, unit, house_number, new_house_community_id, can_divide_achievement,
          commission_settle_status,contract_per_settle_status,actual_per_settle_status,accounting_per_settle_status,review_date_time
          remark, revision, create_user,
        create_time, update_user, update_time, is_deleted
    </sql>

    <select id="selectInfoOnOperateLog" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_business_transaction_report
        where 1 = 1
        <if test="businessIdList != null" >
            and
            <foreach collection="businessIdList" close=")" open=" id in (" item="businessId" separator=",">
                #{businessId}
            </foreach>
        </if>
    </select>

    <select id="selectByEntity" parameterType="mixc.be.rsms.business.domain.dto.TransactionReportQueryParams" resultMap="BaseResultMap">
        select c.*,
                   case
                        when c.status = 'reviewed' and c.await_commission = 0 then 100
                       when c.status = 'reviewing' and c.await_commission != 0 then 10
                       when c.status = 'auditing' then 8
                       when c.status = 'reviewed' then 6
                       when c.status = 'unaudit' then 4
                       when c.status = 'closed' then 2
                       else 0
                    end as status_sort
       from (
                select
                    a.id,
                    a.report_code,
                    a.transaction_contract_num,
                    a.status,
                    a.deposit_id,
                    a.category,
                    a.customer_id,
                    a.house_id,
                    a.parking_space_id,
                    a.deal_date,
                    a.broker_id,
                    a.deal_amount,
                    a.owner_commission,
                    a.customer_commission,
                    a.building,
                    a.unit,
                    a.house_number,
                    a.new_house_community_id,
                    a.create_user,
                    a.create_time,
                    sum(ifnull(b.receivable_commission,0)) - sum(ifnull(b.received_commission,0)) as await_commission,
                    a.sign_date,
                    a.collect_all_date
                from tb_business_transaction_report as a
                left join tb_business_receive_commission_plan b on a.id = b.report_id
                left join tb_business_deposit c on c.id = a.deposit_id
                left join tb_business_order tbo on tbo.id = b.order_id
                where a.is_deleted = 0 and (b.is_deleted = 0 or b.is_deleted is null) and (tbo.is_deleted = 0 or tbo.is_deleted is null)
                    <if test="dto.reportCode != null">
                        and a.report_code like concat('%',concat(#{dto.reportCode},'%'))
                    </if>
                    <if test="dto.transactionContractNum != null">
                        and a.transaction_contract_num like concat('%',concat(#{dto.transactionContractNum},'%'))
                    </if>
                    <if test="dto.status != null">
                        and a.status = #{dto.status}
                    </if>
                    <if test="dto.depositCode !=null">
                        and c.deposit_code like concat('%',concat(#{dto.depositCode},'%'))
                    </if>
                    <if test="dto.category != null">
                        and a.category = #{dto.category}
                    </if>
                    <if test="dto.customerIdList != null">
                        and (
                        a.customer_id in
                        <foreach collection="dto.customerIdList" index="index" item="item" open="(" close=")">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 500 == 0"> ) OR a.customer_id in ( </when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="dto.houseIdList != null">
                        and (
                        a.house_id in
                        <foreach collection="dto.houseIdList" index="index" item="item" open="(" close=")">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 500 == 0"> ) OR a.house_id in ( </when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="dto.parkingSpaceIdList != null">
                        and (
                        a.parking_space_id in
                        <foreach collection="dto.parkingSpaceIdList" index="index" item="item" open="(" close=")">
                            <if test="index != 0">
                                <choose>
                                    <when test="index % 500 == 0"> ) OR a.parking_space_id in ( </when>
                                    <otherwise>,</otherwise>
                                </choose>
                            </if>
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="dto.dealDateFrom != null and dto.dealDateTo != null">
                        and a.deal_date between concat(#{dto.dealDateFrom}, ' 00:00:00') and concat(#{dto.dealDateTo}, ' 23:59:59')
                    </if>
                    <if test="dto.dealAmountFrom != null and dto.dealAmountTo != null">
                        and a.deal_amount between #{dto.dealAmountFrom} * 10000 and #{dto.dealAmountTo} * 10000
                    </if>
                    <if test="dto.ownerCommissionFrom != null and dto.ownerCommissionTo != null">
                        and a.owner_commission between #{dto.ownerCommissionFrom} and #{dto.ownerCommissionTo}
                    </if>
                    <if test="dto.customerCommissionFrom != null and dto.customerCommissionTo">
                        and a.customer_commission between #{dto.customerCommissionFrom} and #{dto.customerCommissionTo}
                    </if>
                    <if test="dto.signDateFrom != null and dto.signDateTo != null">
                        and a.sign_date between concat(#{dto.signDateFrom}, ' 00:00:00')  and concat(#{dto.signDateTo}, ' 23:59:59')
                    </if>
                    <if test="permissionUserIdList != null  and permissionUserIdList.size() > 0">
                      <choose>
                          <when test="permissionUserIdList != null  and permissionUserIdList.size() == 1 and permissionUserIdList.get(0) == -9999L">
                              and 1 = 1
                          </when>
                          <otherwise>
                              and a.broker_id in
                              <foreach collection="permissionUserIdList" close=")" open="(" item="userId" separator=",">
                                  #{userId}
                              </foreach>
                          </otherwise>
                      </choose>
                    </if>
                    <if test="dto.allDateFrom != null and dto.allDateTo != null">
                        and a.status in ('payed', 'closed')
                    </if>
                group by a.id
                having 1 = 1
                <if test="dto.awaitCommissionFrom != null and dto.awaitCommissionTo != null">
                    and await_commission between #{dto.awaitCommissionFrom} and #{dto.awaitCommissionTo}
                </if>
                <if test="dto.allDateFrom != null and dto.allDateTo != null">
                    and a.collect_all_date between concat(#{dto.allDateFrom},' 00:00:00') and concat(#{dto.allDateTo},' 23:59:59')
                </if>
          ) as c
        where 1 = 1
       order by status_sort desc, sign_date desc, create_time desc
    </select>

</mapper>
