<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessSubOrderRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessSubOrderRefund">
        <id column="id" property="id" />
        <result column="parent_order_id" property="parentOrderId" />
        <result column="sub_order_code" property="subOrderCode" />
        <result column="receive_sub_order_id" property="receiveSubOrderId" />
        <result column="amount" property="amount" />
        <result column="refund_channel" property="refundChannel" />
        <result column="bank_num" property="bankNum" />
        <result column="account_name" property="accountName" />
        <result column="bank_name" property="bankName" />
        <result column="city" property="city" />
        <result column="order_status" property="orderStatus" />
        <result column="sf_refund_order_num" property="sfRefundOrderNum" />
        <result column="charge_id" property="chargeId" />
        <result column="remark" property="remark"/>
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_order_id, sub_order_code, receive_sub_order_id, amount, refund_channel, bank_num, account_name, bank_name, city, order_status,
          sf_refund_order_num, charge_id, remark, revision, create_user,
        create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
