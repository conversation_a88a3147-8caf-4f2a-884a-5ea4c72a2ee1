<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessSubOrderReceiveMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessSubOrderReceive">
        <id column="id" property="id" />
        <result column="parent_order_id" property="parentOrderId" />
        <result column="sub_order_code" property="subOrderCode" />
        <result column="pay_mode" property="payMode" />
        <result column="receive_community_id" property="receiveCommunityId" />
        <result column="receive_community_name" property="receiveCommunityName" />
        <result column="zs_receive_community_code" property="zsReceiveCommunityCode" />
        <result column="order_status" property="orderStatus" />
        <result column="pay_amount" property="payAmount" />
        <result column="good_name" property="goodName" />
        <result column="discount_amount" property="discountAmount" />
        <result column="pay_channel" property="payChannel" />
        <result column="charge_id" property="chargeId" />
        <result column="business_id" property="businessId" />
        <result column="remark" property="remark"/>
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_order_id, sub_order_code, business_id, pay_mode, receive_community_id, receive_community_name, zs_receive_community_code, order_status, pay_amount, good_name, discount_amount, pay_channel, charge_id,  remark, revision, create_user,
        create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
