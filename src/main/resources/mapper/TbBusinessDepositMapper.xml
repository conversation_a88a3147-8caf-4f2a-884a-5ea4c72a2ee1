<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessDepositMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessDeposit">
        <id column="id" property="id" />
        <result column="deposit_code" property="depositCode" />
        <result column="type" property="type" />
        <result column="category" property="category" />
        <result column="transaction_contract_num" property="transactionContractNum" />
        <result column="house_desc" property="houseDesc" />
        <result column="parking_space_desc" property="parkingSpaceDesc" />
        <result column="customer_id" property="customerId" />
        <result column="customer_mobile" property="customerMobile" />
        <result column="broker_id" property="brokerId" />
        <result column="status" property="status" />
        <result column="audit_time" property="auditTime" />
        <result column="remark" property="remark" />
        <result column="order_id" property="orderId" />
        <result column="order_code" property="orderCode" />
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, deposit_code, type, category, transaction_contract_num, house_desc, parking_space_desc, order_id, order_code, customer_id, customer_mobile, broker_id, status, audit_time, remark, revision, create_user,
        create_time, update_user, update_time, is_deleted
    </sql>


    <select id="selectPages" resultMap="BaseResultMap">
        select tdd.*
        from tb_business_deposit as tdd
        where tdd.is_deleted = 0
        <if test="params.type != null">
            and tdd.type = #{params.type}
        </if>
        <if test="params.depositCode != null">
            and tdd.deposit_code like concat('%',concat(#{params.depositCode},'%'))
        </if>
        <if test="params.brokerIds != null">
            and tdd.broker_id in
            <foreach collection="params.brokerIds" item="brokerId" separator="," open="(" close=")">
                #{brokerId}
            </foreach>
        </if>
        <if test="params.category != null">
            and tdd.category = #{params.category}
        </if>
        <if test="params.status != null">
            and tdd.status = #{params.status}
        </if>
        <if test="permissionUserIdList != null and permissionUserIdList.size() > 0">
            <choose>
                <when test="permissionUserIdList != null  and permissionUserIdList.size() == 1 and permissionUserIdList.get(0) == -9999L">
                    and 1 = 1
                </when>
                <otherwise>
                    and tdd.broker_id in
                    <foreach collection="permissionUserIdList" close=")" open="(" item="userId" separator=",">
                        #{userId}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="customerIdList != null  and customerIdList.size() > 0">
            and (
                tdd.customer_id in
                <foreach collection="customerIdList" item="customerId" separator="," close=")" open="(" >
                    #{customerId}
                </foreach>
            )
        </if>
        group by tdd.id
        order by tdd.create_time desc
    </select>
</mapper>
