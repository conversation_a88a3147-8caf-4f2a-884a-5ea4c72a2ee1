<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessContractResidentRentMapper">

  <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessContractResidentRent">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="contract_id" jdbcType="BIGINT" property="contractId" />
    <result column="proxy_user_name" jdbcType="VARCHAR" property="proxyUserName" />
    <result column="proxy_user_mobile" jdbcType="VARCHAR" property="proxyUserMobile" />
    <result column="proxy_user_id_number" jdbcType="VARCHAR" property="proxyUserIdNumber" />
    <result column="tenant_proxy_user_name" jdbcType="VARCHAR" property="tenantProxyUserName" />
    <result column="tenant_proxy_user_mobile" jdbcType="VARCHAR" property="tenantProxyUserMobile" />
    <result column="tenant_proxy_user_id_number" jdbcType="VARCHAR" property="tenantProxyUserIdNumber" />
    <result column="co_purchaser" jdbcType="VARCHAR" property="coPurchaser" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="create_user" property="createUser" />
    <result column="create_time" property="createTime" />
    <result column="update_user" property="updateUser" />
    <result column="update_time" property="updateTime" />
    <result column="is_deleted" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, contract_id, proxy_user_name, proxy_user_mobile, proxy_user_id_number, tenant_proxy_user_name, 
    tenant_proxy_user_mobile, tenant_proxy_user_id_number, co_purchaser, revision, create_user,
    create_time, update_user, update_time, is_deleted
  </sql>
  
</mapper>