<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessCommitmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessCommitment">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="commitment_code" jdbcType="VARCHAR" property="commitmentCode" />
        <result column="commitment_type" jdbcType="VARCHAR" property="commitmentType" />
        <result column="amount" jdbcType="DECIMAL" property="amount" />
        <result column="checkout_day" jdbcType="TIMESTAMP" property="checkoutDay" />
        <result column="checkout_month" jdbcType="TIMESTAMP" property="checkoutMonth" />
        <result column="distribution_status" jdbcType="TINYINT" property="distributionStatus" />
        <result column="revision" jdbcType="INTEGER" property="revision" />
        <result column="create_user" jdbcType="BIGINT" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user" jdbcType="BIGINT" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, commitment_code, commitment_type, amount, checkout_day, checkout_month, distribution_status,
        revision, create_user, create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
