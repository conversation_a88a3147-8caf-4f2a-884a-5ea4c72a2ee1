<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessTransactionInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessTransactionInfo">
        <id column="id" property="id" />
        <result column="deposit_id" property="depositId" />
        <result column="transaction_date_time" property="transactionDateTime" />
        <result column="transaction_type" property="transactionType" />
        <result column="transaction_amount" property="transactionAmount" />
        <result column="contract_num" property="contractNum" />
<!--        <result column="enclosure_file_key" property="enclosureFileKey" />-->
        <result column="status" property="status" />
        <result column="order_status" property="orderStatus" />
        <result column="sub_order_id" property="subOrderId" />
        <result column="sub_order_code" property="subOrderCode" />
        <result column="refund_channel" property="refundChannel" />
        <result column="bank_num" property="bankNum" />
        <result column="account_name" property="accountName" />
        <result column="bank_name" property="bankName" />
        <result column="city" property="city" />
        <result column="remark" property="remark" />
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, deposit_id, transaction_date_time, transaction_type, transaction_amount, contract_num, status, order_status, sub_order_id, sub_order_code,
          refund_channel,bank_num,account_name,bank_name,city,
          remark, revision, create_user,
        create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
