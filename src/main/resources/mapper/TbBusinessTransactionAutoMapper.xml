<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessTransactionAutoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessTransactionAuto">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="transaction_id" jdbcType="BIGINT" property="transactionId" />
        <result column="tenths_role" jdbcType="VARCHAR" property="tenthsRole" />
        <result column="tenths_ratio" jdbcType="DECIMAL" property="tenthsRatio" />
        <result column="tenths_money" jdbcType="DECIMAL" property="tenthsMoney" />
        <result column="tenths_transfer" jdbcType="VARCHAR" property="tenthsTransfer" />
        <result column="percentage" jdbcType="VARCHAR" property="percentage" />
        <result column="user_Id" jdbcType="BIGINT" property="userId" />
        <result column="amount" jdbcType="DECIMAL" property="amount" />
        <result column="revision" jdbcType="INTEGER" property="revision" />
        <result column="create_user" jdbcType="BIGINT" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user" jdbcType="BIGINT" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, transaction_id, tenths_role, tenths_ratio, tenths_money, tenths_transfer, percentage, user_Id, amount,
        revision, create_user, create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
