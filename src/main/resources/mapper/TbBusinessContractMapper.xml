<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessContractMapper">
  <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessContract">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="contract_number" jdbcType="VARCHAR" property="contractNumber" />
    <result column="contract_type" jdbcType="VARCHAR" property="contractType" />
    <result column="contract_status" jdbcType="VARCHAR" property="contractStatus" />
    <result column="house_id" jdbcType="BIGINT" property="houseId" />
    <result column="intermediary_company_id" jdbcType="BIGINT" property="intermediaryCompanyId" />
    <result column="leaser_id" jdbcType="BIGINT" property="leaserId" />
    <result column="leaser_user_name" jdbcType="VARCHAR" property="leaserUserName" />
    <result column="leaser_mobile" jdbcType="VARCHAR" property="leaserMobile" />
    <result column="leaser_id_number" jdbcType="VARCHAR" property="leaserIdNumber" />
    <result column="leaser_address" jdbcType="VARCHAR" property="leaserAddress" />
    <result column="customer_id" jdbcType="BIGINT" property="tenantId" />
    <result column="customer_mobile" jdbcType="VARCHAR" property="tenantMobile" />
    <result column="customer_id_number" jdbcType="VARCHAR" property="tenantIdNumber" />
    <result column="customer_address" jdbcType="VARCHAR" property="tenantAddress" />
    <result column="contract_sign_date" jdbcType="TIMESTAMP" property="contractSignDate" />
    <result column="transaction_price" jdbcType="DECIMAL" property="transactionPrice" />
    <result column="leaser_commission" jdbcType="DECIMAL" property="leaserCommission" />
    <result column="customer_commission" jdbcType="DECIMAL" property="customerCommission" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="create_user" property="createUser" />
    <result column="create_time" property="createTime" />
    <result column="update_user" property="updateUser" />
    <result column="update_time" property="updateTime" />
    <result column="is_deleted" property="isDeleted" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, contract_number, contract_type, contract_status, house_id, intermediary_company_id, 
    leaser_id, leaser_user_name, leaser_mobile, leaser_id_number, leaser_address,
    tenant_id, tenant_mobile, tenant_id_number, tenant_address, contract_sign_date, 
    transaction_price, leaser_commission, customer_commission, revision, create_user,
    create_time, update_user, update_time, is_deleted
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_business_contract
    where id = #{id,jdbcType=BIGINT}
  </select>
  
</mapper>