<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessOrder">
        <id column="id" property="id" />
        <result column="order_code" property="orderCode" />
        <result column="status" property="status" />
        <result column="contract_num" property="contractNum"/>
        <result column="house_ids" property="houseIds" />
        <result column="parking_ids" property="parkingIds" />
        <result column="building" property="building" />
        <result column="unit" property="unit" />
        <result column="house_number" property="houseNumber" />
        <result column="new_house_community_id" property="newHouseCommunityId" />
        <result column="community_ids" property="communityIds" />
        <result column="customer_name" property="customerName" />
        <result column="customer_mobile" property="customerMobile" />
        <result column="order_date" property="orderDate" />
        <result column="amount" property="amount" />
        <result column="transaction_type" property="transactionType" />
        <result column="remark" property="remark"/>
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , order_code, status, contract_num, house_ids, parking_ids, building, unit, house_number, new_house_community_id,
          community_ids, customer_name, customer_mobile, order_date, amount, transaction_type, revision,  remark, create_user,
        create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
