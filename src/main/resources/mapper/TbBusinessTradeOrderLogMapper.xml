<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessTradeOrderLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessTradeOrderLog">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="order_code" property="orderCode" />
        <result column="order_serial_number" property="orderSerialNumber" />
        <result column="sub_order_id" property="subOrderId" />
        <result column="sub_order_code" property="subOrderCode" />
        <result column="sub_order_serial_number" property="subOrderSerialNumber" />
        <result column="status" property="status" />
        <result column="resp_body" property="respBody" />
        <result column="req_body" property="reqBody" />
        <result column="call_back_flag" property="callBackFlag" />
        <result column="inner_task_flag" property="innerTaskFlag" />
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, order_code, order_serial_number, sub_order_id, sub_order_code, sub_order_serial_number, status,
          req_body, resp_body, call_back_flag, inner_task_flag
          revision, create_user, create_time, update_user, update_time, is_deleted, remark
    </sql>

</mapper>
