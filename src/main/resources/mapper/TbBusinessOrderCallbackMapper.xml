<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessOrderCallbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessOrderCallback">
        <id column="id" property="id" />
        <result column="out_trade_no" property="outTradeNo" />
        <result column="sub_out_trade_no" property="subOutTradeNo" />
        <result column="status" property="status" />
        <result column="body" property="body" />
        <result column="total_fee" property="totalFee" />
        <result column="response" property="response" />
        <result column="revision" property="revision" />
        <result column="remark" property="remark" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, out_trade_no, sub_out_trade_no, status, body, total_fee, response, revision, create_user,
    create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
