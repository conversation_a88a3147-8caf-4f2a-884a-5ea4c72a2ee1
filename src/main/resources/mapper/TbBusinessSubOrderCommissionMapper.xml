<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessSubOrderCommissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessSubOrderCommission">
        <id column="id" property="id" />
        <result column="parent_order_id" property="parentOrderId" />
        <result column="sub_order_code" property="subOrderCode" />
        <result column="export_sub_order_id" property="exportSubOrderId" />
        <result column="export_sub_order_code" property="exportSubOrderCode" />
        <result column="export_amount" property="exportAmount" />
        <result column="order_status" property="orderStatus" />
        <result column="charge_id" property="chargeId" />
        <result column="business_id" property="businessId" />
        <result column="remark" property="remark"/>
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_order_id, sub_order_code, business_id, export_sub_order_id, export_sub_order_code, export_amount, order_status, charge_id,  remark, revision, create_user,
        create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
