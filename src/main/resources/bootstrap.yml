spring:
  cloud:
    nacos:
      config:
        server-addr: ${NACOS_ADDR:mixc-be-rsms-nacos:8848}
        namespace: ${NACOS_NAMESPACE:rsms-dev}
        file-extension: yaml
        extension-configs:
          - dataId: rsms-business.yml
            refresh: true
            group: rsms-business
        shared-configs:
          - dataId: public-redisson-cfg-sentinel.yml
            refresh: true
            group: public
          - dataId: sa-token.yml
            refresh: true
            group: public
        username: rsms
        password: 4uy5Ryp7oW3W2xc1
logging:
  config: classpath:logback-nacos.xml
