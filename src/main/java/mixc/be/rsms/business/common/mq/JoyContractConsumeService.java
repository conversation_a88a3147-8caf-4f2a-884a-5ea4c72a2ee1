package mixc.be.rsms.business.common.mq;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.domain.dto.JoyContractCallbackParam;
import mixc.be.rsms.business.service.IBizContractService;
import mixc.be.rsms.pojo.business.MessageContent;

/**
 * <AUTHOR>
 * @date 2025-03-12
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class JoyContractConsumeService implements ApplicationListener<MessageReceivedEvent> {

	private final IBizContractService contractService;
	
    @SuppressWarnings("unchecked")
	@Override
    public void onApplicationEvent(MessageReceivedEvent event) {
    	log.info("合同状态实时更新回调业务开始执行");;
	    if (event.getMessageEnum().getCode().equals(MessageEnum.JOY_CONTRACT_STATUS_DATA.getCode())) {
	        try {
	        	ObjectMapper objectMapper = new ObjectMapper();
	            objectMapper.registerModule(new JavaTimeModule());
	            MessageContent<String> messageContent = objectMapper.readValue(event.getMessage(), MessageContent.class);
	            String joyContractMsg = messageContent.getData();
	            log.info("合同状态实时更新获取到的内容为：{}", joyContractMsg);
	            //更新合同状态
	            if (!ObjectUtils.isEmpty(joyContractMsg)) {
	            	JoyContractCallbackParam joyContractCallbackParam = objectMapper.readValue(joyContractMsg, JoyContractCallbackParam.class);
	            	contractService.realTimeUpdateJoyContractStatus(joyContractCallbackParam);
	            }
	        } catch (Exception e) {
	            log.error("MQ接收的轻合同回调修改状态消息格式异常,消息体={},异常信息={}", event.getMessage(), e.getMessage());
	            throw new BusinessException("MQ接收的轻合同回调修改状态消息格式异常,异常信息={}", e.getMessage());
	        }
	    }
    }
}