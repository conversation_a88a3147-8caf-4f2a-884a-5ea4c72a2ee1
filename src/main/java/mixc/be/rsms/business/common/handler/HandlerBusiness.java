package mixc.be.rsms.business.common.handler;

import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.enums.BusinessEnum;
import mixc.be.rsms.business.common.exception.BizException;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.exception.UnAuthException;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.ResultVo;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname handle
 * @Date 2021/3/17 4:41 下午
 */
@ControllerAdvice
@Slf4j
public class HandlerBusiness {

    @ExceptionHandler(value = BusinessException.class)
    @ResponseBody
    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    public ResultVo<Object> handlerBusinessException(BusinessException e){
        return ResultVoUtil.error(e.getCode(),e.getMessage());

    }

    @ExceptionHandler(value = BizException.class)
    @ResponseBody
    @ResponseStatus(value = HttpStatus.OK)
    public <T> ResultVo<T> handlerBizException(BizException e){
        return ResultVoUtil.error(e.getCode(),e.getMessage());

    }
    
    @ExceptionHandler(value = UnAuthException.class)
    @ResponseBody
    @ResponseStatus(value = HttpStatus.UNAUTHORIZED)
    public <T> ResultVo<T> handlerUnAuthException(UnAuthException e){
        return ResultVoUtil.error(e.getCode(),e.getMessage());
    }
    
    

    /*
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    @ResponseStatus(value = HttpStatus.OK)
    public ResultVo<?> handleValidException(MethodArgumentNotValidException e) {
        log.error("参数校验异常", e);
        // 获取所有错误信息
        List<String> errors = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .toList();

        return ResultVoUtil.error(BusinessEnum.PARAMETER_EXCEPTION.getCode(),errors.toString());
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResultVo<?> handleBindException(BindException e) {
        log.error("参数绑定异常", e);
        List<String> errors = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .toList();

        return ResultVoUtil.error(BusinessEnum.PARAMETER_EXCEPTION.getCode(),errors.toString());
    }

}
