package mixc.be.rsms.business.common.aspect;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.annotation.OperateLog;
import mixc.be.rsms.business.common.feignclient.DataServerClient;
import mixc.be.rsms.business.domain.dto.DepositAddParams;
import mixc.be.rsms.business.domain.dto.DepositAuditParams;
import mixc.be.rsms.business.domain.dto.TransactionReportAddParams;
import mixc.be.rsms.business.domain.dto.TransactionReportEditParams;
import mixc.be.rsms.business.service.impl.DepositServiceImpl;
import mixc.be.rsms.business.service.impl.TransactionReportServiceImpl;
import mixc.be.rsms.common.enums.DropdownEnum;
import mixc.be.rsms.common.utils.AuthUtil;
import mixc.be.rsms.common.utils.SnowFlake;
import mixc.be.rsms.pojo.data.OperateLogResp;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 操作日志
 * @date 2024/10/07
 */
@Slf4j
@Aspect
@Component
public class BizLogAspect {

    @Resource
    private SnowFlake snowFlake;
    @Resource
    private DataServerClient dataServerClient;

    @Pointcut("@annotation(mixc.be.rsms.business.common.annotation.OperateLog)")
    public void pointCut() {

    }

    @Around(value = "pointCut()")
    public Object saveOperateLog(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        Object[] methodParams = joinPoint.getArgs();
        OperateLog operationLog = method.getAnnotation(OperateLog.class);
        String operateType = operationLog.operateType();
        String operateModule = operationLog.operateModule();
        log.info("operateModule：{} and operateType：{}", operateModule, operateType);

        Object methodReturnObj = joinPoint.proceed();
        // 登陆人Id
        Long operateId = AuthUtil.getLoginIdAsLong();
        // 处理带看客源日志
        Class<?> declaringClass = method.getDeclaringClass();
        // 注解标记的是 客源 service 类下的方法
      if (declaringClass.equals(TransactionReportServiceImpl.class)) {
            // 成交报告
            dealWithTransactionReportLog(operateId, methodParams, methodReturnObj, operateType, operateModule);
        } else if (declaringClass.equals(DepositServiceImpl.class)){
            // 意向金，模块类型算 成交报告
            dealWithDepositLog(operateId, methodParams, methodReturnObj, operateType, operateModule);
        }
        return methodReturnObj;
    }


    /**
     *
     * @param operateId     操作人ID
     * @param methodParams  请求参数
     * @param operateType   操作类型
     * @param operateModule 操作模块
     */
    private void dealWithDepositLog(Long operateId, Object[] methodParams, Object methodReturnObj, String operateType, String operateModule) {
        Long businessId = 0L;
        String operateContent = "";
        if (methodParams.length > 0) {
            // 新增
            Optional<Object> first = Arrays.stream(methodParams).filter(item -> {
                return item instanceof DepositAddParams;
            }).findFirst();
            if (first.isPresent() && methodReturnObj instanceof Long) {
                businessId = (Long) methodReturnObj;
                DepositAddParams addParams = (DepositAddParams) first.get();
                if (!ObjectUtils.isEmpty(addParams.getId())) {
                    operateType = DropdownEnum.OPERATE_LOG_OPERATE_TYPE_EDIT.getDictKey();
                }else {
                    operateType = DropdownEnum.OPERATE_LOG_OPERATE_TYPE_ADD.getDictKey();
                }
            }else {
                // 提交、审核、驳回、反审核
                first = Arrays.stream(methodParams).filter(item -> {
                    return item instanceof DepositAuditParams;
                }).findFirst();
                if (first.isPresent()) {
                    Object o = first.get();
                    DepositAuditParams params = ((DepositAuditParams) o);
                    // 提交
                    if (!ObjectUtils.isEmpty(params.getDeposit()) && methodReturnObj instanceof Long){
                        businessId = (Long) methodReturnObj;
                        operateType = DropdownEnum.OPERATE_LOG_OPERATE_TYPE_SUBMIT.getDictKey();
                    } else if (!ObjectUtils.isEmpty(params)) {
                        // 审核、驳回、反审核
                        if (!ObjectUtils.isEmpty(params.getId())) {
                            businessId = params.getId();
                        }
                        if (!ObjectUtils.isEmpty(params.getPass()) && !DropdownEnum.OPERATE_LOG_OPERATE_TYPE_ANTI_AUDIT.getDictKey().equals(operateType)) {
                            operateType = params.getPass() ? DropdownEnum.OPERATE_LOG_OPERATE_TYPE_AUDIT.getDictKey() : DropdownEnum.OPERATE_LOG_OPERATE_TYPE_REJECT.getDictKey();
                        }
                    }
                }
            }

            operateContent = getOptionMsg(operateType);
            //保存日志
            saveLog(businessId, operateModule, operateType, operateId, operateContent);
        }
    }

    /**
     * 处理成交报告日志
     *
     * @param operateId     操作人ID
     * @param methodParams  请求参数
     * @param operateType   操作类型
     * @param operateModule 操作模块
     */
    private void dealWithTransactionReportLog(Long operateId, Object[] methodParams, Object methodReturnObj, String operateType, String operateModule) {
        Long businessId = 0L;
        String operateContent = "";
        if (methodParams.length > 0) {
            Optional<Object> first = Arrays.stream(methodParams).filter(item -> {
                return item instanceof TransactionReportEditParams || item instanceof TransactionReportAddParams || item instanceof Long;
            }).findFirst();
            if (first.isPresent()) {
                Object o = first.get();
                if (o instanceof TransactionReportEditParams transactionReportEditParams) {
                    if (!ObjectUtils.isEmpty(transactionReportEditParams.getId())) {
                        businessId = transactionReportEditParams.getId();
                    } else if (!ObjectUtils.isEmpty(transactionReportEditParams) && !ObjectUtils.isEmpty(transactionReportEditParams.getTransactionReport())
                            && !ObjectUtils.isEmpty(transactionReportEditParams.getTransactionReport().getId())) {
                        businessId = transactionReportEditParams.getTransactionReport().getId();
                    } else if (!ObjectUtils.isEmpty(methodReturnObj) && methodReturnObj instanceof Long) {
                        businessId = (Long) methodReturnObj;
                    }
                } else if (o instanceof TransactionReportAddParams transactionReportAddParams) {
                    // 修改不记日志
                    if (!ObjectUtils.isEmpty(transactionReportAddParams.getId())) {
                        return;
                    }
                    if (!ObjectUtils.isEmpty(methodReturnObj) && methodReturnObj instanceof Long) {
                        businessId = (Long) methodReturnObj;
                    }
                } else if (o instanceof Long) {
                    businessId = (Long) o;
                }
                operateContent = getOptionMsg(operateType);
            }
            //保存日志
            saveLog(businessId, operateModule, operateType, operateId, operateContent);
        }
    }

    /**
     * 根据操作类型获取状态码
     *
     * @param dicKey
     * @return
     */
    private String getOptionMsg(String dicKey) {
        Optional<DropdownEnum> first = Arrays.stream(DropdownEnum.values()).filter(item -> {
            if (item.getDictKey().equals(dicKey)) {
                return true;
            }
            return false;
        }).findFirst();
        return first.map(dropdownEnum -> String.format("【操作类型：%s】", dropdownEnum.getDictValue())).orElse("not null option type");
    }



    /**
     * 保存日志内容
     *
     * @param businessId     业务主键
     * @param operateModule  操作模块
     * @param operateType    操作类型
     * @param operatorId     操作人ID
     * @param operateContent 操作内容
     */
    private void saveLog(Long businessId, String operateModule, String operateType, Long operatorId, String operateContent) {
        OperateLogResp operateLog = new OperateLogResp();
        operateLog.setId(snowFlake.nextId());
        operateLog.setBusinessId(businessId);
        operateLog.setOperateModule(operateModule);
        operateLog.setOperateType(operateType);
        operateLog.setCreateUser(operatorId);
        operateLog.setOperateContent(operateContent);
        dataServerClient.innerSaveLog(operateLog);
    }

}
