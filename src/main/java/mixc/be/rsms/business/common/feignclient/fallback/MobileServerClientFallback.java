package mixc.be.rsms.business.common.feignclient.fallback;

import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.feignclient.MobileServerClient;

/**
 * <AUTHOR>
 * @description
 * @date 2025/03/25
 */
@Slf4j
public class MobileServerClientFallback implements MobileServerClient {
    @Override
    public String getOpenIdByMobile(String mobile) {
        log.error("根据手机号查询用户的 OpenId失败");
        throw new BusinessException("根据手机号查询用户的 OpenId失败", "-1");
    }
}
