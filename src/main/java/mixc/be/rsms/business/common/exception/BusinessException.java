package mixc.be.rsms.business.common.exception;

import lombok.Getter;
import mixc.be.rsms.business.common.enums.ResultEnum;
import mixc.be.rsms.common.service.ErrorCode;

/**
 * <AUTHOR>
 * @Classname AuthorException
 * @Date 2021/3/17 4:39 下午
 */
@Getter
public class BusinessException extends RuntimeException{

    private String code;


    public BusinessException(ErrorCode errorCode){
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
    }

    public BusinessException(ResultEnum resultEnum){
        super(resultEnum.getMsg());
        this.code = resultEnum.getCode();
    }

    public BusinessException(String message, String code) {
        super(message);
        this.code = code;
    }
}
