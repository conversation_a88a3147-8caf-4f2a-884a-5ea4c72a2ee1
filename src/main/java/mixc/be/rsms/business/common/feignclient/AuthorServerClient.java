package mixc.be.rsms.business.common.feignclient;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import mixc.be.rsms.business.common.feignclient.fallback.AuthServerClientFallback;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.author.UserDetailsMicroResp;
import mixc.be.rsms.pojo.third.EmployeeInfoResponse;

@FeignClient(name = "rsms-author", fallback = AuthServerClientFallback.class)
public interface AuthorServerClient {

	/**
	 * 根据权限编码返回用户id集合
	 * @param buttonCode ID集合
	 * @return
	 */
	@GetMapping("/user/getUserPermissionByCode")
	List<Long> getUserPermissionByCode(@RequestParam String buttonCode);
	
	/**
	 * 根据权限编码和授权类型返回所属公司集合
	 * @param buttonCode ID集合
	 * @param resTypeCode 授权类型编码
	 *
	 * @return
	 */
	@GetMapping("/user/getUserPermissionByCode")
	List<Long> getUserPermissionByCode(@RequestParam String buttonCode, @RequestParam String resTypeCode);

	/**
	 * @description true存在租售状态为停用的用户
	 * <AUTHOR>
	 * @param[1] userIds
	 * @throws
	 * @return Boolean
	 * @time 2024/10/8 18:36
	 */
	@GetMapping("/user/getUserRsmsStatusEnableExists")
	Boolean getUserRsmsStatusEnableExists(@RequestParam List<Long> userIds);

	/**
	 * @description 获取用户权限集合字符码
	 * <AUTHOR>
	 * @param[1] userId
	 * @throws
	 * @return List<String>
	 * @time 2024/9/30 19:38
	 */
	@GetMapping("/permissions/all")
	List<String> getUserAllPermissions(@RequestParam Long userId);


	/**
	 * 根据 idList 获取多个用户的信息
	 * @param userIds
	 * @return
	 */
	@GetMapping("/user/getUserDetailsMicro")
	List<UserDetailsMicroResp> getUserDetailsMicro(@RequestParam List<Long> userIds);

	/**
	 * 根据用户名称模糊搜索用户ID
	 * @param userName 用户名称
	 * @return
	 */
	@GetMapping("/user/getUserIdByNameMicro")
	List<Long> getUserIdsByUserName(@RequestParam String userName);

	@PostMapping(value = "/init/user/userSaveBatch")
	void userSaveBatch(@RequestBody List<EmployeeInfoResponse> employees);

	@PostMapping(value = "/init/user/userUpdateBatch")
	void userUpdateBatch(@RequestBody List<EmployeeInfoResponse> employees);
	
	/**
     * 员工端登出
     * @return
     */
    @PostMapping("/permissions/empLogout")
    ResultVo<Boolean> empLogout();
}
