package mixc.be.rsms.business.common.mq;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2024-10-15 14:17
 */

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReceiveMsg {

    private final ApplicationEventPublisher eventPublisher;

    /**
     * 消费者通过事件驱动模型将消息转发给事件监听者
     * @return
     */
    @Bean
   public Consumer<String> chargeMsgChannel() {
        return message -> {
            log.info("chargeMsgChannel接收收费数据消息为=>{}",message);
            if (StringUtils.isNotEmpty(message)){
                eventPublisher.publishEvent(new MessageReceivedEvent(this, message, MessageEnum.CHARGE_ORDER_DATA));
            }
        };
    }
    
    /**
     * 朝昔合同状态变更信息发给事件监听者
     * @return
     */
    @Bean
   public Consumer<String> joyContractStatusMsgChannel() {
        return message -> {
            log.info("joyContractStatusMsgChannel：接收轻合同数据消息为=>{}",message);
            if (StringUtils.isNotEmpty(message)){
                eventPublisher.publishEvent(new MessageReceivedEvent(this, message, MessageEnum.JOY_CONTRACT_STATUS_DATA));
            }
        };
    }

}
