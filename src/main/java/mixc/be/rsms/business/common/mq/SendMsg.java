package mixc.be.rsms.business.common.mq;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-12-30 14:32
 */

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SendMsg {

    private final StreamBridge streamBridge;

    /**
     * 发送账单收入单消息
     */
    public void sendChargeOrderMsg(String msg) {
        log.info("收费回调修改订单状态=>{}",msg);
        streamBridge.send("chargeMsgChannel-out-0", MessageBuilder.withPayload(msg).build());
    }
    
    /**
     * 发送账单收入单消息
     */
    public void sendJoyContractStatusMsg(String msg) {
        log.info("轻合同回调租售系统实时更新合同状态，消息内容：{}",msg);
        streamBridge.send("joyContractStatusMsgChannel-out-0", MessageBuilder.withPayload(msg).build());
    }

}
