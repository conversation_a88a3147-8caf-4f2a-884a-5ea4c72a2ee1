package mixc.be.rsms.business.common.mq;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024-10-15 14:27
 */

@Getter
public enum MessageEnum {

    CHARGE_ORDER_DATA("charge_order_data","收费系统回到订单状态数据"),
    JOY_CONTRACT_STATUS_DATA("JOY_CONTRACT_STATUS_DATA","轻合同合同状态变更回调状态数据"),
    ;

    private final String code;

    private final String msg;

    MessageEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
