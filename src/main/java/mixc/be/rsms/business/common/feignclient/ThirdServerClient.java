package mixc.be.rsms.business.common.feignclient;

import mixc.be.rsms.business.common.feignclient.fallback.ThirdServerClientFallback;
import mixc.be.rsms.pojo.third.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/25
 */
@FeignClient(name = "rsms-third", contextId = "rsms-third-sf", fallback = ThirdServerClientFallback.class)
public interface ThirdServerClient {


    /**
     * 获取电商订单规则收费项
     * @param param
     * @return
     */
    @PostMapping(value = "/sf/getItems")
    SFOrderItemResp getItems(@RequestBody SFOrderItemParam param);

    /**
     * 同步订单
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/sf/syncOrder")
    SFOrderResp syncOrder(@RequestBody SFOrderSyncParam param);

    /**
     * 调整已支付的子订单到其他子订单单
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/sf/adjustToOrder")
    SFOrderResp adjustToOrder(@RequestBody SFOrderAdjustParam param);

    /**
     * 新增子订单
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/sf/addSubOrder")
    SFOrderResp addSubOrder(@RequestBody SFOrderAddSubParam param);

    /**
     * 调整取消子订单
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/sf/cancelSubOrder")
    SFOrderResp cancelSubOrder(@RequestBody SFOrderCancelSubParam param);

    /**
     * 查询订单详情
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/sf/getOrderDetailInfo")
    SFOrderDetailResp getOrderDetailInfo(@RequestBody SFOrderDetailParam param);

    /**
     * 查询子订单详情
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/sf/getSubOrderDetailInfo")
    SFOrderDetailSubResp getSubOrderDetailInfo(@RequestBody SFOrderDetailSubParam param);

    /**
     * 订单退款
     *
     * @param param
     * @return
     */
    @PostMapping("/sf/refund")
    SfOrderRefundResp refund(@RequestBody SFOrderRefundParam param);

    /**
     * 订单退款详情
     *
     * @param param
     * @return
     */
    @PostMapping("/sf/refundDetail")
    SFOrderRefundDetailResp refundDetail(@RequestBody SFOrderRefundDetailParam param);

    /**
     * 支付交易查询
     *
     * @param param
     * @return
     */
    @PostMapping("/sf/payQuery")
    SFOrderPayQueryResp payQuery(@RequestBody SFOrderPayQueryParam param);

    /**
     * 支付交易下单
     * @param param
     * @return
     */
    @PostMapping("/sf/createTrade")
    SFOrderTradeCreateResp createTrade(@RequestBody SFOrderTradeCreateParam param);
}
