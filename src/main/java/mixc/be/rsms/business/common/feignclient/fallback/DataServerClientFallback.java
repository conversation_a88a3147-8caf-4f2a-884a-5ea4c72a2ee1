package mixc.be.rsms.business.common.feignclient.fallback;

import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.feignclient.DataServerClient;
import mixc.be.rsms.business.domain.dto.ContractFileInfoParam;
import mixc.be.rsms.business.domain.dto.ContractNumberParam;
import mixc.be.rsms.business.domain.dto.OrgDetailsResp;
import mixc.be.rsms.business.vo.DataFileInfoVo;
import mixc.be.rsms.business.vo.EnterpriseVo;
import mixc.be.rsms.business.vo.HouseInfoVo;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.data.*;
import mixc.be.rsms.pojo.file.FileInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/05
 */
@Slf4j
public class DataServerClientFallback implements DataServerClient {

    @Override
    public ResultVo<List<FileInfo>> previewFile(Long fileId) {
        log.error("文件预览失败");
        throw new BusinessException("文件预览失败", "-1");
    }

    @Override
    public ResultVo<List<DataFileInfoVo>> findByBusinessIdList(List<Long> businessIdList) {
        log.error("根据多个业务Id查询文件信息失败");
        throw new BusinessException("文件预览失败", "-1");
    }

    /**
     * 批量保存文件信息
     * @param dtoList
     * @return
     */
    @Override
    public ResultVo<Integer> innerBatchSave(List<FileInfoResp> dtoList) {
        log.error("批量保存文件信息失败");
        throw new BusinessException("批量保存文件信息失败", "-1");
    }

    /**
     * 根据 业务ID 删除
     * @param bussinessIdList
     * @return
     */
    @Override
    public ResultVo<Integer> deleteByBusinessIdList(List<Long> bussinessIdList) {
        log.error("根据 业务ID 删除失败");
        throw new BusinessException("根据 业务ID 删除失败", "-1");
    }

    @Override
    public ResultVo<List<DataFileInfoVo>> listFileInfo(Long businessId, String fileType, Boolean isPresigned) {
        log.error("根据业务主键、业务类型、文件类型和是否预览查询文件信息失败");
        throw new BusinessException("根据业务主键、业务类型、文件类型和是否预览查询文件信息失败", "-1");
    }

    /**
     * 根据关键字查询客户信息查询
     * @param customerNameKeyWord
     * @return
     */
    @Override
    public List<CustomerSourceResp> innerQueryCustomerByName(String customerNameKeyWord) {
        log.error("根据关键字查询客户信息查询失败");
        throw new BusinessException("根据关键字查询客户信息查询失败", "-1");
    }

    @Override
    public List<CustomerSourceResp> innerQueryCustomerByIdList(List<Long> idList) {
        log.error("根据Id列表查询多个客户信息失败");
        throw new BusinessException("根据Id列表查询多个客户信息失败", "-1");
    }

    @Override
    public List<HouseInfoVo> listHouseInfoByIds(List<Long> houseIdList) {
        log.error("根据房源ID查询房源信息失败");
        throw new BusinessException("根据房源ID查询房源信息失败", "-1");
    }

    /**
     * 根据权限ID集合查询房源信息
     * @param permissionIdList
     * @return
     */
    @Override
    public List<Long> listHouseByPermissionIdList(List<Long> permissionIdList) {
        log.error("根据权限ID集合查询房源信息失败");
        throw new BusinessException("根据权限ID集合查询房源信息失败", "-1");
    }

    @Override
    public List<Long> getHouseIdByCommunityKeyWord(String keyWord) {
        log.error("根据关键字查询房源信息失败");
        throw new BusinessException("根据关键字查询房源信息失败", "-1");
    }

    @Override
    public List<Long> queryCommunityIdByNameKeyWord(String communityKeyWord) {
        log.error("根据项目关键字查询项目ID失败");
        throw new BusinessException("根据项目关键字查询项目ID失败", "-1");
    }

    /**
     * 根据Id列表返回多个项目信息
     * @param communityIds
     * @return
     */
    @Override
    public List<CommunityResp> getCommunityListByIds(List<Long> communityIds) {
        log.error("根据Id列表返回多个项目信息失败");
        throw new BusinessException("根据Id列表返回多个项目信息失败", "-1");
    }

    /**
     * 客源修改(内部调用)
     * @param customerId
     * @param status
     * @param properties
     * @return
     */
    @Override
    public Integer innerUpdateCustomerStatus(Long customerId, String status, String properties) {
        log.error("客源修改失败");
        throw new BusinessException("客源修改失败", "-1");
    }

    /**
     * 修改房源的状态
     * @param houseId
     * @param rentStatus
     * @param houseProp
     * @return
     */
    @Override
    public Integer innerUpdateHouseStatus(Long houseId, String rentStatus, String houseProp) {
        log.error("修改房源的状态失败");
        throw new BusinessException("修改房源的状态失败", "-1");
    }

    /**
     * 根据业务Id查询操作日志
     * @param businessIdList
     * @return
     */
    @Override
    public List<OperateLogResp> queryOperateLogByBusinessId(List<Long> businessIdList) {
        log.error("根据业务Id查询操作日志失败");
        throw new BusinessException("根据业务Id查询操作日志失败", "-1");
    }

    @Override
    public Integer innerSaveLog(OperateLogResp operateLogResp) {
        log.error("新增操作日志失败");
        throw new BusinessException("新增操作日志失败", "-1");
    }

	@Override
	public void innerSaveContractFile(ContractFileInfoParam contractFileInfoParam) {
		log.error("新增合同签约附件失败");
        throw new BusinessException("新增合同签约附件失败", "-1");
	}

	@Override
	public Map<Long, List<DataFileInfoVo>> listHouseCoverUrl(List<Long> houseIdList) {
		log.error("查询房源封面图片信息失败");
        throw new BusinessException("查询房源封面图片信息失败", "-1");
	}

    @Override
    public List<NewHouseResp> getNewHouseByIdList(List<Long> idList) {
        log.error("根据新房项目ID 查询失败");
        throw new BusinessException("根据新房项目ID 查询失败", "-1");
    }

    @Override
    public List<ParkingSpaceResp> getParkingSpaceByIdList(List<Long> idList) {
        log.error("根据Id查询车位信息失败");
        throw new BusinessException("根据Id查询车位信息失败", "-1");
    }

	@Override
	public EnterpriseVo queryEnterprise(Long id) {
		log.error("居间方公司信息查询信息失败");
        throw new BusinessException("居间方公司信息查询失败", "-1");
	}

	@Override
	public List<EnterpriseResp> listEnterprise() {
		log.error("居间方公司列表信息查询信息失败");
        throw new BusinessException("居间方公司列表信息查询失败", "-1");
	}

    @Override
    public List<Long> getParkingSpaceIdByCommunityKeyWord(String keyWord) {
        log.error("根据关键字查询车位信息失败");
        throw new BusinessException("根据关键字查询车位信息失败", "-1");
    }
    
    @Override
	public List<Long> getParkingSpaceByKeyWord(String keyWord) {
		log.error("根据车位关键字查询车位信息失败");
        throw new BusinessException("根据车位关键字查询车位信息失败", "-1");
	}

    @Override
    public CustomerSourceResp innerQueryCustomerByMobile(String mobile) {
        log.error("根据手机号 查询客户信息查询失败");
        throw new BusinessException("根据手机号 查询客户信息查询失败", "-1");
    }
    public Map<Long, List<Long>> batchFindTargetRoleUserIdByOrgId(List<Long> orgIdList, String roleNameKeyWord) {
        log.error("批量查询组织目标角色用户失败");
        throw new BusinessException("批量查询组织目标角色用户失败", "-1");
    }

	@Override
	public ResultVo<Void> batchPushTip(List<UserTipParam> userTipParams) {
		log.error("批量插入用户提醒信息失败");
        throw new BusinessException("批量插入用户提醒信息失败", "-1");
	}

	@Override
	public List<OrgDetailsResp> findOrgDetailsByIds(List<Long> orgIds) {
		log.error("批量查询组织信息失败");
        throw new BusinessException("批量查询组织信息失败", "-1");
	}


    @Override
    public List<CostItemInfoResp> queryBySfCostItemCode(List<String> sfCostItemCodeList) {
        log.info("根据收费系统收费项code查询记录(微服务内部调用)失败");
        throw new BusinessException("根据收费系统收费项code查询记录(微服务内部调用)失败", "-1");
    }

    @Override
    public List<CostItemInfoResp> queryByZsCostItemCode(List<String> zsCostItemCodeList) {
        log.info("根据租售系统收费项code查询记录(微服务内部调用)失败");
        throw new BusinessException("根据租售系统收费项code查询记录(微服务内部调用)失败", "-1");
    }

    @Override
    public CostItemInfoResp queryOne(String zsCostItemCode, String zsCostItemName) {
        log.info("根据租售收费项code 查询收费配置信息 (微服务内部调用)失败");
        throw new BusinessException("根据租售收费项code 查询收费配置信息 (微服务内部调用)失败", "-1");
    }

	@Override
	public ContractNumberResp getByContractNumber(String contractNumber, Long companyId) {
		log.info("根据合同编号查询合同信息失败");
        throw new BusinessException("根据合同编号查询合同信息失败", "-1");
	}

	@Override
	public Integer updateContractNumber(ContractNumberParam params) {
		log.info("更新合同编号信息失败");
        throw new BusinessException("更新合同编号信息失败", "-1");
	}

	@Override
	public Integer updateContractNumberRule(ContractNumberParam params) {
		log.info("更新合同编号规则信息失败");
        throw new BusinessException("更新合同编号规则信息失败", "-1");
	}

    @Override
    public TenthsDetailsResp getDetailByOrgIdAndBusinessType(Long orgId, String businessType) {
        log.info("分成设置详情失败");
        throw new BusinessException("分成设置详情失败", "-1");
    }

    @Override
    public Map<String, TransactionRelResp> queryTransactionRelAll() {
        log.info("成交类型配置映射信息查询失败");
        throw new BusinessException("成交类型配置映射信息查询失败", "-1");
    }


    @Override
    public Long findDistrictIdByUserId(Long storeId) {
        log.info("查询用户所在门店的区id (微服务调用)失败");
        throw new BusinessException("查询用户所在门店的区id (微服务调用)失败", "-1");
    }

    @Override
    public Long findRegionIdByUserId(Long storeId) {
        log.info("查询用户所在门店的大区id (微服务调用)失败");
        throw new BusinessException("查询用户所在门店的大区id (微服务调用)失败", "-1");
    }
}
