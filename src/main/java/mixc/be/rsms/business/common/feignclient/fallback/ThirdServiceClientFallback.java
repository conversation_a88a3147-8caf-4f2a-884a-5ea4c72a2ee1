package mixc.be.rsms.business.common.feignclient.fallback;

import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.feignclient.ThirdServiceClient;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.third.JoyContractAttachementResp;
import mixc.be.rsms.pojo.third.JoyContractSignedResp;
import mixc.be.rsms.pojo.third.JoyCreateContractParam;
import mixc.be.rsms.pojo.third.JoyRecallParam;
import mixc.be.rsms.pojo.third.JoyResultResp;
import mixc.be.rsms.pojo.third.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 朝昔创建合同feign接口
 * <AUTHOR>
 */
@Slf4j
@Component
public class ThirdServiceClientFallback implements ThirdServiceClient {

	@Override
	public ResultVo<List<String>> createJoyContract(JoyCreateContractParam joyCreateContractParam) {
		log.error("朝昔创建合同失败");
		throw new BusinessException("朝昔创建合同失败", "-1");
	}
	
	@Override
	public JoyResultResp<String> updateJoyContract(JoyCreateContractParam createContractParam) {
		log.error("朝昔合同保存签署参数失败");
		throw new BusinessException("朝昔合同保存签署参数失败", "-1");
	}

	@Override
	public JoyResultResp<String> generateContrackLock(String joyContractId, String accessToken) {
		log.error("生成契约锁合同文件失败");
		throw new BusinessException("生成契约锁合同文件失败", "-1");
	}

	@Override
	public JoyResultResp<String> recallJoyContract(JoyRecallParam joyRecallParam) {
		log.error("撤回朝昔合同失败");
		throw new BusinessException("撤回朝昔合同失败", "-1");
	}

	@Override
	public JoyResultResp<String> getJoyContractLockUrl(String joyContractId, String customerId, String accessToken) {
		log.error("获取契约锁签约地址失败");
		throw new BusinessException("获取契约锁签约地址失败", "-1");
	}

	@Override
	public JoyContractSignedResp joyContractDetail(String joyContractId) {
		log.error("获取朝昔合同详情失败");
		throw new BusinessException("获取朝昔合同详情失败", "-1");
	}

	@Override
	public JoyContractFieldInfoResp getContractFieldInfo(String joyContractId) {
		log.error("合同填写字段查询接口失败");
		throw new BusinessException("合同填写字段查询接口失败", "-1");
	}

	@Override
	public String getJoyAccessToken() {
		log.error("获取朝昔api-token失败");
		throw new BusinessException("获取朝昔api-token失败", "-1");
	}

	@Override
	public JoyContractAttachementResp getJoyContractAttachementUrl(String joyContractId) {
		log.error("获取朝昔合同附件信息失败");
		throw new BusinessException("获取朝昔合同附件信息失败", "-1");
	}

	
}
