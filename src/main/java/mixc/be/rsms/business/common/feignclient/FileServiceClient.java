package mixc.be.rsms.business.common.feignclient;

import mixc.be.rsms.business.common.feignclient.fallback.FileServiceClientFallback;
import mixc.be.rsms.pojo.file.DownloadFile;
import mixc.be.rsms.pojo.file.FileInfo;
import mixc.be.rsms.pojo.file.UploadFile;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.net.URL;
import java.util.List;
import java.util.Map;

@FeignClient(name = "rsms-third", fallback = FileServiceClientFallback.class)
public interface FileServiceClient {

	/**
	 * 通过文件ID集合获取文件预览URL
	 * @param fileKeys 文件ID集合
	 * @return
	 */
	@PostMapping("/file/presigned")
	List<FileInfo> queryByFileKeys(@RequestBody List<String> fileKeys);
	
	/**
	 * 文件上传
	 * @param uploadParam 文件上传参数对象
	 * @return
	 */
	@PostMapping("/file/upload")
	URL uploadFile(@RequestBody UploadFile uploadFile);
	
	/**
	 * 获取单个文件的内容
	 * @param fileName 文件名称
	 * @param fileKey 文件在S3的KEY
	 * @return
	 */
	@GetMapping("/file/getFileByte")
	byte[] getFileByteForSingleFile(@RequestParam String fileName, @RequestParam String fileKey);
	
	/**
	 * 获取批量下载的文件内容
	 * @param downloadFiles 下载文件列表 
	 * @return
	 */
	@PostMapping(value = "/file/getBatchFileByte")
	Map<String, byte[]> getBatchFileByteForSingleFile(@RequestBody DownloadFile downloadFile);
	
	/**
	 * 删除S3文件
	 * @param fileKeys 文件KEY集合
	 * @return
	 */
	@DeleteMapping("/file/delete")
	void deleteS3File(@RequestParam List<String> fileKeys);
}
