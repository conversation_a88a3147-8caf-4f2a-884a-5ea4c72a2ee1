package mixc.be.rsms.business.common.feignclient;

import io.swagger.v3.oas.annotations.Operation;
import mixc.be.rsms.business.common.feignclient.fallback.DataServerClientFallback;
import mixc.be.rsms.business.domain.dto.ContractFileInfoParam;
import mixc.be.rsms.business.domain.dto.ContractNumberParam;
import mixc.be.rsms.business.domain.dto.OrgDetailsResp;
import mixc.be.rsms.business.vo.DataFileInfoVo;
import mixc.be.rsms.business.vo.EnterpriseVo;
import mixc.be.rsms.business.vo.HouseInfoVo;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.data.*;
import mixc.be.rsms.pojo.file.FileInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/05
 */
@FeignClient(name = "rsms-data", fallback = DataServerClientFallback.class)
public interface DataServerClient {
    /**
     * 文件预览
     *
     * @param fileId
     * @return
     */
    @GetMapping("/file/preview")
    ResultVo<List<FileInfo>> previewFile(@RequestParam Long fileId);

    /**
     * 根据 多个业务Id 查询 文件信息
     * @param businessIdList
     * @return
     */
    @GetMapping("/file/findByBusinessIdList")
    ResultVo<List<DataFileInfoVo>> findByBusinessIdList(@RequestParam List<Long> businessIdList);

    /**
     * 批量保存文件信息
     * @param dtoList
     * @return
     */
    @PostMapping("/file/innerBatchSave")
    ResultVo<Integer> innerBatchSave(@RequestBody List<FileInfoResp> dtoList);

    /**
     * 根据 业务ID 删除
     *
     * @param bussinessIdList
     */
    @DeleteMapping("/file/deleteByBusinessIdList")
    ResultVo<Integer> deleteByBusinessIdList(@RequestBody List<Long> bussinessIdList);

    /**
     * 根据业务主键、业务类型、文件类型和是否预览查询文件信息
     * @param businessId
     * @param fileType
     * @param isPresigned
     * @return
     */

    @GetMapping("/file/listFileInfo")
    ResultVo<List<DataFileInfoVo>> listFileInfo(
            @RequestParam Long businessId,
            @RequestParam String fileType,
            @RequestParam Boolean isPresigned);


    /**
     * 根据关键字查询客户信息查询(微服务调用)
     *
     * @param customerNameKeyWord
     * @return
     */
    @GetMapping("/customerSource/innerQueryCustomerByName")
    List<CustomerSourceResp> innerQueryCustomerByName(@RequestParam String customerNameKeyWord);

    /**
     * 根据手机号 查询客户信息查询(微服务调用)
     *
     * @param mobile
     * @return
     */
    @GetMapping("/customerSource/innerQueryCustomerByMobile")
    CustomerSourceResp innerQueryCustomerByMobile(@RequestParam String mobile);

    /**
     * 根据Id列表查询多个客户信息
     *
     * @param idList
     * @return
     */
    @GetMapping("/customerSource/innerQueryCustomerByIdList")
    List<CustomerSourceResp> innerQueryCustomerByIdList(@RequestParam List<Long> idList);


    /**
     * 根据房源ID查询房源信息
     * @param houseIdList
     * @return
     */
    @GetMapping("/house/listHouseInfoByIds")
    List<HouseInfoVo> listHouseInfoByIds(@RequestParam("houseIdList") List<Long> houseIdList);

    /**
     * 根据权限ID集合查询房源信息
     *
     * @param permissionIdList
     * @return
     */
    @GetMapping("/house/listHouseByPermissionIdList")
    List<Long> listHouseByPermissionIdList(@RequestParam("permissionIdList") List<Long> permissionIdList);


    /**
     * 根据关键字查询房源信息
     * @param keyWord
     * @return
     */
    @GetMapping("/house/getHouseIdByCommunityKeyWord")
    List<Long> getHouseIdByCommunityKeyWord(@RequestParam("keyWord") String keyWord);


    /**
     * 根据关键字查询车位信息
     * @param keyWord
     * @return
     */
    @GetMapping("/parkingSpace/getParkingSpaceIdByCommunityKeyWord")
    List<Long> getParkingSpaceIdByCommunityKeyWord(@RequestParam("keyWord") String keyWord);

    /**
     * 根据项目关键字查询项目ID
     *
     * @param communityKeyWord
     * @return
     */
    @GetMapping("/community/queryCommunityIdByNameKeyWord")
    List<Long> queryCommunityIdByNameKeyWord(@RequestParam("communityKeyWord") String communityKeyWord);

    /**
     * 根据Id列表返回多个项目信息
     * @param communityIds
     * @return
     */
    @GetMapping("/community/getListByIds")
    List<CommunityResp> getCommunityListByIds(@RequestParam("communityIds") List<Long> communityIds);


    /**
     * 客源修改(微服务调用)
     *
     * @param customerId
     * @param status
     * @param properties
     * @return
     */
    @PostMapping("/customerSource/innerUpdateCustomerStatus")
    Integer innerUpdateCustomerStatus(@RequestParam(required = true) Long customerId,
                                      @RequestParam(required = false) String status,
                                      @RequestParam(required = false) String properties);

    /**
     * 修改房源的状态
     * @param houseId
     * @param rentStatus
     * @param houseProp
     * @return
     */
    @PostMapping("/house/innerUpdateHouseStatus")
    Integer innerUpdateHouseStatus(@RequestParam("houseId") Long houseId,
                                          @RequestParam("rentStatus") String rentStatus,
                                          @RequestParam("houseProp") String houseProp);

    /**
     * 根据业务Id查询操作日志
     *
     * @param businessIdList
     * @return
     */
    @GetMapping("/operate/log/queryByBusinessId")
    List<OperateLogResp> queryOperateLogByBusinessId(@RequestParam List<Long> businessIdList);

    /**
     * 新增操作日志
     * @param operateLogResp
     * @return
     */
    @PostMapping("/operate/log/innerSaveLog")
    Integer innerSaveLog(@RequestBody OperateLogResp operateLogResp);
    
    /**
     * 合同签约附件新增
     * @param contractFileInfoParam 附件参数
     */
    @PostMapping("/contract/file/add")
    void innerSaveContractFile(@RequestBody ContractFileInfoParam contractFileInfoParam);

    /**
     * 查询房源列表封面图片
     * @param houseIdList 房源ID集合
     * @return
     */
    @GetMapping("/house/file/listHouseCoverUrl")
    Map<Long, List<DataFileInfoVo>> listHouseCoverUrl(@RequestParam List<Long> houseIdList);

    /**
     * 根据新房项目ID 查询
     * @param idList
     * @return
     */
    @GetMapping("/newHouse/getByIdList")
    List<NewHouseResp> getNewHouseByIdList(@RequestParam List<Long> idList);

    /**
     * 根据Id查询车位信息
     * @param idList
     * @return
     */
    @GetMapping("/parkingSpace/getByIdList")
    List<ParkingSpaceResp> getParkingSpaceByIdList(@RequestParam("idList") List<Long> idList);
    
    /**
     * 根据关键字查询车位信息
     * @param keyWord 车位关键字
     * @return
     */
    @GetMapping("/parkingSpace/getParkingSpaceIdByCommunityKeyWord")
    List<Long> getParkingSpaceByKeyWord(@RequestParam("keyWord") String keyWord);
    
    /**
     * 居间方公司详情查询
     * @param id 主键
     * @return
     */
    @GetMapping("/enterprise/innerEnterpriseDetail")
    EnterpriseVo queryEnterprise(@RequestParam Long id);
    
    /**
     * 居间方公司查询
     * @return
     */
    @GetMapping("/enterprise/appQuery")
    List<EnterpriseResp> listEnterprise();

    @GetMapping(value = "/org/batchFindTargetRoleUserIdByOrgId")
    Map<Long, List<Long>> batchFindTargetRoleUserIdByOrgId(@RequestParam List<Long> orgIdList, @RequestParam String roleNameKeyWord);

    /**
     * 推送用户通知(微服务内部调用)
     * @param userTipParams
     * @return
     */
    @PostMapping("/userTip/batchPushTip")
    ResultVo<Void> batchPushTip(@RequestBody List<UserTipParam> userTipParams);


    /**
     * 根据收费系统收费项code查询记录(微服务内部调用)
     * @param sfCostItemCodeList
     * @return
     */
    @GetMapping("/finances/costItemInfo/queryBySfCostItemCode")
    List<CostItemInfoResp> queryBySfCostItemCode(@RequestParam("sfCostItemCodeList") List<String> sfCostItemCodeList);


    /**
     * 根据收费系统收费项code查询记录(微服务内部调用)
     * @param zsCostItemCodeList
     * @return
     */
    @GetMapping("/finances/costItemInfo/queryByZsCostItemCode")
    List<CostItemInfoResp> queryByZsCostItemCode(@RequestParam("zsCostItemCodeList") List<String> zsCostItemCodeList);


    /**
     * 根据 租售 Code 和 收费ID、租售名称查询
     * @param zsCostItemCode    租售系统 收费项code
     * @param zsCostItemName 租售系统收费项名称
     * @return
     */
    @GetMapping("/finances/costItemInfo/queryOne")
    CostItemInfoResp queryOne(@RequestParam(name = "zsCostItemCode") String zsCostItemCode,
                              @RequestParam(name = "zsCostItemName", required = false) String zsCostItemName);


    @Operation(summary = "查询组织详情")
    @GetMapping("/org/findOrgDetailsByIds")
    List<OrgDetailsResp> findOrgDetailsByIds(@RequestParam List<Long> ids);
    
    /**
     * 
     * @param contractNumber
     * @param companyId
     * @return
     */
    @GetMapping("/finances/contractNumber/getByContractNumber")
    ContractNumberResp getByContractNumber(@RequestParam String contractNumber, @RequestParam Long companyId);
    
    /**
     * 更新合同号
     * @param params
     */
    @PostMapping("/finances/contractNumber/updateContractNumber")
    Integer updateContractNumber(@RequestBody ContractNumberParam params);
    
    /**
     * 更新合同号
     * @param params
     */
    @PostMapping("/finances/contractNumber/updateContractNumberRule")
    Integer updateContractNumberRule(@RequestBody ContractNumberParam params);

    /**
     * 分成设置详情(微服务调用)
     *
     * @param orgId        所属公司ID
     * @param businessType 成交分类编码
     */
    @GetMapping("/finances/clinch/tenths/getDetailByOrgIdAndBusinessType")
    TenthsDetailsResp getDetailByOrgIdAndBusinessType(@RequestParam("orgId") Long orgId,
                                                      @RequestParam("businessType") String businessType);


    /**
     * 成交类型配置映射信息查询
     *
     * @return
     */
    @GetMapping("/transactionRel/queryAll")
    Map<String, TransactionRelResp> queryTransactionRelAll();


    /**
     * 查询用户所在门店的区id (微服务调用)
     *
     * @param storeId
     * @return
     */
    @GetMapping(value = "/org/findDistrictIdByUserId")
    Long findDistrictIdByUserId(@RequestParam("storeId") Long storeId);

    /**
     * 查询用户所在门店的大区id (微服务调用)
     *
     * @param storeId
     * @return
     */
    @GetMapping(value = "/org/findRegionIdByUserId")
    Long findRegionIdByUserId(@RequestParam("storeId") Long storeId);
}
