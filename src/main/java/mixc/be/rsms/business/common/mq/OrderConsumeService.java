package mixc.be.rsms.business.common.mq;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.enums.BusinessEnum;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.domain.pojo.TbBusinessOrderCallback;
import mixc.be.rsms.business.mapper.TbBusinessOrderCallbackMapper;
import mixc.be.rsms.business.service.IOrderService;
import mixc.be.rsms.common.constant.RsmsConstant;
import mixc.be.rsms.common.enums.CommonEnum;
import mixc.be.rsms.common.enums.DropdownEnum;
import mixc.be.rsms.common.utils.JacksonUtil;
import mixc.be.rsms.common.utils.RequestAttributesUtil;
import mixc.be.rsms.pojo.business.MessageContent;
import mixc.be.rsms.pojo.third.SFOrderCallBackParam;
import mixc.be.rsms.pojo.third.SFOrderRefundCallBackParam;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024-10-15 14:29
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrderConsumeService implements ApplicationListener<MessageReceivedEvent> {
    private final IOrderService orderService;
    private final TbBusinessOrderCallbackMapper callback;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onApplicationEvent(MessageReceivedEvent event) {
        String traceId = UUID.randomUUID().toString();
        // 设置自定义的 请求头
        RequestAttributesUtil.setCustomizeRequestAttributes(Map.of(
                CommonEnum.TRACE_ID.getCode(), traceId,
                CommonEnum.LAUNCH_SQUARE.getCode(), RsmsConstant.LAUNCH_SQUARE_MQ
        ));
        MDC.put(CommonEnum.TRACE_ID.getCode(), traceId);
        if (event.getMessageEnum().getCode().equals(MessageEnum.CHARGE_ORDER_DATA.getCode())) {
            try {
                log.info("收到 MQ 消息：{} 开始处理......", JacksonUtil.toJSON(event.getMessage()));
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                MessageContent<List<Long>> messageContent = JacksonUtil.fromJSON(event.getMessage(), new TypeReference<MessageContent<List<Long>>>() {
                });
                log.info("收到 MQ 消息content :{}", JacksonUtil.toJSON(messageContent));
                if (ObjectUtils.isEmpty(messageContent)) {
                    log.info("收到 MQ 消息content 为空..");
                    throw new BusinessException(BusinessEnum.ORDER_MQ_ERROR);
                }
                // 退款MQ 消息处理
                if (DropdownEnum.MQ_MESSAGE_TYPE_REFUND.getDictKey().equals(messageContent.getType())) {
                    log.info("当前 MQ 消息为退款消息....");
                    List<TbBusinessOrderCallback> tbBusinessOrderCallbacks = callback.selectBatchIds(messageContent.getData());
                    tbBusinessOrderCallbacks.forEach(orderCallback -> {
                        try {
                            SFOrderRefundCallBackParam refundCallBackParam = JacksonUtil.fromJSON(orderCallback.getBody(), new TypeReference<SFOrderRefundCallBackParam>() {
                            });
                            if (ObjectUtils.isEmpty(refundCallBackParam)) {
                                log.info("退款回调报文反序列化结果为空。。。。。。");
                                throw new BusinessException(BusinessEnum.ORDER_MQ_ERROR);
                            }
                            orderService.handleRefundCallBack(refundCallBackParam.getOutRefundNo(),
                                    refundCallBackParam.getRefundResult(),
                                    refundCallBackParam.getRefundCompletionTime(),
                                    refundCallBackParam.getRefundAmount().divide(new BigDecimal("100")));
                        } catch (Exception e) {
                            log.error("处理退款回调信息异常....", e);
                            throw new RuntimeException(e);
                        }
                    });
                } else { // 收款MQ 消息处理
                    log.info("当前 MQ 消息为收款消息....");
                    List<TbBusinessOrderCallback> tbBusinessOrderCallbacks = callback.selectBatchIds(messageContent.getData());
                    tbBusinessOrderCallbacks.forEach(orderCallback -> {
                        log.info("开始处理回调：主流水号：{} 子流水号：{} 回调记录：{}", orderCallback.getOutTradeNo(), orderCallback.getSubOutTradeNo(), orderCallback);
                        try {
                            SFOrderCallBackParam callBackParam = JacksonUtil.fromJSON(orderCallback.getBody(), new TypeReference<SFOrderCallBackParam>() {
                            });
                            if (ObjectUtils.isEmpty(callBackParam)) {
                                log.error("处理回调记录异常：回调信息：{}", orderCallback);
                                throw new BusinessException(BusinessEnum.ORDER_PAY_CALLBACK_ERROR);
                            }
                            // 线下转账 回调
                            if (DropdownEnum.SF_PAY_CHANNEL_TRANSFER_OFFLINE.getDictKey().equals(callBackParam.getPayChannel())) {
                                orderService.handlePayCallBack(orderCallback.getOutTradeNo(),
                                        orderCallback.getSubOutTradeNo(),
                                        callBackParam.getTradeState(),
                                        callBackParam.getPayChannel(),
                                        callBackParam.getPayFinishTime(),
                                        orderCallback.getTotalFee().divide(new BigDecimal("100.00")));
                            } else if (DropdownEnum.SF_PAY_CHANNEL_CMB.getDictKey().equals(callBackParam.getPayChannel())) {
                                // 招行聚合支付
                                orderService.handleThirdPayCallBack(false,
                                        callBackParam.getTransactionId(),
                                        orderCallback.getOutTradeNo(),
                                        orderCallback.getSubOutTradeNo(),
                                        callBackParam.getTradeState(),
                                        callBackParam.getPayChannel(),
                                        callBackParam.getPayFinishTime(),
                                        !ObjectUtils.isEmpty(callBackParam.getTotalFee()) ? callBackParam.getTotalFee().divide(new BigDecimal("100.00")) : new BigDecimal("0.00"));
                            } else { // 其他类型均为聚合支付
                                orderService.handlePayCallBack(orderCallback.getOutTradeNo(),
                                        orderCallback.getSubOutTradeNo(),
                                        callBackParam.getTradeState(),
                                        callBackParam.getPayChannel(),
                                        callBackParam.getPayFinishTime(),
                                        orderCallback.getTotalFee().divide(new BigDecimal("100.00")));
                            }
                        } catch (Exception e) {
                            log.error("处理收款回调信息异常....", e);
                            throw new RuntimeException(e);
                        }
                    });
                }
            } catch (Exception e) {
                log.error("MQ接收的订单回调修改状态消息格式异常,消息体={},异常信息={}", event.getMessage(), e.getMessage());
                throw new BusinessException(BusinessEnum.ORDER_PAY_CALLBACK_ERROR);
            }
        }

    }

}