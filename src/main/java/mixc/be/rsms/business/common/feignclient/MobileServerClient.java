package mixc.be.rsms.business.common.feignclient;

import mixc.be.rsms.business.common.feignclient.fallback.MobileServerClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @description
 * @date 2025/03/25
 */
@FeignClient(name = "rsms-mobile", fallback = MobileServerClientFallback.class)
public interface MobileServerClient {

    /**
     * 根据手机号查询用户的 OpenId
     * @param mobile
     * @return
     */
    @GetMapping("/public/wx/getOpenIdByMobile")
    String getOpenIdByMobile(@RequestParam String mobile);
}
