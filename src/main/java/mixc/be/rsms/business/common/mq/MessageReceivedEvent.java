package mixc.be.rsms.business.common.mq;

import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2024-10-15 14:25
 */

public class MessageReceivedEvent extends ApplicationEvent {

    private final String message;

    private final MessageEnum messageEnum;

    public MessageReceivedEvent(Object source, String message, MessageEnum messageEnum) {
        super(source);
        this.message = message;
        this.messageEnum = messageEnum;
    }

    public String getMessage() {
        return message;
    }

    public MessageEnum getMessageEnum() {
        return messageEnum;
    }
}
