package mixc.be.rsms.business.common.enums;

import lombok.Getter;

/**
 * @Classname ResultEnum
 * @Date 2021/3/17 4:31 下午
 * @Created by wangxy
 */
@Getter
public enum ResultEnum {

    OK("200", "OK"),
    ERROR("500", "error"),
    PARAM_ERROR("400","param error"),
    AUTH_DENY("401", "not access"),;

    private String code;
    private String msg;

    ResultEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
