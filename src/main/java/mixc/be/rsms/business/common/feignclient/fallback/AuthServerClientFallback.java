package mixc.be.rsms.business.common.feignclient.fallback;

import java.util.List;

import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.feignclient.AuthorServerClient;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.author.UserDetailsMicroResp;
import mixc.be.rsms.pojo.third.EmployeeInfoResponse;

@Slf4j
public class AuthServerClientFallback implements AuthorServerClient {

	@Override
	public List<Long> getUserPermissionByCode(String buttonCode) {
		log.error("查询员工权限失败");
		throw new BusinessException("查查询员工权限失败", "-1");
	}
	
	@Override
	public List<Long> getUserPermissionByCode(String buttonCode, String resTypeCode) {
		log.error("根据权限码和授权类型查询员工权限失败");
		throw new BusinessException("根据权限码和授权类型查询员工权限失败", "-1");
	}

	@Override
	public Boolean getUserRsmsStatusEnableExists(List<Long> userIds) {
		log.error("查询是否存在租售状态为启用的数据失败");
		throw new BusinessException("查询是否存在租售状态为启用的数据失败", "-1");
	}

	@Override
	public List<UserDetailsMicroResp> getUserDetailsMicro(List<Long> userIds) {
		log.error("查询用户信息失败");
		throw new BusinessException("查询用户信息失败", "-1");
	}

	@Override
	public List<Long> getUserIdsByUserName(String userName) {
		log.error("查询用户ID信息失败");
		throw new BusinessException("查询用户ID信息失败", "-1");
	}

	@Override
	public List<String> getUserAllPermissions(Long userId) {
		log.error("获取用户权限集合字符码失败");
		throw new BusinessException("获取用户权限集合字符码失败", "-1");
	}

	@Override
	public void userSaveBatch(List<EmployeeInfoResponse> employees) {
		log.error("批量保存员工数据失败");
		throw new BusinessException("批量保存员工数据失败", "-1");
	}

	@Override
	public void userUpdateBatch(List<EmployeeInfoResponse> employees) {
		log.error("批量更新员工数据失败");
		throw new BusinessException("批量更新员工数据失败", "-1");
	}
	
	@Override
	public ResultVo<Boolean> empLogout() {
		log.error("员工登出失败");
		throw new BusinessException("员工登出失败", "-1");
	}

}
