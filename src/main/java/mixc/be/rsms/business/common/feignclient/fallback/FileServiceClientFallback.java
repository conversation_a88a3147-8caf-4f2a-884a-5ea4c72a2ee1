package mixc.be.rsms.business.common.feignclient.fallback;

import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.feignclient.FileServiceClient;
import mixc.be.rsms.pojo.file.DownloadFile;
import mixc.be.rsms.pojo.file.FileInfo;
import mixc.be.rsms.pojo.file.UploadFile;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * 文件预览服务feign接口
 * <AUTHOR>
 */
@Slf4j
public class FileServiceClientFallback implements FileServiceClient {

	@Override
	public List<FileInfo> queryByFileKeys(List<String> fileKeys) {
		log.error("生成文件预览URL失败");
		throw new BusinessException("生成文件预览URL失败", "-1");
	}

	@Override
	public void deleteS3File(List<String> fileKeys) {
		log.error("删除文件失败");
		throw new BusinessException("删除文件失败", "-1");
	}

	@Override
	public byte[] getFileByteForSingleFile(String fileName, String fileKey) {
		log.error("文件：{} 下载失败", fileName);
		throw new BusinessException("文件下载失败", "-1"); 
	}

	@Override
	public Map<String, byte[]> getBatchFileByteForSingleFile(DownloadFile downloadFile) {
		log.error("文件批量下载失败");
		throw new BusinessException("文件批量下载失败", "-1");
	}

	@Override
	public URL uploadFile(UploadFile uploadFile) {
		log.error("文件上传失败");
		throw new BusinessException("文件上传失败", "-1");
	}

	
}
