package mixc.be.rsms.business.common.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/04/29
 */
@Configuration
@ConfigurationProperties(prefix = "report")
@RefreshScope
@Data
public class TransactionCostItemConfig {

    private List<TransactionType2CostItem> transactionType2CostItem;



    @Getter
    @Setter
    public static class TransactionType2CostItem{
        // 成交报告 分类
        private String category;
        // 收费项 code
        private String costItemCode;
    }
}
