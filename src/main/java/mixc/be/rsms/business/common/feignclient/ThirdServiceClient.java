package mixc.be.rsms.business.common.feignclient;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import mixc.be.rsms.business.common.feignclient.fallback.FileServiceClientFallback;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.third.JoyContractAttachementResp;
import mixc.be.rsms.pojo.third.JoyContractFieldInfoResp;
import mixc.be.rsms.pojo.third.JoyContractSignedResp;
import mixc.be.rsms.pojo.third.JoyCreateContractParam;
import mixc.be.rsms.pojo.third.JoyRecallParam;
import mixc.be.rsms.pojo.third.JoyResultResp;

@FeignClient(name = "rsms-third", contextId = "rsms-third-zx", fallback = FileServiceClientFallback.class)
public interface ThirdServiceClient {

	/**
	 * 创建朝昔合同并保存签署参数
	 * @param createContractParam 创建朝昔合同参数
	 * @return
	 */
	@PostMapping("/contract/createJoyContract")
	ResultVo<List<String>> createJoyContract(@RequestBody JoyCreateContractParam createContractParam);
	
	/**
	 * 创建朝昔合同并保存签署参数
	 * @param updateSignParam 签署参数对象
	 * @return
	 */
	@PostMapping("/contract/updateJoyContract")
	JoyResultResp<String> updateJoyContract(@RequestBody JoyCreateContractParam updateSignParam);
	
	/**
	 * 生产契约锁合同文件
	 * @param joyContractId 朝昔合同ID
	 * @param accessToken token
	 * @return
	 */
	@GetMapping("/contract/generateContrackLock")
	JoyResultResp<String> generateContrackLock(@RequestParam String joyContractId, @RequestParam String accessToken);
	
	/**
	 * 撤回朝昔合同
	 * @param joyRecallParam 朝昔撤回参数对象
	 * @return
	 */
	@PostMapping("/contract/recallJoyContract")
	JoyResultResp<String> recallJoyContract(@RequestBody JoyRecallParam joyRecallParam);
	
	/**
	 * 获取契约锁签约地址
	 * @param joyContractId 朝昔合同ID
	 * @param customerId 客户ID
	 * @param accessToken token
	 * @return
	 */
	@GetMapping("/contract/getJoyContractLockUrl")
	JoyResultResp<String> getJoyContractLockUrl(@RequestParam String joyContractId, @RequestParam String customerId, @RequestParam String accessToken);
	
	/**
	 * 获取朝昔合同详情
	 * @param joyContractId 朝昔合同ID
	 * @param accessToken token
	 * @return
	 */
	@GetMapping("/contract/joyContractDetail")
	JoyContractSignedResp joyContractDetail(@RequestParam String joyContractId);

	/**
	 * 合同填写字段查询接口
	 * @param joyContractId
	 * @return
	 */
	@GetMapping("/contract/getContractFieldInfo")
	JoyContractFieldInfoResp getContractFieldInfo(@RequestParam String joyContractId);

	/**
	 * 获取朝昔api-token
	 * @return
	 */
	@GetMapping("/contract/getJoyAccessToken")
	String getJoyAccessToken();
	
	/**
	 * 获取朝昔合同附件信息
	 * @param joyContractId 朝昔合同ID
	 * @return
	 */
	@GetMapping("/contract/getJoyContractAttachementUrl")
	JoyContractAttachementResp getJoyContractAttachementUrl(@RequestParam String joyContractId);
}
