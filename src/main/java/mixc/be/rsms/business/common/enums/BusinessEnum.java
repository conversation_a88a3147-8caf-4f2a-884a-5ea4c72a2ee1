package mixc.be.rsms.business.common.enums;

import lombok.Getter;
import mixc.be.rsms.common.service.ErrorCode;

/**
 * <AUTHOR>
 * @date 2024-10-03 16:19
 */

@Getter
public enum BusinessEnum implements ErrorCode {

     PARAMETER_EXCEPTION("PARAMETER_EXCEPTION","参数异常"),

     REQUEST_SID_IS_NOT_INVALID("REQUEST_SID_IS_NOT_INVALID","请求标识无效"),

    ORDER_PAY_CALLBACK_ERROR("ORDER_PAY_CALLBACK_ERROR", "订单回调处理异常"),

    TRANSFER_STATUS_S16_ERROR("TRANSFER_STATUS_S16_ERROR","当前过户阶段完成时，下个过户阶段只能为空"),
    
    LEASER_OWNERSHIP_ATTACHMENT_NOT_NULL("LEASER_OWNERSHIP_ATTACHMENT_NOT_NULL", "甲方房屋产权资料复印件不能为空"),
    IDENTITY_ATTACHMENT_NOT_NULL("IDENTITY_ATTACHMENT_NOT_NULL", "甲乙双方身份证明复印件不能为空"),
    HOUSE_HANDOVER_ATTACHMENT_NOT_NULL("HOUSE_HANDOVER_ATTACHMENT_NOT_NULL", "房屋交接清单不能为空"),
    CONTRACT_ID_NOT_EXIST("CONTRACT_ID_NOT_EXIST", "合同对象不存在"),
    CONTRACT_STATUS_ERROR("CONTRACT_STATUS_ERROR", "合同状态异常"),
    CONTRACT_DELETE_NOT_ALLOWED("CONTRACT_DELETE_NOT_ALLOWED", "非草稿状态不能被删除"),
    CUSTOMER_MOBILE_NOT_NULL("CUSTOMER_MOBILE_NOT_NULL", "客户手机号不能为空"),
    ASSETS_ID_NOT_NULL("ASSETS_ID_NOT_NULL", "资产ID不能为空"),
    ASSETS_INFO_NOT_EXIST("ASSETS_INFO_NOT_EXIST", "资产信息不存在"),
    CALL_JOY_CONTRACT_ERROR("CALL_JOY_CONTRACT_ERROR", "调用朝昔合同接口出错"),
    RSMS_TOKEN_ERROR("RSMS_TOKEN_ERROR", "租售端token过期或不存在"),
    JOY_API_TOKEN_EXPIRED("JOY_API_TOKEN_EXPIRED", "朝昔API-token过期"),
    DUPLICATE_CONTRACT_NUMBER("DUPLICATE_CONTRACT_NUMBER", "合同编号重复"),
    LEASER_HAD_SIGNED("LEASER_HAD_SIGNED", "甲方已经签署过了，不能再进行签署"),
    CUSTOMER_HAD_SIGNED("CUSTOMER_HAD_SIGNED", "乙方已经签署过了，不能再进行签署"),
    CONTRACT_RESIGN_NOT_ALLOWED("CONTRACT_RESIGN_NOT_ALLOWED", "仅已审核/签署中（包含待甲方签字、待乙方签字）的合同可重新发起签约"),
    LEASER_PROXY_INFO_NOT_EMPTY("LEASER_PROXY_INFO_NOT_EMPTY", "甲方代理信息不完整，请填写完整"),
    CUSTOMER_PROXY_INFO_NOT_EMPTY("CUSTOMER_PROXY_INFO_NOT_EMPTY", "乙方代理信息不完整，请填写完整"),

    ORDER_NOT_EXIST_ERROR("ORDER_NOT_EXIST_ERROR", "订单不存在'"),
    SUB_ORDER_NOT_EXIST_ERROR("SUB_ORDER_NOT_EXIST_ERROR", "子订单不存在'"),
    ORDRE_STATUS_ERROR("ORDRE_STATUS_ERROR", "订单状态异常"),
    ORDER_MQ_ERROR("ORDER_MQ_ERROR", "订单MQ消息处理异常"),
    RECEIVECOMMUNITY_NOT_EXIST_ERROR("RECEIVECOMMUNITY_NOT_EXIST_ERROR","未查询到对应的收费项Id"),
    CURRENT_ORDER_PAYING("CURRENT_ORDER_PAYING", "当前订单处于支付中"),
    ORDER_SERIAL_ERROR("ORDER_SERIAL_ERROR", "订单流水记录异常"),
    SYNC_ORDER_ERROR("SYNC_ORDER_ERROR", "同步订单异常"),
    CANCEL_TRANSFER_ORDER_ERROR("CANCEL_TRANSFER_ORDER_ERROR", "转账订单无法手动取消"),
    CANCEL_POS_PAYMENT_ERROR("CANCEL_POS_PAYMENT_ERROR", "POS订单无法手动取消"),

    DEPOSIT_STATUS_ERROR("DEPOSIT_STATUS_ERROR", "当前意向金记录状态异常"),
    DEPOSIT_DATA_NOT_AGREEMENT_ERROR("DEPOSIT_STATUS_ERROR", "类目、关联房源/车位、交易类型不一致，请确认并调整"),
    DEPOSIT_DELETE_ERROR("DEPOSIT_DELETE_ERROR", "只有没有提交过的意向金或订金可以被删除"),
    DEPOSIT_INSERT_ERROR("DEPOSIT_INSERT_ERROR", "意向金新增异常"),

    DEPOSIT_NOT_REFUNDABLE_BALANCE_ERROR("DEPOSIT_NOT_REFUNDABLE_BALANCE_ERROR",  "意向金没有可退余额异常"),
    DEPOSIT_REFUND_ERROR("DEPOSIT_REFUND_ERROR",  "意向金退款异常"),

    DEPOSIT_TYPE_PARAMS_ERROR("DEPOSIT_TYPE_PARAMS_ERROR", "类型不能为空" ),
    DEPOSIT_CATEGORY_PARAMS_NOT_NULL_ERROR("DEPOSIT_CATEGORY_PARAMS_ERROR", "类目不能为空" ),
    DEPOSIT_CUSTOMER_PARAMS_NOT_NULL_ERROR("DEPOSIT_CUSTOMER_PARAMS_ERROR", "客户信息不能为空" ),
    DEPOSIT_TRANSACTION_INFO_PARAMS_NOT_NULL_ERROR("DEPOSIT_TRANSACTION_INFO_PARAMS_NOT_NULL_ERROR", "交易信息不能为空" ),
    DEPOSIT_PARAMS_TRANSACTION_STATUS_NOT_NULL_ERROR("DEPOSIT_PARAMS_TRANSACTION_STATUS_NOT_NULL_ERROR", "交易信息状态不能为空" ),
    DEPOSIT_PARAMS_TRANSACTION_AMOUNT_NOT_NULL_ERROR("DEPOSIT_PARAMS_TRANSACTION_AMOUNT_NOT_NULL_ERROR", "交易信息金额不能为空" ),
    DEPOSIT_PARAMS_TRANSACTION_DATE_NOT_NULL_ERROR("DEPOSIT_PARAMS_TRANSACTION_DATE_NOT_NULL_ERROR", "交易信息日期不能为空" ),
    DEPOSIT_PARAMS_TRANSACTION_TYPE_NOT_NULL_ERROR("DEPOSIT_PARAMS_TRANSACTION_TYPE_NOT_NULL_ERROR", "交易信息类型不能为空" ),
    DEPOSIT_PARAMS_TRANSACTION_CONTRACTNUM_NOT_NULL_ERROR("DEPOSIT_PARAMS_TRANSACTION_CONTRACTNUM_NOT_NULL_ERROR", "交易信息成交合同不能为空" ),
    DEPOSIT_PARAMS_TRANSACTION_BALANCEAMOUNT_ERROR("DEPOSIT_PARAMS_TRANSACTION_BALANCEAMOUNT_ERROR", "退回金额+转佣金额不可以大于收款金额，请确认。" ),

    DEPOSIT_REPEAT_LINKED_ERROR("DEPOSIT_REPEAT_LINKED_ERROR", "意向金重复关联成交报告"),

    TRANSACTION_REPORT_DELETE_ERROR("TRANSACTION_REPORT_DELETE_ERROR", "只有没有提交过的成交合同可以被删除"),
    TRANSACTION_REPORT_STATUS_ERROR("TRANSACTION_REPORT_STATUS_ERROR", "当前记录状态异常"),
    TRANSACTION_REPORT_RECEIVED_COMMISSION_ERROR("TRANSACTION_REPORT_RECEIVED_COMMISSION_ERROR", "只有收佣记录的待收佣金（应收-实收）>0时，可收佣"),
    TRANSACTION_REPORT_CLOSE_ERROR("TRANSACTION_REPORT_CLOSE_ERROR", "只有状态为：已复核、待收佣金为：0、关联的意向金【剩余金额】为 0，可关闭"),
    TRANSACTION_REPORT_DEPOSIT_TRANSACTION_ERROR("TRANSACTION_REPORT_DEPOSIT_TRANSACTION_ERROR", "意向金下交易信息中存在转佣记录"),
    TRANSACTION_REPORT_CONVERT_COMMISSION_ERROR("TRANSACTION_REPORT_CONVERT_COMMISSION_ERROR", "转入佣金加上应收佣金不能大于客户应缴佣金，请调整"),

    AUDIT_STATUS_ERROR("AUDIT_STATUS_ERROR", "审核/复核是否通过参数异常"),

    TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR("TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR", "成交报告参数不能为空异常"),
    ORDER_STATUS_ERROR("ORDER_STATUS_ERROR","订单状态异常"),
    JOY_CONTRACT_CALLBACK_PARAM_NOT_NULL("JOY_CONTRACT_CALLBACK_PARAM_NOT_NULL", "朝昔合同状态更新回调参数不能为空"),
    JOY_CONTRACT_CALLBACK_SIGN_ERROR("JOY_CONTRACT_CALLBACK_SIGN_ERROR", "朝昔合同状态更新回调参数验签失败"),
    JOY_CONTRACT_CALLBACK_AES_ERROR("JOY_CONTRACT_CALLBACK_AES_ERROR", "朝昔合同状态更新回调参数AES解密失败"),

    COMMUNITY_CODE_NOT_NULL("COMMUNITY_CODE_NOT_NULL", "项目编码不得为空"),

    NOT_SUPPORT_CONTRACT_TYPE_CONVERT_TRANSACTION_ERROR("NOT_SUPPORT_CONTRACT_TYPE_CONVERT_TRANSACTION_ERROR", "该合同类型暂不支持生成成交报告"),
    
    CONTRACT_NUMBER_STATUS_ERROR("CONTRACT_NUMBER_STATUS_ERROR", "所选合同编号为已使用/作废状态，请修改。"),
    CONTRACT_NUMBER_HAS_USED("CONTRACT_NUMBER_HAS_USED", "所选合同编号已被使用，请重新选择"),
    ;

     private final String code;
     private final String msg;

     BusinessEnum(String code, String msg) {
      this.code = code;
      this.msg = msg;
     }

     @Override
     public String getCode() {
      return this.code;
     }

     @Override
     public String getMessage() {
      return this.msg;
     }
}
