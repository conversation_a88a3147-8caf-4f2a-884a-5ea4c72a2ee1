package mixc.be.rsms.business.common.feignclient.fallback;

import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.feignclient.ThirdServerClient;
import mixc.be.rsms.pojo.third.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/25
 */
@Slf4j
public class ThirdServerClientFallback implements ThirdServerClient {
    @Override
    public SFOrderItemResp getItems(SFOrderItemParam param) {
        log.error("获取电商订单规则收费项失败");
        throw new BusinessException("获取电商订单规则收费项失败", "-1");
    }

    @Override
    public SFOrderResp syncOrder(SFOrderSyncParam param) {
        log.error("同步订单失败");
		throw new BusinessException("同步订单失败", "-1");
    }

    @Override
    public SFOrderResp adjustToOrder(SFOrderAdjustParam param) {
        log.error("调整已支付的子订单到其他子订单单失败");
		throw new BusinessException("调整已支付的子订单到其他子订单单失败", "-1");
    }

    @Override
    public SFOrderResp addSubOrder(SFOrderAddSubParam param) {
        log.error("新增子订单失败");
		throw new BusinessException("新增子订单失败", "-1");
    }

    @Override
    public SFOrderResp cancelSubOrder(SFOrderCancelSubParam param) {
        log.error("调整取消子订单失败");
		throw new BusinessException("调整取消子订单失败", "-1");
    }

    @Override
    public SFOrderDetailResp getOrderDetailInfo(SFOrderDetailParam param) {
        log.error("查询订单详情失败");
		throw new BusinessException("查询订单详情失败", "-1");
    }

    @Override
    public SFOrderDetailSubResp getSubOrderDetailInfo(SFOrderDetailSubParam param) {
        log.error("查询子订单详情失败");
		throw new BusinessException("查询子订单详情失败", "-1");
    }

    @Override
    public SfOrderRefundResp refund(SFOrderRefundParam param) {
        log.error("订单退款失败");
        throw new BusinessException("订单退款失败", "-1");
    }

    @Override
    public SFOrderRefundDetailResp refundDetail(SFOrderRefundDetailParam param) {
        log.error("订单退款详情失败");
        throw new BusinessException("订单退款详情失败", "-1");
    }

    @Override
    public SFOrderPayQueryResp payQuery(SFOrderPayQueryParam param) {
        log.error("支付交易查询失败");
        throw new BusinessException("支付交易查询失败", "-1");
    }

    @Override
    public SFOrderTradeCreateResp createTrade(SFOrderTradeCreateParam param) {
        log.error("支付交易下单失败");
        throw new BusinessException("支付交易下单失败", "-1");
    }
}
