package mixc.be.rsms.business.common.exception;

import lombok.Getter;
import mixc.be.rsms.business.common.enums.ResultEnum;
import mixc.be.rsms.common.service.ErrorCode;

/**
 * <AUTHOR>
 * @date 2024-09-05 13:53
 */

@Getter
public class UnAuthException extends RuntimeException{

    private final String code;

    public UnAuthException(ErrorCode errorCode){
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
    }

    public UnAuthException(ResultEnum resultEnum){
        super(resultEnum.getMsg());
        this.code = resultEnum.getCode();
    }

    public UnAuthException(String message, String code) {
        super(message);
        this.code = code;
    }

}
