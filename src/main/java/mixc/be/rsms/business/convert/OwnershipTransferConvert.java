package mixc.be.rsms.business.convert;

import mixc.be.rsms.business.domain.dto.OwnershipTransferUpdateParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessOwnershipTransfer;
import mixc.be.rsms.business.vo.OwnershipTransferVo;
import mixc.be.rsms.pojo.business.OwnershipTransferResp;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
/**
 * 权证过户映射转换类
 * <AUTHOR>
 * @date 2024-10-14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OwnershipTransferConvert {

	/**
	 * dto 转 entity
	 * @param dto 权证过户新增或编辑参数
	 * @return
	 */
	TbBusinessOwnershipTransfer convert(OwnershipTransferUpdateParams dto);

	OwnershipTransferVo dataToVo(TbBusinessOwnershipTransfer transfer);

	OwnershipTransferResp data2Resp(TbBusinessOwnershipTransfer transfer);
}
