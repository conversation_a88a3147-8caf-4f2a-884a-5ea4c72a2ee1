package mixc.be.rsms.business.convert;

import mixc.be.rsms.business.domain.dto.OrderAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessOrder;
import mixc.be.rsms.business.vo.OrderVo;
import mixc.be.rsms.pojo.business.OrderResp;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataOrderConvert {

    OrderVo data2Vo(TbBusinessOrder order);

    OrderResp data2Resp(TbBusinessOrder order);

    OrderResp vo2Resp(OrderVo orderVo);

    TbBusinessOrder add2Data(OrderAddParams params);
}
