package mixc.be.rsms.business.convert;

import mixc.be.rsms.business.domain.dto.SubOrderRefundAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessSubOrderRefund;
import mixc.be.rsms.business.vo.OrderRefundVo;
import mixc.be.rsms.pojo.business.OrderRefundResp;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataSubOrderRefundConvert {

    OrderRefundVo data2Vo(TbBusinessSubOrderRefund orderRefund);

    OrderRefundResp data2Resp(TbBusinessSubOrderRefund orderRefund);

    TbBusinessSubOrderRefund add2Data(SubOrderRefundAddParams params);

    List<OrderRefundResp> vo2RespList(List<OrderRefundVo> orderRefundVos);
}
