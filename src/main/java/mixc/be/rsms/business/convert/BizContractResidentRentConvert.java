package mixc.be.rsms.business.convert;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import mixc.be.rsms.business.domain.dto.ContractSignAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessContractResidentRent;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizContractResidentRentConvert {

    TbBusinessContractResidentRent convert(ContractSignAddParams params);
}
