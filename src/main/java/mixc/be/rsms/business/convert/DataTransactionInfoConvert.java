package mixc.be.rsms.business.convert;

import mixc.be.rsms.business.domain.dto.TransactionInfoAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionInfo;
import mixc.be.rsms.business.vo.TransactionInfoVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/15
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataTransactionInfoConvert {

    TransactionInfoVo dataToVo(TbBusinessTransactionInfo transactionInfo);

    TbBusinessTransactionInfo addParamToData(TransactionInfoAddParams transactionInfoAddParams);
}
