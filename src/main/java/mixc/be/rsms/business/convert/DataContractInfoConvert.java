package mixc.be.rsms.business.convert;

import mixc.be.rsms.business.domain.dto.ContractInfoAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessContractInfo;
import mixc.be.rsms.business.vo.ContractInfoVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataContractInfoConvert {

    TbBusinessContractInfo addParamsToData(ContractInfoAddParams params);

    ContractInfoVo dataToVo(TbBusinessContractInfo info);
}
