package mixc.be.rsms.business.convert;

import mixc.be.rsms.business.domain.dto.TransactionReportAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionReport;
import mixc.be.rsms.business.vo.TransactionReportVo;
import mixc.be.rsms.pojo.business.TransactionReportResp;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataTransactionReportConvert {

    TransactionReportVo dataToVo(TbBusinessTransactionReport report);

    TbBusinessTransactionReport addParamsToData(TransactionReportAddParams params);

    List<TransactionReportResp> data2Resp(List<TbBusinessTransactionReport> tbBusinessTransactionReports);

    TbBusinessTransactionReport resp2Data(TransactionReportResp transactionReportResp);
}
