package mixc.be.rsms.business.convert;

import mixc.be.rsms.business.vo.ParkingSpaceVo;
import mixc.be.rsms.pojo.data.ParkingSpaceResp;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description
 * @date 2025/02/07
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataParkingSpaceConvert {

    ParkingSpaceResp vo2Resp(ParkingSpaceVo vo);

    ParkingSpaceVo resp2Vo(ParkingSpaceResp resp);
}
