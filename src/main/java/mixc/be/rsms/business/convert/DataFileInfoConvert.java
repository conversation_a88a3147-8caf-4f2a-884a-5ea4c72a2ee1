package mixc.be.rsms.business.convert;

import mixc.be.rsms.business.vo.DataFileInfoVo;
import mixc.be.rsms.pojo.data.FileInfoResp;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/02/07
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataFileInfoConvert {

    List<DataFileInfoVo> resp2Vo(List<FileInfoResp> resp);

    FileInfoResp vo2Resp(DataFileInfoVo vo);

}
