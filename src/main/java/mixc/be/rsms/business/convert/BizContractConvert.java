package mixc.be.rsms.business.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

import mixc.be.rsms.business.domain.dto.ContractSignAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessContract;
import mixc.be.rsms.business.vo.ContractAttachmentVo;
import mixc.be.rsms.business.vo.ContractBaseInfoVo;
import mixc.be.rsms.business.vo.ContractDetailVo;
import mixc.be.rsms.business.vo.ContractPartAVo;
import mixc.be.rsms.business.vo.ContractPartBVo;
import mixc.be.rsms.business.vo.ContractSignedVo;
import mixc.be.rsms.business.vo.HouseInfoVo;
import mixc.be.rsms.pojo.mobile.ContractResp;
import mixc.be.rsms.pojo.mobile.CustomerAssetDetailResp;
import mixc.be.rsms.pojo.mobile.CustomerAssetResp;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizContractConvert {

	@Mappings({
		@Mapping(source = "id", target = "id"),
		@Mapping(source = "contractBaseInfo.contractType", target = "contractType"),
		@Mapping(source = "contractBaseInfo.contractNumber", target = "contractNumber"),
		@Mapping(source = "contractBaseInfo.houseId", target = "houseId"),
		@Mapping(source = "contractBaseInfo.parkSpaceId", target = "parkingSpaceId"),
		@Mapping(source = "contractBaseInfo.intermediaryCompanyId", target = "intermediaryCompanyId"),
		
		@Mapping(source = "partAParams.leaserType", target = "leaserType"),
		@Mapping(source = "partAParams.leaserUserName", target = "leaserUserName"),
		@Mapping(source = "partAParams.leaserMobile", target = "leaserMobile"),
		@Mapping(source = "partAParams.identityType", target = "leaserIdType"),
		@Mapping(source = "partAParams.identityNumber", target = "leaserIdNumber"),
		@Mapping(source = "partAParams.leaserAddress", target = "leaserAddress"),
		@Mapping(source = "partAParams.leaserProxyUserName", target = "leaserProxyUserName"),
		@Mapping(source = "partAParams.leaserProxyUserMobile", target = "leaserProxyUserMobile"),
		@Mapping(source = "partAParams.leaserProxyUserIdNumber", target = "leaserProxyUserIdNumber"),
		
		@Mapping(source = "partBParams.customerType", target = "customerType"),
		@Mapping(source = "partBParams.customerId", target = "customerId"),
		@Mapping(source = "partBParams.customerMobile", target = "customerMobile"),
		@Mapping(source = "partBParams.identityType", target = "customerIdType"),
		@Mapping(source = "partBParams.identityNumber", target = "customerIdNumber"),
		@Mapping(source = "partBParams.customerAddress", target = "customerAddress"),
		@Mapping(source = "partBParams.customerProxyUserName", target = "customerProxyUserName"),
		@Mapping(source = "partBParams.customerProxyUserMobile", target = "customerProxyUserMobile"),
		@Mapping(source = "partBParams.customerProxyUserIdNumber", target = "customerProxyUserIdNumber"),
		@Mapping(source = "partBParams.coPurchaser", target = "coPurchaser"),
		
		@Mapping(source = "signParams.contractSignDate", target = "contractSignDate"),
		@Mapping(source = "signParams.transactionPrice", target = "transactionPrice"),
		@Mapping(source = "signParams.leaserCommission", target = "leaserCommission"),
		@Mapping(source = "signParams.customerCommission", target = "customerCommission"),
	})
    TbBusinessContract convert(ContractSignAddParams params);
    
    ContractDetailVo convertVo(TbBusinessContract bizContract);
    
    @Mappings({
		@Mapping(source = "bizContract.id", target = "contractId"),
		@Mapping(source = "bizContract.parkingSpaceId", target = "spaceParkId"),
		@Mapping(source = "bizContract.createUser", target = "createUser"),
	})
    ContractBaseInfoVo convertBaseInfoVo(TbBusinessContract bizContract);
    
    @Mappings({
    	@Mapping(source = "bizContract.leaserIdType", target = "identityType"),
		@Mapping(source = "bizContract.leaserIdNumber", target = "identityNumber"),
	})
    ContractPartAVo convertPartAVo(TbBusinessContract bizContract);
    
    @Mappings({
    	@Mapping(source = "bizContract.customerIdType", target = "identityType"),
		@Mapping(source = "bizContract.customerIdNumber", target = "identityNumber")
	})
    ContractPartBVo convertPartBVo(TbBusinessContract bizContract);
    
    ContractSignedVo convertContractSignedVo(TbBusinessContract bizContract);
    
    ContractAttachmentVo convertContractAttachmentVo(TbBusinessContract bizContract);
    
    CustomerAssetResp contract2Asset(TbBusinessContract bizContract);
    
    CustomerAssetDetailResp houseInfo2AssetDetail(HouseInfoVo houseInfoVo);
    
    List<ContractResp> bizContractList2ConractRespList(List<TbBusinessContract> bizContractList);
}
