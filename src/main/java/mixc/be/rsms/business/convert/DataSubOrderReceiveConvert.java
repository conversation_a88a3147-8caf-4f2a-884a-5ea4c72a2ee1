package mixc.be.rsms.business.convert;

import mixc.be.rsms.business.domain.dto.SubOrderReceiveAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessSubOrderReceive;
import mixc.be.rsms.business.vo.OrderReceiveVo;
import mixc.be.rsms.pojo.business.OrderReceiveResp;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataSubOrderReceiveConvert {
    OrderReceiveVo data2Vo(TbBusinessSubOrderReceive orderReceive);

    OrderReceiveResp data2Resp(TbBusinessSubOrderReceive orderReceive);

    TbBusinessSubOrderReceive add2Data(SubOrderReceiveAddParams params);

    List<OrderReceiveResp> vo2RespList(List<OrderReceiveVo> orderReceiveVos);
}
