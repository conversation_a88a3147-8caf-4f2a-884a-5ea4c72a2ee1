package mixc.be.rsms.business.convert;

import mixc.be.rsms.business.domain.dto.SubOrderCommissionAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessSubOrderCommission;
import mixc.be.rsms.business.vo.OrderCommissionVo;
import mixc.be.rsms.pojo.business.OrderCommissionResp;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataSubOrderCommissionConvert {
    OrderCommissionVo data2Vo(TbBusinessSubOrderCommission orderCommission);

    OrderCommissionResp data2Resp(TbBusinessSubOrderCommission orderCommission);

    TbBusinessSubOrderCommission add2Data(SubOrderCommissionAddParams params);

    List<OrderCommissionResp> vo2RespList(List<OrderCommissionVo> orderCommissionVos);
}
