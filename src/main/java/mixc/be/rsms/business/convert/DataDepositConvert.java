package mixc.be.rsms.business.convert;

import mixc.be.rsms.business.domain.dto.DepositAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessDeposit;
import mixc.be.rsms.business.vo.DepositVo;
import mixc.be.rsms.pojo.business.DepositResp;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/15
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataDepositConvert {

    DepositVo dataToVo(TbBusinessDeposit deposit);

    TbBusinessDeposit addParamToData(DepositAddParams depositAddParams);

    List<DepositResp> data2RespList(List<TbBusinessDeposit> tbBusinessDeposits);

    DepositResp data2Resp(TbBusinessDeposit tbBusinessDeposits);

    TbBusinessDeposit resp2Data(DepositResp depositResp);
}
