package mixc.be.rsms.business.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import mixc.be.rsms.business.vo.RequestVo;

import java.io.Serial;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderQueryParams extends RequestVo {
    @Serial
    private static final long serialVersionUID = -6540580492160805320L;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String orderCode;

    /**
     * 订单状态：待支付-unpay、部分付款-partpay、已付款-payed、全额退款-refundall、已转佣-conversioned_commission
     */
    @Schema(description = "订单状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String status;


    /**
     * 交易类型:yxj-意向金、dj-定金、yj-佣金
     */
    @Schema(description = "交易类型(字典值)", example = "yxj-意向金、dj-定金、yj-佣金", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String transactionType;

    /**
     * 所属项目关键字
     */
    @Schema(description = "所属项目关键字", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String communityKeyWord;

    @Schema(description = "客户姓名关键字", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String customerNameKeyWord;

    @Schema(description = "客户电话关键字", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String customerMobileKeyWord;
    /**
     * 单据号码关键字
     */
    @Schema(description = "单据号码关键字", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String documentNumKeyWord;

    /**
     * 订单日期始
     */
    @Schema(description = "订单日期始", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private LocalDate orderDateFrom;

    /**
     * 订单日期止
     */
    @Schema(description = "订单日期止", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private LocalDate orderDateTo;

    @Schema(description = "房源/车位号关键字", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String houseOrParkingKeyWord;

    @Schema(description = "经纪人/创建人关键字", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String agentOrCreateBy;
}
