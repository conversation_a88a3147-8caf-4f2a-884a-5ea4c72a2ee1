package mixc.be.rsms.business.domain.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 合同签署参数实体类
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractSignParams implements Serializable {

	private static final long serialVersionUID = 827471861947431841L;

	/**
	 * 合同签署日期
	 */
	@NotNull(message = "合同签署日期不能为空")
	@Schema(description = "合同签署日期", example = "321909213456713452", requiredMode = Schema.RequiredMode.REQUIRED)
	private LocalDate contractSignDate;
	
	/**
	 * 成交价格
	 */
	@NotNull(message = "成交价格不能为空")
	@Schema(description = "成交价格", example = "120.00", requiredMode = Schema.RequiredMode.REQUIRED)
	private BigDecimal transactionPrice;
	
	/**
	 * 业主佣金
	 */
	@NotNull(message = "业主佣金不能为空")
	@Schema(description = "业主佣金（当甲方信息为空时此字段为空）", example = "120.00", requiredMode = Schema.RequiredMode.REQUIRED)
	private BigDecimal leaserCommission;
	
	/**
	 * 客户佣金
	 */
	@NotNull(message = "客户佣金不能为空")
	@Schema(description = "客户佣金（当乙方信息为空时此字段为空）", example = "120.00", requiredMode = Schema.RequiredMode.REQUIRED)
	private BigDecimal customerCommission;
}
