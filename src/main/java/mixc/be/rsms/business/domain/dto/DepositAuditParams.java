package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 成交意向金/订金表 查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
public class DepositAuditParams implements Serializable {

    @Serial
    private static final long serialVersionUID = 2172582917152226998L;
    /**
     * 主键ID
     */
    @Schema(description = "主键ID", example = "238747292", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long id;

    /**
     * 审核通过/驳回
     */
    @Schema(description = "审核通过/驳回", example = "审核/驳回接口专用：true:审核通过，false:驳回", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean pass;

    /**
     * 当前记录，为空时，仅改变状态、不为空时，做新增或修改
     */
    @Schema(description = "当前记录对象", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private DepositAddParams deposit;
}
