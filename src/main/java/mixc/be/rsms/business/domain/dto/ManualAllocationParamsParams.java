package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;

/**
 * 手动分配业绩对象
 * <AUTHOR>
 * @description
 * @date 2025/05/29
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ManualAllocationParamsParams extends AllocationParams {
    @Serial
    private static final long serialVersionUID = -6741165303247695078L;

    @Schema(description = "手动分配类型(字典值：manual_allocation_type)：业绩-performance，提成-commitment")
    private String typeCode = "performance";

}
