package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/21
 */
@Data
public class ContractInfoAddParams implements Serializable {

    @Serial
    private static final long serialVersionUID = 5420314746361609146L;

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long id;
    /**
     * 业主电话
     */
    @Schema(description = "业主电话", example = "***********", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ownerMobile;

    /**
     * 甲方姓名
     */
    @Schema(description = "甲方姓名", example = "王小二", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ownerName;

    /**
     * 甲方证件号
     */
    @Schema(description = "甲方证件号", example = "140992482882848258", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ownerIcardNum;

    /**
     * 甲方联系地址
     */
    @Schema(description = "甲方联系地址", example = "XX小区", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ownerAddress;

    /**
     * 客户电话
     */
    @Schema(description = "客户电话", example = "13428492849", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customerMobile;

    /**
     * 乙方姓名
     */
    @Schema(description = "乙方姓名", example = "王小二", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customerName;

    /**
     * 乙方证件号
     */
    @Schema(description = "乙方证件号", example = "14099248288284825829", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customerIcardNum;

    /**
     * 乙方联系地址
     */
    @Schema(description = "乙方联系地址", example = "XX小区", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customerAddress;
}
