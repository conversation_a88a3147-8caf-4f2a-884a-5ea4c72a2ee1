package mixc.be.rsms.business.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-10-08 16:38
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "组织编辑参数")
public class OrgDetailsResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 5637907621467026104L;

    @Schema(description = "组织id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "组织名称")
    private String orgName;

    @Schema(description = "朝昔组织类型名称")
    private String orgTypeName;

    @Schema(description = "组织层级（platform-平台、company-公司、region-大区、district-区、store-店、group-组）")
    private String orgLevel;

    @Schema(description = "父组织层级（platform-平台、company-公司、region-大区、district-区、store-店、group-组）")
    private String parentOrgLevel;

    @Schema(description = "备注")
    private String remark;



}
