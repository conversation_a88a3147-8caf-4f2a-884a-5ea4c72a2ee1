package mixc.be.rsms.business.domain.dto;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 朝昔合同回调参数实体类
 * <AUTHOR>
 * @date 2025-03-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JoyContractCallbackParam implements Serializable {
	
	private static final long serialVersionUID = 1539894307582735766L;

	/**
	 * 合同业务数据AES加密后的加密串
	 */
	private String ciphertext;
	
	/**
	 * 事件类型
	 */
	private String eventType;
	
	/**
	 * 业务数据
	 */
	private Resource resource;
	
	/**
	 * 重试次数
	 */
	private Integer retryCount;
	
	/**
	 * 业务数据实体类
	 */
	@Data
	public static class Resource {
		
		/**
		 * 合同ID
		 */
		private String contractId;
		
		/**
		 * 合同签署状态，1-待提交，2-待签约，3-已撤回，4-已过期，5-已完成
		 */
		private String contractNotifyState;
		
		/**
		 * 合同签署状态变化时间戳，毫秒
		 */
		private String contractStatusUpdateTime;
		
		/**
		 * 签署对象列表（待签约和已完成状态的合同会返回签署对象列表）
		 */
		private List<ContractSign> contractSignObjectList;
	}
	
	/**
	 * 签署对象实体类
	 */
	@Data
	public static class ContractSign {
		
		/**
		 * 签署方：firstParty 甲方，secondParty 乙方，thirdParty 丙方
		 */
		private String signParty;
		
		/**
		 * 签署对象类型：CORPORATE 企业，PERSONAL 个人
		 */
		private String signObjectType;
		
		/**
		 * 签署手机号
		 */
		private String signObjectMobile;
		
		/**
		 * 签署方姓名
		 */
		private String signObjectName;
		
		/**
		 * 签署状态：0-待签署，1-已签署，3-结束签署（由于其中一方退出，或者过期，或者合同作废等原因，会到这个状态
		 */
		private Integer signStatus;
		
		/**
		 * 签署时间，毫秒时间戳
		 */
		private Long signTime;
	}
}
