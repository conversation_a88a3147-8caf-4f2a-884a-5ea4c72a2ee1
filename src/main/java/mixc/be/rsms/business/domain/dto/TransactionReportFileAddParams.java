package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/22
 */
@Data
public class TransactionReportFileAddParams implements Serializable {

    @Serial
    private static final long serialVersionUID = -4945678240747859588L;
    /**
     * 文件业务
     */
    @Schema(description = "文件描述(字典值)", example = "identity-身份证明、ownership-产权证明、commission-佣金证明、report-合同证明、other-其他", requiredMode = Schema.RequiredMode.REQUIRED)
    private String businessType;

    /**
     * 文件Id
     */
    @Schema(description = "文件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

}
