package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 * 合同双方信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_business_contract_info")
public class TbBusinessContractInfo extends BaseEntity {


    @Serial
    private static final long serialVersionUID = -5695237745534099010L;
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 成交报告表ID
     */
    @TableField("report_id")
    private Long reportId;

    /**
     * 业主电话
     */
    @TableField("owner_mobile")
    private String ownerMobile;

    /**
     * 甲方姓名
     */
    @TableField("owner_name")
    private String ownerName;

    /**
     * 甲方证件号
     */
    @TableField("owner_icard_num")
    private String ownerIcardNum;

    /**
     * 甲方联系地址
     */
    @TableField("owner_address")
    private String ownerAddress;

    /**
     * 客户电话
     */
    @TableField("customer_mobile")
    private String customerMobile;

    /**
     * 乙方姓名
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 乙方证件号
     */
    @TableField("customer_icard_num")
    private String customerIcardNum;

    /**
     * 乙方联系地址
     */
    @TableField("customer_address")
    private String customerAddress;
}
