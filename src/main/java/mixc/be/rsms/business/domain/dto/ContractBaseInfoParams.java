package mixc.be.rsms.business.domain.dto;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "合同基本信息参数")
public class ContractBaseInfoParams implements Serializable  {

	private static final long serialVersionUID = -6161145992681010165L;

	/**
	 * 合同类型
	 */
	@NotBlank(message = "合同类型不能为空")
	@Schema(description = "合同类型:字典", example = "resident_rent_service", requiredMode = Schema.RequiredMode.REQUIRED)
	private String contractType;
	
	/**
	 * 合同编号
	 */
	@NotBlank(message = "合同编号不能为空")
	@Schema(description = "合同编号", example = "HT20241201001", requiredMode = Schema.RequiredMode.REQUIRED)
	private String contractNumber;
	
	/**
	 * 房源ID
	 */
	@Schema(description = "房源ID(当合同类型为房源合同相关时此字段不能为空)", example = "1234555", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Long houseId;
	
	/**
	 * 车位ID
	 */
	@Schema(description = "车位ID(当合同类型为车位合同相关时此字段不能为空)", example = "1234555", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Long parkSpaceId;
	
	/**
	 * 居间方公司ID
	 */
	@Schema(description = "居间方公司ID(当合同不需要居间方参与时此字段为空)", example = "1234555", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Long intermediaryCompanyId;
	
	/**
	 * 居间方公司名称
	 */
	@Schema(description = "居间方公司名称(详情展示用)", example = "万象置地", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Long intermediaryCompanyName;
}
