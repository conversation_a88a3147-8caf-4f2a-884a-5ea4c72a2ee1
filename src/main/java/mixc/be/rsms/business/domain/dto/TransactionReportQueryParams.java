package mixc.be.rsms.business.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import mixc.be.rsms.business.vo.RequestVo;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TransactionReportQueryParams extends RequestVo {


    @Serial
    private static final long serialVersionUID = -2859263592907357057L;
    /**
     * 成交报告编号
     */
    @Schema(description = "成交报告编号", example = "CJ+当天日期+6位数字", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String reportCode;

    /**
     * 成交合同编号
     */
    @Schema(description = "成交合同编号", example = "dde43935344533", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String transactionContractNum;

    /**
     * 成交报告状态：unaudit-未审核，auditing-审核中，reviewing-复核中，reviewed-已复核，closed-已结案
     */
    @Schema(description = "成交报告状态(字典值)", example = "unaudit-未审核，auditing-审核中，reviewing-复核中，reviewed-已复核，closed-已结案", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String status;

    /**
     * 意向金/订金ID
     */
    @Schema(description = "意向金/订金ID", example = "22839248", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String depositCode;

    /**
     * 类型:(dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖)
     */
    @NotBlank(message = "类型不能为空")
    @Schema(description = "类型(字典值)", example = "dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String category;
    /**
     * 客户IdList
     */
    @Schema(description = "客户IdList", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<Long> customerIdList;

    /**
     * 客户姓名关键字
     */
    @Schema(description = "客户姓名关键字", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String customerNameKeyWord;
    /**
     * 房源Id列表
     */
    @Schema(description = "房源Id列表", example = "234112", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<Long> houseIdList;


    /**
     * 车位Id列表
     */
    @Schema(description = "车位Id列表", example = "234112", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<Long> parkingSpaceIdList;

    /**
     * 房源搜索关键字(项目关键字)
     */
    @Schema(description = "房源搜索关键字(项目关键字)", example = "XX小区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String keyword;

    /**
     * 成交日期起始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "成交日期起始", example = "2024-09-08", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate dealDateFrom;


    /**
     * 成交日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "成交日期结束", example = "2024-09-08", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate dealDateTo;


    /**
     * 成交金额起始
     */
    @Schema(description = "成交金额起始", example = "92.99", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal dealAmountFrom;

    /**
     * 成交金额结束
     */
    @Schema(description = "成交金额结束", example = "92.99", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal dealAmountTo;


    /**
     * 业主佣金起始
     */
    @Schema(description = "业主佣金起始", example = "10.99", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal ownerCommissionFrom;


    /**
     * 业主佣金结束
     */
    @Schema(description = "业主佣金结束", example = "10.99", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal ownerCommissionTo;

    /**
     * 客户佣金起始
     */
    @Schema(description = "客户佣金起始", example = "10.99", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal customerCommissionFrom;

    /**
     * 业主佣金结束
     */
    @Schema(description = "业主佣金结束", example = "10.99", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal customerCommissionTo;

    /**
     * 待收佣金起始
     */
    @Schema(description = "待收佣金起始", example = "10.99", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal awaitCommissionFrom;

    /**
     * 待收佣金结束
     */
    @Schema(description = "待收佣金结束", example = "10.99", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal awaitCommissionTo;

    /**
     * 签约日期起始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "签约日期起始", example = "2024-09-08", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate signDateFrom;

    /**
     * 签约日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "签约日期结束", example = "2024-09-08", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate signDateTo;


    /**全
     * 收齐日期起始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "收齐日期起始", example = "2024-09-08", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate allDateFrom;

    /**
     * 收齐日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "收齐日期结束", example = "2024-09-08", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate allDateTo;

    /**
     * 可分成业绩起始(三期字段)
     */
    @Schema(description = "可分成业绩起始", example = "10.00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal canDivideAchievementFrom;

    /**
     * 可分成业绩结束(三期字段)
     */
    @Schema(description = "可分成业绩结束", example = "20.00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal canDivideAchievementTo;
}
