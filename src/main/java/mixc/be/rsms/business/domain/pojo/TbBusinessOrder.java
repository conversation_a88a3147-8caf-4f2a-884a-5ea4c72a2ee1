package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@TableName("tb_business_order")
@EqualsAndHashCode(callSuper = false)
public class TbBusinessOrder extends BaseEntity {


    @Serial
    private static final long serialVersionUID = -8822616849127287084L;
    /**
     * 主键
     */

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 订单编号
     */
    @TableField("order_code")
    private String orderCode;

    /**
     * 订单状态：待支付-unpay、部分付款-partpay、已付款-payed、全额退款-refundall、已转佣-conversioned_commission
     */
    @TableField("status")
    private String status;

    /**
     * 合同编号
     */
    @TableField("contract_num")
    private String contractNum;

    /**
     * 房源ID
     */
    @TableField("house_ids")
    private String houseIds;

    /**
     * 车位ID
     */
    @TableField("parking_ids")
    private String parkingIds;

    /**
     * 楼栋
     */
    @TableField("building")
    private String building;

    /**
     * 单元
     */
    @TableField("unit")
    private String unit;

    /**
     * 房号
     */
    @TableField("house_number")
    private String houseNumber;

    /**
     * 新房项目ID
     */
    @TableField("new_house_community_id")
    private Long newHouseCommunityId;

    /**
     * 项目ID
     */
    @TableField("community_ids")
    private String communityIds;

    /**
     * 客户姓名
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 客户电话
     */
    @TableField("customer_mobile")
    private String customerMobile;

    /**
     * 订单日期
     */
    @TableField("order_date")
    private LocalDateTime orderDate;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 交易类型:yxj-意向金、dj-定金、yj-佣金
     */
    @TableField("transaction_type")
    private String transactionType;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

}
