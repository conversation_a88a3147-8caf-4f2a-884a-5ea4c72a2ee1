package mixc.be.rsms.business.domain.dto;

import java.io.Serializable;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 附件信息参数实体类
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractAttachementParams implements Serializable  {

	private static final long serialVersionUID = 6651744261470140796L;

	/**
	 * 甲方房屋产权资料复印件
	 */
	@Schema(description = "甲方房屋产权资料复印件", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
	private List<FileInfoParam> leaserOwnershipAttachment;
	
	/**
	 * 甲乙双方身份证明复印件
	 */
	@Schema(description = "甲乙双方身份证明复印件", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
	private List<FileInfoParam> identityAttachment;
	
	/**
	 * 房屋交接清单
	 */
	@Schema(description = "房屋交接清单", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
	private List<FileInfoParam> houseHandoverAttachment;
	
	/**
	 * 乙方身份证明证件
	 */
	@Schema(description = "乙方身份证明证件", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
	private List<FileInfoParam> customerIdentityAttachment;
	
	/**
	 * 甲方车位产权资料复印件
	 */
	@Schema(description = "甲方车位产权资料复印件", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
	private List<FileInfoParam> leaserCarOwnershipAttachment;
	
	/**
	 * 乙方车辆资料复印件
	 */
	@Schema(description = "乙方车辆资料复印件", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
	private List<FileInfoParam> customerCarInfoAttachment;
	
	/**
	 * 补充条款
	 */
	@Schema(description = "补充条款", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
	private List<FileInfoParam> supplementaryTermsAttachment;
	
	/**
	 * 房屋平面图、装修一览表
	 */
	@Schema(description = "房屋平面图、装修一览表", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
	private List<FileInfoParam> houseDesignAndDecorationAttachment;
}
