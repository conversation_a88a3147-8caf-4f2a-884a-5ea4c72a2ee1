package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <p>
 * 业绩主表-明细行
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@TableName("tb_business_performance_item")
@EqualsAndHashCode(callSuper = true)
public class TbBusinessPerformanceItem extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -2404729534090447340L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 业绩表id
     */
    @TableField("performance_id")
    private Long performanceId;

    /**
     * 分成角色编码(house_input:房源录入人,house_maintainer:房源维护人,house_opener:房源开盘人,house_pic:房源图片人,house_video:房源视频人,house_key:房源钥匙人,house_delegate:房源委托人,house_bargain:房源议价人,client_first_input:客源首录人,client_maintainer:客源维护人,deal_maker:成交人,reserve:预留)
     */
    @TableField("tenths_role")
    private String tenthsRole;

    /**
     * 税率
     */
    @TableField("tax_rate")
    private BigDecimal taxRate;

    /**
     * 应收不含税金额
     */
    @TableField("receivable_excluding_tax_amount")
    private BigDecimal receivableExcludingTaxAmount;

    /**
     * 实收不含税金额
     */
    @TableField("real_time_excluding_tax_amount")
    private BigDecimal realTimeExcludingTaxAmount;
}
