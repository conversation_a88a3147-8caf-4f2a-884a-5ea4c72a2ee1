package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2025/04/07
 */
@Data
public class DepositRefundParam implements Serializable {
    @Serial
    private static final long serialVersionUID = 266907140386149797L;

    /**
     * 意向金ID
     */
    @Schema(description = "意向金ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "意向金ID 不能为空")
    private Long id;

    /**
     * 退款渠道
     */
    @Schema(description = "退款渠道", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "退款渠道 不能为空")
    private String refundChannel;

    /**
     * 收款银行账号
     */
    @Schema(description = "收款银行账号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "收款银行账号 不能为空")
    private String bankNum;

    /**
     * 收款账号名
     */
    @Schema(description = "收款账号名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "收款账号名 不能为空")
    private String accountName;

    /**
     * 收款开户行
     */
    @Schema(description = "收款开户行", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "收款开户行 不能为空")
    private String bankName;

    /**
     * 收款账户城市
     */
    @Schema(description = "收款账户城市", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "收款账户城市 不能为空")
    private String city;

}
