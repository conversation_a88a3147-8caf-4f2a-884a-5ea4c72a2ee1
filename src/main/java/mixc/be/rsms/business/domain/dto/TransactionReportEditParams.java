package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/21
 */
@Data
public class TransactionReportEditParams implements Serializable {
    @Serial
    private static final long serialVersionUID = 4300113584490401496L;
    /**
     * 成交报告ID
     */
    @Schema(description = "成交报告ID", example = "344533", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long id;

    /**
     * 审核/复核 通过状态
     */
    @Schema(description = "审核/复核是否通过(true:通过，false:不通过)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean pass;

    /**
     * 提交时专用对象
     * 当前记录 为空时，仅作 提交；不为空则先保存/修改，再做提交
     */
    @Schema(description = "当前记录状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private TransactionReportAddParams transactionReport;

}
