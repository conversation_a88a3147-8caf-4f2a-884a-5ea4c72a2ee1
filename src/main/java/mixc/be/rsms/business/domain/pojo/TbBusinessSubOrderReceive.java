package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <p>
 * 收款订单子表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_business_sub_order_receive")
public class TbBusinessSubOrderReceive extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -1010707788016101620L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 主订单Id
     */
    @TableField("parent_order_id")
    private Long parentOrderId;

    /**
     * 子订单编号
     */
    @TableField("sub_order_code")
    private String subOrderCode;

    /**
     * 支付模式:2-全额支付、1-分期
     */
    @TableField("pay_mode")
    private String payMode;

    /**
     * 费用项目id(来源：租售项目接口)
     */
    @TableField("receive_community_id")
    private String receiveCommunityId;

    /**
     * 收费项名称(来源：租售系统返回)
     */
    @TableField("receive_community_name")
    private String receiveCommunityName;


    /**
     * 租售系统 收费项 code
     */
    @TableField("zs_receive_community_code")
    private String zsReceiveCommunityCode;

    /**
     * 订单状态:0-待支付，1-已支付
     */
    @TableField("order_status")
    private String orderStatus;

    /**
     * 支付金额
     */
    @TableField("pay_amount")
    private BigDecimal payAmount;

    /**
     * 商品名称
     */
    @TableField("good_name")
    private String goodName;

    /**
     * 折扣金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 支付渠道
     */
    @TableField("pay_channel")
    private String payChannel;

    /**
     * 收费系统id
     */
    @TableField("charge_id")
    private Long chargeId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    /**
     * 业务记录Id
     */
    @TableField("business_id")
    private Long businessId;
}
