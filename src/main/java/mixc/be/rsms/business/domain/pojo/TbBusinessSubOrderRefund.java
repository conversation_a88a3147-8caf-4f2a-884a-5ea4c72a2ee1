package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <p>
 * 退款订单子表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_business_sub_order_refund")
public class TbBusinessSubOrderRefund extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 3399586916669489498L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 主订单Id
     */
    @TableField("parent_order_id")
    private Long parentOrderId;

    /**
     * 子订单编号
     */
    @TableField("sub_order_code")
    private String subOrderCode;

    /**
     * 收款订单Id
     */
    @TableField("receive_sub_order_id")
    private Long receiveSubOrderId;

    /**
     * 退款金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 退款渠道
     */
    @TableField("refund_channel")
    private String refundChannel;

    /**
     * 收款银行账号
     */
    @TableField("bank_num")
    private String bankNum;

    /**
     * 收款账号名
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 收款开户行
     */
    @TableField("bank_name")
    private String bankName;

    /**
     * 收款账户城市
     */
    @TableField("city")
    private String city;

    /**
     * 订单状态:3退款已驳回, 4退款成功, 5退款失败  9 退款中
     */
    @TableField("order_status")
    private String orderStatus;

    /**
     * 收费系统退款订单号
     */
    @TableField("sf_refund_order_num")
    private String sfRefundOrderNum;

    /**
     * 收费系统id
     */
    @TableField("charge_id")
    private Long chargeId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 业务记录Id
     */
    @TableField("business_id")
    private Long businessId;
}
