package mixc.be.rsms.business.domain.dto;

import java.io.Serial;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * APP端房源新增参数实体类
 * <AUTHOR>
 * @date 2024-11-04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractSignAddParams implements Serializable {

	@Serial
	private static final long serialVersionUID = 4474220494285741177L;

	/**
	 * 操作类型：save-报错、next-下一步
	 */
	@Schema(description = "操作类型", example = "save", requiredMode = Schema.RequiredMode.REQUIRED)
	private String operateType;
	
	/**
	 * 主键ID
	 */
	@Schema(description = "主键ID(新增时可选，更新时必填)", example = "1050780972368437248", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Long id;
	
	/**
	 * 合同基本信息
	 */
	@Schema(description = "合同基本信息", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
	private ContractBaseInfoParams contractBaseInfo;
	
	/**
	 * 甲方信息
	 */
	@Schema(description = "甲方信息", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private ContractPartAParams partAParams;
	
	/**
	 * 乙方信息
	 */
	@Schema(description = "乙方信息", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private ContractPartBParams partBParams;
	
	/**
	 * 合同签署信息
	 */
	@Schema(description = "合同签署信息", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private ContractSignParams signParams;
	
	/**
	 * 附件信息
	 */
	@Schema(description = "附件信息", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private ContractAttachementParams attachmentParams;
	/**
	 * 城市编码
	 */
	@Schema(description = "城市编码", example = "100100", requiredMode = Schema.RequiredMode.REQUIRED)
	private String cityCode;
	
	/**
	 * token
	 */
	@Schema(description = "access_token", example = "100100", requiredMode = Schema.RequiredMode.REQUIRED)
	private String accessToken;
}
