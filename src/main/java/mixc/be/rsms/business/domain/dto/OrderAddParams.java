package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class OrderAddParams implements Serializable {

    @Serial
    private static final long serialVersionUID = -8885258534415762149L;

    /**
     * 成交合同编号
     */
    @Schema(description = "成交合同编号")
    private String contractNum;

    /**
     * 房源ID
     */
    @Schema(description = "房源ID")
    private String houseIds;

    /**
     * 车位ID
     */
    @Schema(description = "车位ID")
    private String parkingIds;

    /**
     * 楼栋
     */
    @Schema(description = "楼栋")
    private String building;

    /**
     * 单元
     */
    @Schema(description = "单元")
    private String unit;

    /**
     * 房号
     */
    @Schema(description = "房号")
    private String houseNumber;

    /**
     * 新房项目ID
     */
    @Schema(description = "新房项目ID")
    private Long newHouseCommunityId;

    /**
     * 项目ID
     */
    @Schema(description = "Schema")
    private String communityIds;
    
    /**
     * 客户姓名
     */
    @Schema(description = "客户姓名")
    private String customerName;

    /**
     * 客户电话
     */
    @Schema(description = "客户电话")
    private String customerMobile;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * 交易类型:yxj-意向金、dj-定金、yj-佣金
     */
    @Schema(description = "交易类型")
    private String transactionType;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "收款子订单")
    private List<SubOrderReceiveAddParams> subOrderReceiveAddParams;

    @Schema(description = "退款子订单")
    private List<SubOrderRefundAddParams> subOrderRefundAddParams;

    @Schema(description = "收佣子订单")
    private List<SubOrderCommissionAddParams> subOrderCommissionAddParams;
}
