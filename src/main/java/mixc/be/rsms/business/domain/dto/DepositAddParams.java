package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 成交意向金/订金表 新增参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
public class DepositAddParams implements Serializable {

    @Serial
    private static final long serialVersionUID = 6456356956933397631L;
    /**
     * 主键
     */
    @Schema(description = "主键ID(存在则为修改)", example = "223342", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long id;

    /**
     * 类型:(intention:意向金、deposit:订金)
     */
    @Schema(description = "类型(字典值)", example = "intention:意向金、deposit:订金", requiredMode = Schema.RequiredMode.REQUIRED)
    private String type;

    /**
     * 类目:(dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖)
     */
    @Schema(description = "类目(字典值)", example = "dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖", requiredMode = Schema.RequiredMode.REQUIRED)
    private String category;

    /**
     * 关联房源 ID Arrays
     */
    @Schema(description = "关联房源 ID Arrays", example = "[\"1042523367057563648\",\"1042523367057563648\",\"1042523367057563648\",\"1042523367057563648\"]", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String[] houseDescArrays;

    /**
     * 关联车位 ID Arrays
     */
    @Schema(description = "关联车位 ID Arrays", example = "[\"1042523367057563648\",\"1042523367057563648\",\"1042523367057563648\",\"1042523367057563648\"]", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String[] parkingSpaceDescArrays;

    /**
     * 客户Id
     */
    @Schema(description = "客户Id", example = "2", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long customerId;

    /**
     * 客户联系方式
     */
    @Schema(description = "客户联系方式", example = "19283892938", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customerMobile;

    /**
     * 所属经纪人Id
     */
    @Schema(description = "所属经纪人Id", example = "192838932438", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long brokerId;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "备注", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remark;

    /**
     * 交易信息
     */
    @Schema(description = "交易信息列表", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<TransactionInfoAddParams> transactionInfoList;
}
