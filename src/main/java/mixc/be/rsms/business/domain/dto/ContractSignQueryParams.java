package mixc.be.rsms.business.domain.dto;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * APP端房源新增参数实体类
 * <AUTHOR>
 * @date 2024-11-04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractSignQueryParams implements Serializable {

	@Serial
	private static final long serialVersionUID = -5035709908697562677L;

	/**
	 * 合同编号
	 */
	@Schema(description = "合同编号", example = "HT20241201001", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String contractNumber;
	
	/**
	 * 合同类型
	 */
	@Schema(description = "合同类型:字典", example = "resident_rent_service", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String contractType;
	
	/**
	 * 合同状态
	 */
	@Schema(description = "合同状态:字典", example = "draft", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String contractStatus;
	
	/**
	 * 房源
	 */
	@Schema(description = "房源", example = "1234555", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String contractHouseInfo;
	
	/**
	 * 房号
	 */
	@Schema(description = "房号", example = "1101", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String houseNumber;
	
	/**
	 * 乙方（客户）姓名
	 */
	@Schema(description = "乙方（客户）姓名", example = "李四", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String customerName;
	
    /**
     * 甲方(业主)姓名
     */
	@Schema(description = "甲方(业主)姓名", example = "张三", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String leaserUserName;
	
	/**
     * 居间方公司ID
     */
	@Schema(description = "居间方公司ID", example = "122324", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long intermediaryCompanyId;
	
	/**
	 * 创建人
	 */
	@Schema(description = "创建人", example = "张三", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String creator;
	
	/**
	 * 合同签署日期-开始
	 */
	@Schema(description = "合同签署日期-开始", example = "2024-12-17", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private LocalDate contractSignDateStart;
	
	/**
	 * 合同签署日期-结束
	 */
	@Schema(description = "合同签署日期-结束", example = "2024-12-17", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private LocalDate contractSignDateEnd;
	
	/**
     * 搜索条件字符串
     */
	@Schema(description = "搜索条件字符串(员工端搜索使用)", example = "张三", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String queryStr;
	
	
	
	/**
	 * 第几页
	 */
	@Schema(description = "第几页", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer pageNum;
	
	/**
	 * 每页数量
	 */
	@Schema(description = "每页数量", example = "10", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer pageSize;
	
}
