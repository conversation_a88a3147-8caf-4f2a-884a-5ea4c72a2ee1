package mixc.be.rsms.business.domain.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 收款订单子表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_business_sub_order_receive")
public class SubOrderReceiveAddParams implements Serializable {

    @Serial
    private static final long serialVersionUID = -5145385761645337583L;

    /**
     * 支付模式:2-全额支付、1-分期
     */
    @Schema(description = "支付模式")
    private String payMode;

    /**
     * 费用项目id(来源：租售项目接口)
     */
    @Schema(description = "费用项目id")
    private String receiveCommunityId;

    /**
     * 收费项名称(来源：租售系统返回)
     */
    private String receiveCommunityName;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String goodName;

    /**
     * 折扣金额
     */
    @Schema(description = "折扣金额")
    private BigDecimal discountAmount;

    /**
     * 支付渠道
     */
    @Schema(description = "支付渠道")
    private String payChannel;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 业务记录Id
     */
    @Schema(description = "业务记录Id")
    private Long businessId;
}
