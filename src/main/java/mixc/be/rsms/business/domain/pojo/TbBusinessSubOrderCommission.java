package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <p>
 * 收佣订单子表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_business_sub_order_commission")
public class TbBusinessSubOrderCommission extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -5123171828327295078L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 主订单Id
     */
    @TableField("parent_order_id")
    private Long parentOrderId;

    /**
     * 子订单编号
     */
    @TableField("sub_order_code")
    private String subOrderCode;

    /**
     * 转出子订单ID
     */
    @TableField("export_sub_order_id")
    private Long exportSubOrderId;

    /**
     * 转出子订单号
     */
    @TableField("export_sub_order_code")
    private String exportSubOrderCode;

    /**
     * 转出金额
     */
    @TableField("export_amount")
    private BigDecimal exportAmount;

    /**
     * 订单状态:新建-create、进行中-running、已完成-ok、失败-error
     */
    @TableField("order_status")
    private String orderStatus;

    /**
     * 收费系统id
     */
    @TableField("charge_id")
    private Long chargeId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 业务记录Id
     */
    @TableField("business_id")
    private Long businessId;
}
