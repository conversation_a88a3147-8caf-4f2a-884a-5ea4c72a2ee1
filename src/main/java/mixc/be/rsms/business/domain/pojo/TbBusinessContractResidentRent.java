package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_business_contract_resident_rent")
public class TbBusinessContractResidentRent extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 8535567523672790699L;
	/**
	 * 主键
	 */
	@TableId(value = "id")
	private Long id;

	/**
	 * 合同ID
	 */
	@TableField("contract_id")
    private Long contractId;

	/**
	 * 甲方代理人姓名
	 */
	@TableField("leaser_proxy_user_name")
    private String leaserProxyUserName;

	/**
	 * 甲方代理人手机号码
	 */
	@TableField("leaser_proxy_user_mobile")
    private String leaserProxyUserMobile;

	/**
	 * 甲方代理人身份证号码
	 */
	@TableField("leaser_proxy_user_id_number")
    private String leaserProxyUserIdNumber;

	/**
	 * 乙方代理人姓名
	 */
	@TableField("customer_proxy_user_name")
    private String customerProxyUserName;

	/**
	 * 乙方代理人手机号码
	 */
	@TableField("customer_proxy_user_mobile")
    private String customerProxyUserMobile;

	/**
	 * 乙方代理人身份证号码
	 */
	@TableField("customer_proxy_user_id_number")
    private String customerProxyUserIdNumber;

	/**
	 * 共同购买人
	 */
	@TableField("co_purchaser")
    private String coPurchaser;

}