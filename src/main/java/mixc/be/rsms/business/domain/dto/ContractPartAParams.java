package mixc.be.rsms.business.domain.dto;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 甲方信息参数实体类
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractPartAParams implements Serializable  {

	private static final long serialVersionUID = -707384235660825651L;

	/**
     * 甲方类型
     */
	@NotBlank(message = "甲方(业主)姓名不能为空")
	@Schema(description = "甲方类型(字典：personal-个人、company-公司)", example = "personal", requiredMode = Schema.RequiredMode.REQUIRED)
    private String leaserType;
	
	/**
     * 甲方(业主)姓名
     */
	@NotBlank(message = "甲方(业主)姓名不能为空")
	@Schema(description = "甲方(业主)姓名", example = "张三", requiredMode = Schema.RequiredMode.REQUIRED)
    private String leaserUserName;
	
	/**
     * 甲方(业主)手机号码
     */
	@NotBlank(message = "甲方(业主)手机号码不能为空")
	@Schema(description = "甲方(业主)手机号码", example = "13245678123", requiredMode = Schema.RequiredMode.REQUIRED)
    private String leaserMobile;
    
	/**
     * 甲方证件类型
     */
	@Schema(description = "证件类型：字典", example = "idCard", requiredMode = Schema.RequiredMode.REQUIRED)
    private String identityType;
	
	/**
     * 甲方证件号码
     */
	@Schema(description = "证件号码（当甲方类型选择企业时此字段为：甲方营业执照编号）", example = "321909213456713456", requiredMode = Schema.RequiredMode.REQUIRED)
    private String identityNumber;
	
	/**
     * 甲方(业主)联系地址
     */
	@NotBlank(message = "甲方(业主)联系地址不能为空")
	@Schema(description = "甲方(业主)联系地址", example = "西安市莲湖区大庆路33号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String leaserAddress;
	
	/**
	 * 甲方代理人姓名
	 */
	@Schema(description = "甲方代理人姓名", example = "李四", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String leaserProxyUserName;
	
	/**
	 * 甲方代理人手机号码
	 */
	@Schema(description = "甲方代理人手机号码", example = "13324567891", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String leaserProxyUserMobile;
	
	/**
	 * 甲方代理人身份证号
	 */
	@Schema(description = "甲方代理人身份证号", example = "321909213456713452", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String leaserProxyUserIdNumber;
}
