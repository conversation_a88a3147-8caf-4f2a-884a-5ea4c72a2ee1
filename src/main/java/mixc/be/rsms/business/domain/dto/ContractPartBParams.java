package mixc.be.rsms.business.domain.dto;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 甲方信息参数实体类
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractPartBParams implements Serializable  {

	private static final long serialVersionUID = 4666712403593433678L;

	/**
     * 乙方类型
     */
	@Schema(description = "乙方类型(字典：personal-个人、company-公司)", example = "personal", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customerType;
	
	/**
	 * 乙方（客户）ID
	 */
	@NotNull(message = "乙方（客户）ID不能为空")
	@Schema(description = "乙方（客户）", example = "12", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long customerId;
	
	/**
	 * 乙方（客户）姓名
	 */
	@NotBlank(message = "乙方（客户）姓名不能为空")
	@Schema(description = "乙方（客户）姓名", example = "李四", requiredMode = Schema.RequiredMode.REQUIRED)
	private String customerName;
	
	/**
	 * 乙方（客户）手机号码
	 */
	@NotBlank(message = "乙方（客户）手机号码不能为空")
	@Schema(description = "乙方（客户）手机号码", example = "13245678123", requiredMode = Schema.RequiredMode.REQUIRED)
	private String customerMobile;
	
	/**
     * 乙方证件类型
     */
	@Schema(description = "证件类型：字典", example = "idCard", requiredMode = Schema.RequiredMode.REQUIRED)
    private String identityType;
	
	/**
     * 乙方证件号码
     */
	@Schema(description = "证件号码（当甲方类型选择企业时此字段为：乙方营业执照编号）", example = "321909213456713456", requiredMode = Schema.RequiredMode.REQUIRED)
    private String identityNumber;
	
	/**
	 * 乙方（客户）联系地址
	 */
	@NotBlank(message = "乙方（客户）联系地址不能为空")
	@Schema(description = "乙方（客户）联系地址", example = "西安市雁塔区", requiredMode = Schema.RequiredMode.REQUIRED)
	private String customerAddress;
	
	/**
	 * 乙方（客户）代理人姓名
	 */
	@Schema(description = "乙方（客户）代理人姓名", example = "王五", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String customerProxyUserName;
	
	/**
	 * 乙方（客户）代理人手机号
	 */
	@Schema(description = "乙方（客户）代理人手机号", example = "13246578901", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String customerProxyUserMobile;
	
	/**
	 * 乙方（客户）代理人身份证编号
	 */
	@Schema(description = "乙方（客户）代理人身份证编号", example = "321909213456713456", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String customerProxyUserIdNumber;
	
	/**
     * 共同购买人
     */
	@Schema(description = "共同购买人", example = "张三", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String coPurchaser;
}
