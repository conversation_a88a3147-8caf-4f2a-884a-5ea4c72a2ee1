package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 交易信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
@TableName("tb_business_transaction_info")
@EqualsAndHashCode(callSuper = true)
public class TbBusinessTransactionInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -710260921421200086L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 成交意向金/订金表Id
     */
    @TableField("deposit_id")
    private Long depositId;

    /**
     * 交易时间
     */
    @TableField("transaction_date_time")
    private LocalDateTime transactionDateTime;

    /**
     * 交易类型
     * t_type_01：收款、t_type_02：退回、t_type_03：转佣、t_type_03_01：住宅二手房买卖居间服务费、t_type_03_02：住宅二手房租赁居间服务费、t_type_03_03：车位二手房买卖居间服务费、t_type_03_04：车位二手房租赁居间服务费、t_type_03_05：金融机构居间服务费、t_type_03_06：商铺二手房租赁居间服务费、t_type_03_07：商铺二手房买卖居间服务费、t_type_03_08：写字楼二手房租赁居间服务费、t_type_03_09：写字楼二手房买卖居间服务费、t_type_03_10：车位新盘分销居间服务费
     */
    @TableField("transaction_type")
    private String transactionType;

    /**
     * 交易金额
     */
    @TableField("transaction_amount")
    private BigDecimal transactionAmount;

//    /**
//     * 剩余金额
//     */
//    @TableField("surplus_amount")
//    private BigDecimal surplusAmount;

    /**
     * 合同编号
     */
    @TableField("contract_num")
    private String contractNum;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

//    /**
//     * 附件的 FileKey
//     */
//    @TableField("enclosure_file_key")
//    private String enclosureFileKey;

    /**
     * 交易类型的状态 unmodified_pass:审核过但未修改过记录、modify_pass:审核过且修改过记录、insert:新增记录
     */
    @TableField("status")
    private String status;

    /**
     * 订单状态
     */
    @TableField("order_status")
    private String orderStatus;

    /**
     * 子订单ID
     */
    @TableField("sub_order_id")
    private Long subOrderId;

    /**
     * 子订单编号
     */
    @TableField("sub_order_code")
    private String subOrderCode;


    /**
     * 退款渠道
     */
    @TableField("refund_channel")
    private String refundChannel;

    /**
     * 收款银行账号
     */
    @TableField("bank_num")
    private String bankNum;

    /**
     * 收款账号名
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 收款开户行
     */
    @TableField("bank_name")
    private String bankName;

    /**
     * 收款账户城市
     */
    @TableField("city")
    private String city;

    /**
     * 收款金额
     */
    @TableField(exist = false)
    private BigDecimal receiveAmount;

    /**
     * 退款金额
     */
    @TableField(exist = false)
    private BigDecimal refundAmount;

    /**
     * 转佣金额
     */
    @TableField(exist = false)
    private BigDecimal commissionAmount;
}
