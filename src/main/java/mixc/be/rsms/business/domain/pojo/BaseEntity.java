package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-10-12 19:58
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseEntity implements Serializable {


    @Serial
    private static final long serialVersionUID = -7771730874277839229L;
    /**
     * 乐观锁：版本号
     */
    @Version
    private Integer revision;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Long updateUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记：0-否、1-是
     */
    @TableLogic
    private Integer isDeleted;

}
