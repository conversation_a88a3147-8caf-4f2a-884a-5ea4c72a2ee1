package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.time.LocalDate;
/**
 * 权证过户实体类
 * <AUTHOR>
 * @date 2024-10-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "tb_business_ownership_transfer")
public class TbBusinessOwnershipTransfer extends BaseEntity {

	@Serial
	private static final long serialVersionUID = -2067789260886774014L;
	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.INPUT)
	private Long id;

	/**
	 * 成交合同编号
	 */
	@TableField("transfer_contract_number")
    private String transferContractNumber;

    /**
     * 成交报告ID
     */
	@TableField("transaction_report_id")
    private Long transactionReportId;

    /**
     * 成交状态
     */
	@TableField("transfer_status")
    private String transferStatus;

    /**
     * 房源主键
     */
	@TableField("house_id")
    private Long houseId;

	/**
	 * 车位ID
	 */
	@TableField("parking_space_id")
	private Long parkingSpaceId;

    /**
     * 客户主键
     */
	@TableField("customer_id")
    private Long customerId;


	/**
	 * 楼栋
	 */
	@TableField("building")
	private String building;

	/**
	 * 单元
	 */
	@TableField("unit")
	private String unit;

	/**
	 * 房号
	 */
	@TableField("house_number")
	private String houseNumber;

	/**
	 * 新房项目ID
	 */
	@TableField("new_house_community_id")
	private Long newHouseCommunityId;

    /**
     * 预计过户完成时间
     */
	@TableField("expect_transfer_date")
    private LocalDate expectTransferDate;

    /**
     * 当前过户阶段：ts0-房屋挂牌、ts1-签订合同、ts2-抵押中过户、ts3-业主解抵押、ts4-签订网签合同、ts5-客户贷款面签、ts6-贷款审批通过、ts7-网上预审、ts8-网审通过预约、ts9-资金监管、ts10-过户递件、ts11-核税、ts12-领证、ts13-办理抵押、ts14-银行放款给卖方、ts15-物业交割、ts16-完成
     */
	@TableField("current_transfer_stage")
    private String currentTransferStage;

    /**
     * 当前阶段完成时间
     */
	@TableField("current_stage_finish_date")
    private LocalDate currentStageFinishDate;

    /**
     * 下个过户阶段：ts0-房屋挂牌、ts1-签订合同、ts2-抵押中过户、ts3-业主解抵押、ts4-签订网签合同、ts5-客户贷款面签、ts6-贷款审批通过、ts7-网上预审、ts8-网审通过预约、ts9-资金监管、ts10-过户递件、ts11-核税、ts12-领证、ts13-办理抵押、ts14-银行放款给卖方、ts15-物业交割、ts16-完成
     */
	@TableField("next_transfer_stage")
    private String nextTransferStage;

    /**
     * 备注
     */
	@TableField("remarks")
    private String remarks;
}