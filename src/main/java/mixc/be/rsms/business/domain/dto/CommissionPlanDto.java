package mixc.be.rsms.business.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-04 14:18
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommissionPlanDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 7302792243985092788L;

    /**
     * 成交报告表ID
     */
    private Long reportId;

    /**
     * 应收佣金汇总
     */
    private BigDecimal receivableCommissionTotal;

    /**
     * 实收金额汇总
     */
    private BigDecimal receivedCommissionTotal;


}
