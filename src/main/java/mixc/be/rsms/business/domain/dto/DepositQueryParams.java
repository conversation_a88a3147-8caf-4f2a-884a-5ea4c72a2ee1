package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import mixc.be.rsms.business.vo.RequestVo;

import java.io.Serial;

/**
 * <p>
 * 成交意向金/订金表 查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class DepositQueryParams extends RequestVo {


    @Serial
    private static final long serialVersionUID = -1631955002360931434L;
    /**
     * 编号
     */
    @Schema(description = "编号", example = "YXJ0001", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String depositCode;

    /**
     * 类型:(intention:意向金、deposit:订金)
     */
    @Schema(description = "类目(字典值)",example = "类型:(intention:意向金、deposit:订金)")
    private String type;

    /**
     * 类目:(dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖)
     */
    @Schema(description = "类目(字典值)", example = "dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String category;

    /**
     * 客户姓名关键字
     */
    @Schema(description = "客户姓名关键字", example = "张三", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String customerKeyWord;

    /**
     * 所属经纪人姓名关键字
     */
    @Schema(description = "所属经纪人姓名关键字", example = "王五", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String brokerNameKeyWord;

    /**
     * 所属经纪人Id列表
     */
    @Schema(description = "所属经纪人Id列表", example = "1,23,4,2,3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long[] brokerIds;

    /**
     * 状态:(unexamination:未审核、examinationing:审核中、examinationed:已审核)
     */
    @Schema(description = "状态", example = "unexamination:未审核、examinationing:审核中、examinationed:已审核", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String status;

    /**
     * 是否绑定成交报告 true: 绑定、false: 未绑定
     */
    @Schema(description = "是否绑定成交报告", example = "true: 绑定、false: 未绑定", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean bindingTransactionReport;
}
