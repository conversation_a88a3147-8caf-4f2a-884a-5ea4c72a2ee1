package mixc.be.rsms.business.domain.dto;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractFileInfoParam implements Serializable {

	@Serial
	private static final long serialVersionUID = 6874399171490484908L;

	/**
	 * 合同ID
	 */
	private Long contractId;
	
	/**
	 * 合同附件列表
	 */
	private List<FileInfoParam> houseFileList;
}
