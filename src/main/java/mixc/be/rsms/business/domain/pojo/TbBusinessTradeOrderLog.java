package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;

/**
 * <p>
 * 下单流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
@TableName("tb_business_trade_order_log")
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class TbBusinessTradeOrderLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 6131972453989155876L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主订单Id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 主订单编号
     */
    @TableField("order_code")
    private String orderCode;

    /**
     * 主订单流水号
     */
    @TableField("order_serial_number")
    private String orderSerialNumber;

    /**
     * 子订单Id
     */
    @TableField("sub_order_id")
    private Long subOrderId;

    /**
     * 子订单编号
     */
    @TableField("sub_order_code")
    private String subOrderCode;

    /**
     * 子订单流水号
     */
    @TableField("sub_order_serial_number")
    private String subOrderSerialNumber;

    /**
     * 状态：下单成功、下单失败、作废
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 请求参数
     */
    @TableField("req_body")
    private String reqBody;

    /**
     * 响应
     */
    @TableField("resp_body")
    private String respBody;

    /**
     * 记录修改是否来源于回调：0-否、1-是
     */
    @TableField("call_back_flag")
    private Boolean callBackFlag;

    /**
     * 记录修改是否来源于内部定时任务：0-否、1-是
     */
    @TableField("inner_task_flag")
    private Boolean innerTaskFlag;
}
