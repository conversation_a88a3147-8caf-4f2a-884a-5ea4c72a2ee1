package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_business_joy_contract_status_callback")
public class TbBusinessJoyContractStatusCallback extends BaseEntity {

	private static final long serialVersionUID = 4433816642216041224L;

	/**
	 * 主键
	 */
	@TableId(value = "id")
	private Long id;
	
	/**
	 * 朝昔合同ID
	 */
	@TableField(value = "joy_contract_id")
	private String joyContractId;

	/**
	 * 合同编号
	 */
	@TableField("status")
    private String status;

	/**
	 * 合同类型
	 */
	@TableField("body")
    private String body;

}