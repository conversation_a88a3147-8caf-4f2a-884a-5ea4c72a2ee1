package mixc.be.rsms.business.domain.dto;

import java.io.Serial;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileInfoParam implements Serializable {

	@Serial
	private static final long serialVersionUID = 7927383358813478252L;

	/**
	 * 文件ID
	 */
	@Schema(description = "文件ID", example = "1050780972368437248", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long fileId;
	
	/**
	 * 业务ID
	 */
	@Schema(description = "业务ID", example = "1050780972368437249", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Long businessId;
	
	/**
	 * 文件大小
	 */
	@Schema(description = "文件大小", example = "10", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String fileSize;
	
	/**
	 * 文件标签
	 */
	@Schema(description = "文件标签（归档的时候传:archive,其它为空）", example = "archive", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String fileTag;
	
	/**
	 * 文件类型
	 */
	@Schema(description = "文件类型", example = "image", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String fileType;
	
	/**
	 * 业务类型
	 */
	@Schema(description = "业务类型", example = "image", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String businessType;
}
