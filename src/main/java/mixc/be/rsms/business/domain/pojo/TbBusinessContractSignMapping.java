package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_business_contract_sign_mapping")
public class TbBusinessContractSignMapping extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 4811708352669301671L;

	/**
	 * 主键
	 */
	@TableId(value = "id")
    private Long id;

	/**
	 * 城市ID
	 */
	@TableField("city_id")
	private String cityId;

	/**
	 * 城市编码
	 */
	@TableField("city_code")
	private String cityCode;
	
	/**
	 * 城市名称
	 */
	@TableField("city_name")
	private String cityName;
	
	/**
	 * 业务类型
	 */
	@TableField("business_type")
	private String businessType;
	
	/**
	 * 业务类型名称
	 */
	@TableField("business_type_name")
	private String businessTypeName;

	/**
	 * 合同类型
	 */
	@TableField("contract_type")
	private String contractType;
	
	/**
	 * 合同类型名称
	 */
	@TableField("contract_type_name")
	private String contractTypeName;

	/**
	 * 模版ID
	 */
	@TableField("template_id")
	private Integer templateId;

	/**
	 * 模版编码
	 */
	@TableField("template_code")
	private String templateCode;

	/**
	 * 范围
	 */
	@TableField("scope")
	private String scope;

	/**
	 * 成交费用类型code
	 */
	@TableField("transaction_fee_type_code")
	private String transactionFeeTypeCode;

	/**
	 * 成交费用类型
	 */
	@TableField("transaction_fee_type")
	private String transactionFeeType;

	/**
	 * 成交分类
	 */
	@TableField("transaction_category")
	private String transactionCategory;

	/**
	 * 备注
	 */
	@TableField("remarks")
	private String remarks;

}