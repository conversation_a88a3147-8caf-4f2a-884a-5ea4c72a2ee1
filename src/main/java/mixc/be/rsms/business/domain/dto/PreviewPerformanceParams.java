package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2025/05/29
 */
@Data
public class PreviewPerformanceParams implements Serializable {
    @Serial
    private static final long serialVersionUID = 8799735789661066687L;

    @Schema(description = "成交分类 字典值： deposit_category")
    private String categoryCode;

    @Schema(description = "经纪人ID")
    private Long brokerId;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "资产ID(房源、车位、新房)")
    private Long assetsId;

    @Schema(description = "业主佣金")
    private BigDecimal ownerCommission;

    @Schema(description = "客户佣金")
    private BigDecimal customerCommission;

    @Schema(description = "营销金额")
    private BigDecimal marketingFee;
}
