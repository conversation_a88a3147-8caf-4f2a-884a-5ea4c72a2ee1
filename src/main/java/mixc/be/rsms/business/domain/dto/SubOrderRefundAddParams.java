package mixc.be.rsms.business.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 退款订单子表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class SubOrderRefundAddParams implements Serializable {


    @Serial
    private static final long serialVersionUID = -8482877128025203806L;

    /**
     * 收款订单Id
     */
    @Schema(description = "收款订单Id")
    private Long receiveSubOrderId;

    /**
     * 退款金额
     */
    @Schema(description = "退款金额")
    private BigDecimal amount;

    /**
     * 退款渠道
     */
    @Schema(description = "退款渠道")
    private String refundChannel;

    /**
     * 收款银行账号
     */
    @Schema(description = "收款银行账号")
    private String bankNum;

    /**
     * 收款账号名
     */
    @Schema(description = "收款账号名")
    private String accountName;

    /**
     * 收款开户行
     */
    @Schema(description = "收款开户行")
    private String bankName;

    /**
     * 收款账户城市
     */
    @Schema(description = "收款账户城市")
    private String city;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;


    /**
     * 业务记录Id
     */
    @TableField("business_id")
    private Long businessId;

}
