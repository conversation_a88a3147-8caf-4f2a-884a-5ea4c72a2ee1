package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 交易信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
public class TransactionInfoAddParams implements Serializable {


    @Serial
    private static final long serialVersionUID = -512634119529910450L;
    /**
     * 主键
     */
    @Schema(description = "主键ID(存在则为修改;不存在则新增)", example = "223342", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long id;

    /**
     * 成交意向金/订金表Id
     */
    @Schema(description = "成交意向金/订金表Id", example = "223342", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long depositId;

    /**
     * 交易类型 (t_type_01：收款、t_type_02：退回、t_type_03：转佣、t_type_03_01：住宅二手房买卖居间服务费、t_type_03_02：住宅二手房租赁居间服务费、t_type_03_03：车位二手房买卖居间服务费、t_type_03_04：车位二手房租赁居间服务费、t_type_03_05：金融机构居间服务费、t_type_03_06：商铺二手房租赁居间服务费、t_type_03_07：商铺二手房买卖居间服务费、t_type_03_08：写字楼二手房租赁居间服务费、t_type_03_09：写字楼二手房买卖居间服务费、t_type_03_10：车位新盘分销居间服务费)
     */
    @Schema(description = "交易类型(字典值)", example = "t_type_01：收款、t_type_02：退回、t_type_03：转佣、t_type_03_01：住宅二手房买卖居间服务费、t_type_03_02：住宅二手房租赁居间服务费、t_type_03_03：车位二手房买卖居间服务费、t_type_03_04：车位二手房租赁居间服务费、t_type_03_05：金融机构居间服务费、t_type_03_06：商铺二手房租赁居间服务费、t_type_03_07：商铺二手房买卖居间服务费、t_type_03_08：写字楼二手房租赁居间服务费、t_type_03_09：写字楼二手房买卖居间服务费、t_type_03_10：车位新盘分销居间服务费", requiredMode = Schema.RequiredMode.REQUIRED)
    private String transactionType;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额", example = "100.00", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal transactionAmount;

    /**
     * 成交合同编号/成交报告
     */
    @Schema(description = "合同编号", example = "22eewwssd", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contractNum;

    /**
     * 合同附件Id
     */
    @Schema(description = "合同附件ID(文件上传接口返回的对象ID)", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long enclosureFileId;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "测试记录", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remark;

    /**
     * 交易类型的状态 unmodified_pass:审核过但未修改过记录、modify_pass:审核过且修改过记录、insert:新增记录
     */
    @Schema(description = "交易类型的状态(字典值)", example = "unmodified_pass:审核过但未修改过记录、modify_pass:审核过且修改过记录、insert:新增记录", requiredMode = Schema.RequiredMode.REQUIRED)
    private String status;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态(字典值-transaction_order_status)", example = "unpay-未支付")
    private String orderStatus;

    /**
     * 子订单ID
     */
    @Schema(description = "子订单ID", example = "***********")
    private Long subOrderId;

    /**
     * 子订单编号
     */
    @Schema(description = "子订单编号", example = "ZDD2929384527")
    private String subOrderCode;


    /**
     * 退款渠道
     */
    @Schema(description = "退款渠道")
    private String refundChannel;

    /**
     * 收款银行账号
     */
    @Schema(description = "收款银行账号")
    private String bankNum;

    /**
     * 收款账号名
     */
    @Schema(description = "收款账号名")
    private String accountName;

    /**
     * 收款开户行
     */
    @Schema(description = "收款开户行")
    private String bankName;

    /**
     * 收款账户城市
     */
    @Schema(description = "收款账户城市")
    private String city;

}
