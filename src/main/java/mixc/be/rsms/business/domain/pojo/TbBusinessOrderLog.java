package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单报文日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@TableName("tb_business_order_log")
@EqualsAndHashCode(callSuper = false)
public class TbBusinessOrderLog extends BaseEntity{


    @Serial
    private static final long serialVersionUID = 4207806530890674377L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 订单Id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 子订单Id
     */
    @TableField("sub_order_id")
    private Long subOrderId;

    /**
     * 订单编号
     */
    @TableField("order_code")
    private String orderCode;

    /**
     * 子订单编号
     */
    @TableField("sub_order_code")
    private String subOrderCode;

    /**
     * 请求报文
     */
    @TableField("req_body")
    private String reqBody;

    /**
     * 请求时间
     */
    @TableField("req_time")
    private LocalDateTime reqTime;

    /**
     * 响应报文
     */
    @TableField("resp_body")
    private String respBody;

    /**
     * 响应时间
     */
    @TableField("resp_time")
    private LocalDateTime respTime;

    /**
     * 响应状态:success-成功error-失败
     */
    @TableField("resp_status")
    private String respStatus;

    /**
     * 重试次数
     */
    @TableField("retry")
    private Integer retry;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
