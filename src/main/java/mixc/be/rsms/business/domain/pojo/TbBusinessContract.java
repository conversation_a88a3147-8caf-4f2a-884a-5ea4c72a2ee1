package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_business_contract")
public class TbBusinessContract extends BaseEntity {

	@Serial
	private static final long serialVersionUID = -9210294797706691481L;


	/**
	 * 主键
	 */
	@TableId(value = "id")
	private Long id;
	
	/**
	 * 朝昔合同ID
	 */
	@TableField(value = "joy_contract_id")
	private String joyContractId;

	/**
	 * 合同编号
	 */
	@TableField("contract_number")
    private String contractNumber;

	/**
	 * 合同类型
	 */
	@TableField("contract_type")
    private String contractType;

	/**
	 * 合同状态
	 */
	@TableField("contract_status")
    private String contractStatus;

	/**
	 * 房源ID
	 */
	@TableField("house_id")
    private Long houseId;
	
	/**
	 * 车位ID
	 */
	@TableField("parking_space_id")
    private Long parkingSpaceId;
	
	/**
	 * 新房项目ID
	 */
	@TableField("new_house_community_id")
	private Long newHouseCommunityId;
	
	/**
	 * 新房-栋
	 */
	@TableField("building")
	private String building;
	
	/**
	 * 新房-单元
	 */
	@TableField("unit")
	private String unit;
	
	/**
	 * 新房-房号
	 */
	@TableField("house_number")
	private String houseNumber;

	/**
	 * 居间方公司ID
	 */
	@TableField("intermediary_company_id")
    private Long intermediaryCompanyId;

	/**
	 * 甲方类型：personal-个人、company-公司
	 */
	@TableField("leaser_type")
	private String leaserType;
	
	/**
	 * 甲方(业主)ID
	 */
	@TableField("leaser_id")
    private Long leaserId;
	
	/**
	 * 甲方证件类型：id_card-身份证、passport-护照
	 */
	@TableField("leaser_id_type")
	private String leaserIdType;
	
	/**
	 * 甲方(业主)姓名
	 */
	@TableField("leaser_user_name")
	private String leaserUserName;

	/**
	 * 甲方手机号码
	 */
	@TableField("leaser_mobile")
    private String leaserMobile;

	/**
	 * 甲方身份证号码
	 */
	@TableField("leaser_id_number")
    private String leaserIdNumber;

	/**
	 * 甲方联系地址
	 */
	@TableField("leaser_address")
    private String leaserAddress;
	
	/**
	 * 甲方代理人姓名
	 */
	@TableField("leaser_proxy_user_name")
    private String leaserProxyUserName;
	
	/**
	 * 甲方代理人手机号码
	 */
	@TableField("leaser_proxy_user_mobile")
    private String leaserProxyUserMobile;

	/**
	 * 甲方代理人身份证号码
	 */
	@TableField("leaser_proxy_user_id_number")
    private String leaserProxyUserIdNumber;
	
	/**
	 * 客户类型：personal-个人、company-公司
	 */
	@TableField("customer_type")
	private String customerType;
	
	/**
	 * 乙方ID
	 */
	@TableField("customer_id")
    private Long customerId;
	
	/**
	 * 乙方证件类型：id_card-身份证、passport-护照
	 */
	@TableField("customer_id_type")
	private String customerIdType; 

	/**
	 * 乙方手机号码
	 */
	@TableField("customer_mobile")
    private String customerMobile;

	/**
	 * 乙方身份证号码
	 */
	@TableField("customer_id_number")
    private String customerIdNumber;

	/**
	 * 乙方联系地址
	 */
	@TableField("customer_address")
    private String customerAddress;
	
	/**
	 * 乙方代理人姓名
	 */
	@TableField("customer_proxy_user_name")
    private String customerProxyUserName;
	
	/**
	 * 乙方代理人手机号码
	 */
	@TableField("customer_proxy_user_mobile")
    private String customerProxyUserMobile;

	/**
	 * 乙方代理人身份证号码
	 */
	@TableField("customer_proxy_user_id_number")
    private String customerProxyUserIdNumber;
	
	/**
	 * 共同购买人
	 */
	@TableField("co_purchaser")
    private String coPurchaser;
	
	/**
	 * 签约日期
	 */
	@TableField("contract_sign_date")
    private LocalDateTime contractSignDate;

	/**
	 * 成交价格
	 */
	@TableField("transaction_price")
    private BigDecimal transactionPrice;

	/**
	 * 业主佣金
	 */
	@TableField("leaser_commission")
    private BigDecimal leaserCommission;

	/**
	 * 客户佣金
	 */
	@TableField("customer_commission")
    private BigDecimal customerCommission;

}