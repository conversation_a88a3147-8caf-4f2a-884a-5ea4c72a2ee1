package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 成交报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_business_transaction_report")
public class TbBusinessTransactionReport extends BaseEntity {


    @Serial
    private static final long serialVersionUID = 7959147555340188383L;
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 成交报告编码 CJ+当天日期+6位数字
     */
    @TableField(value = "report_code")
    private String reportCode;

    /**
     * 成交合同编号
     */
    @TableField("transaction_contract_num")
    private String transactionContractNum;

    /**
     * 类目:(dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖)
     */
    @TableField("category")
    private String category;

    /**
     * 客户ID
     */
    @TableField("customer_id")
    private Long customerId;

    /**
     * 房源ID
     */
    @TableField("house_id")
    private Long houseId;

    /**
     * 车位Id
     */
    @TableField("parking_space_id")
    private Long parkingSpaceId;

    /**
     * 业主信息
     */
    @TableField("owner_info")
    private String ownerInfo;

    /**
     * 成交日期
     */
    @TableField("deal_date")
    private LocalDate dealDate;

    /**
     * 成交金额
     */
    @TableField("deal_amount")
    private BigDecimal dealAmount;

    /**
     * 成交报告状态：unaudit-未审核，auditing-审核中，reviewing-复核中，reviewed-已复核，closed-已结案
     */
    @TableField("status")
    private String status;

    /**
     * 经纪人ID
     */
    @TableField("broker_id")
    private Long brokerId;

    /**
     * 业主佣金
     */
    @TableField("owner_commission")
    private BigDecimal ownerCommission;

    /**
     * 客户佣金
     */
    @TableField("customer_commission")
    private BigDecimal customerCommission;

    /**
     * 营销费用
     */
    @TableField("marketing_fee")
    private BigDecimal marketingFee;

    /**
     * 土地证号
     */
    @TableField("land_certificate_num")
    private String landCertificateNum;

    /**
     * 产权证号
     */
    @TableField("property_right_num")
    private String propertyRightNum;

    /**
     * 权证人
     */
    @TableField("warrant_holder")
    private String warrantHolder;

    /**
     * 意向金/订金ID
     */
    @TableField("deposit_id")
    private Long depositId;

    /**
     * 其他支出
     */
    @TableField("other_expenses")
    private BigDecimal otherExpenses;

    /**
     * 补充条款
     */
    @TableField("supplementary_terms")
    private String supplementaryTerms;

    /**
     * 签约日期
     */
    @TableField("sign_date")
    private LocalDate signDate;

    /**
     * 楼栋
     */
    @TableField("building")
    private String building;

    /**
     * 单元
     */
    @TableField("unit")
    private String unit;

    /**
     * 房号
     */
    @TableField("house_number")
    private String houseNumber;

    /**
     * 新房项目ID
     */
    @TableField("new_house_community_id")
    private Long newHouseCommunityId;


    /**
     * 可分成业绩(三期字段)
     */
    @TableField("can_divide_achievement")
    private BigDecimal canDivideAchievement;

    /**
     * 提成结算状态(字典值: settlement_status)
     */
    @TableField("commission_settle_status")
    private String commissionSettleStatus;

    /**
     * 签约业绩结算状态(字典值: settlement_status)
     */
    @TableField("contract_per_settle_status")
    private String contractPerSettleStatus;

    /**
     * 实收业绩结算状态(字典值: settlement_status)
     */
    @TableField("actual_per_settle_status")
    private String actualPerSettleStatus;

    /**
     * 核算业绩结算状态(字典值: settlement_status)
     */
    @TableField("accounting_per_settle_status")
    private String accountingPerSettleStatus;

    /**
     * 复核通过时间
     */
    @TableField("review_date_time")
    private LocalDateTime reviewDateTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 可结案标记
     */
    @TableField(exist = false)
    private Integer closedSort;

    /**
     * 状态排序字段
     */
    @TableField(exist = false)
    private Integer statusSort;

    /**
     * 待收佣金
     */
    @TableField(exist = false)
    private BigDecimal awaitCommission;

    /**
     * 收齐日期
     */
    @TableField("collect_all_date")
    private LocalDateTime collectAllDate;

    /**
     * 业绩分配标志 自动或者手动
     */
    @TableField("allocation_flag")
    private String allocationFlag;

}
