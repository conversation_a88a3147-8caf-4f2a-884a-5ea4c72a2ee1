package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <p>
 * 订单回调记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Data
@TableName("tb_business_order_callback")
@EqualsAndHashCode(callSuper = false)
public class TbBusinessOrderCallback extends BaseEntity {


    @Serial
    private static final long serialVersionUID = -7863090961438734638L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 主流水号
     */
    @TableField("out_trade_no")
    private String outTradeNo;

    /**
     * 子流水号
     */
    @TableField("sub_out_trade_no")
    private String subOutTradeNo;

    /**
     * 本笔订单支付金额  以 分 为单位
     */
    @TableField("total_fee")
    private BigDecimal totalFee;

    /**
     * 状态：SUCCESS-成功、FAIL-失败、CLOSED-关闭
     */
    @TableField("status")
    private String status;

    /**
     * 回调原始报文
     */
    @TableField("body")
    private String body;

//    /**
//     * 回调支付方式：字典值：pay_mode
//     */
//    @TableField("callback_source")
//    private String callbackSource;

    /**
     * 回调返回
     */
    @TableField("response")
    private String response;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
