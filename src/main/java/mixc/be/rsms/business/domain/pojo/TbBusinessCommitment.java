package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 提成主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@TableName("tb_business_commitment")
@EqualsAndHashCode(callSuper = true)
public class TbBusinessCommitment extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 5558133304667415351L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 提成编码
     */
    @TableField("commitment_code")
    private String commitmentCode;

    /**
     * 提成类型
     */
    @TableField("commitment_type")
    private String commitmentType;

    /**
     * 提成金额合计
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 结账日期
     */
    @TableField("checkout_day")
    private LocalDateTime checkoutDay;

    /**
     * 结账月份
     */
    @TableField("checkout_month")
    private String checkoutMonth;

    /**
     * 1-已发放、0-未发放，默认未发放
     */
    @TableField("distribution_status")
    private Integer distributionStatus;
}
