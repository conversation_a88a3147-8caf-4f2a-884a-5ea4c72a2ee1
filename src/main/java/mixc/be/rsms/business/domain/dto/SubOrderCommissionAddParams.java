package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 收佣订单子表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class SubOrderCommissionAddParams implements Serializable {

    @Serial
    private static final long serialVersionUID = 2909917166780934798L;

    /**
     * 转出子订单ID
     */
    @Schema(description = "转出子订单ID")
    private Long exportSubOrderId;

    /**
     * 转出子订单号
     */
    @Schema(description = "转出子订单号")
    private String exportSubOrderCode;

    /**
     * 转出金额
     */
    @Schema(description = "转出金额")
    private BigDecimal exportAmount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 业务记录Id
     */
    @Schema(description = "业务记录Id")
    private Long businessId;
}
