package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * <p>
 * 成交意向金/订金表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
@TableName("tb_business_deposit")
@EqualsAndHashCode(callSuper = true)
public class TbBusinessDeposit extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 2044251641458086218L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 编号
     */
    @TableField("deposit_code")
    private String depositCode;

    /**
     * 类型:(intention:意向金、deposit:订金)
     */
    @TableField("type")
    private String type;

    /**
     * 类目:(dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖)
     */
    @TableField("category")
    private String category;

    /**
     * 成交合同编号
     */
    @TableField("transaction_contract_num")
    private String transactionContractNum;

    /**
     * 关联房源Arrays 的拼接字符串
     */
    @TableField("house_desc")
    private String houseDesc;

    /**
     * 关联车位Arrays 的拼接字符串
     */
    @TableField("parking_space_desc")
    private String parkingSpaceDesc;

    /**
     * 客户Id
     */
    @TableField("customer_id")
    private Long customerId;

    /**
     * 客户联系方式
     */
    @TableField("customer_mobile")
    private String customerMobile;

    /**
     * 所属经纪人Id
     */
    @TableField("broker_id")
    private Long brokerId;

    /**
     * 状态:(unexamination:未审核、examinationing:审核中、examinationed:已审核)
     */
    @TableField("status")
    private String status;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 主订单Id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 主订单编号
     */
    @TableField("order_code")
    private String orderCode;
}
