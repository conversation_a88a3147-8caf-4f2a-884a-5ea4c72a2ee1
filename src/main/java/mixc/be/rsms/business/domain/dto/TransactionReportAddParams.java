package mixc.be.rsms.business.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/21
 */
@Data
public class TransactionReportAddParams implements Serializable {

    @Serial
    private static final long serialVersionUID = 3421575054430339372L;
    /**
     * 主键ID
     */
    @Schema(description = "主键ID(区分新增、修改)", example = "344533", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long id;

    /**
     * 成交合同编号
     */
    @Schema(description = "成交合同编号", example = "dde43935344533", requiredMode = Schema.RequiredMode.REQUIRED)
    private String transactionContractNum;

    /**
     * 类目:(dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖)
     */
    @Schema(description = "类目(字典值)", example = "dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖", requiredMode = Schema.RequiredMode.REQUIRED)
    private String category;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID", example = "33532", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long customerId;

    /**
     * 房源ID
     */
    @Schema(description = "房源ID", example = "33532", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long houseId;

    /**
     * 车位ID
     */
    @Schema(description = "车位ID", example = "33532", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long parkingSpaceId;

    /**
     * 业主信息
     */
    @Schema(description = "业主信息", example = "王小明|13528382942", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ownerInfo;

    /**
     * 成交日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "成交日期", example = "2024-09-08", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate dealDate;

    /**
     * 成交金额
     */
    @Schema(description = "成交金额", example = "92.99", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal dealAmount;

    /**
     * 经纪人ID
     */
    @Schema(description = "经纪人ID", example = "4452", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long brokerId;



    /**
     * 业主佣金
     */
    @Schema(description = "业主佣金", example = "10.99", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal ownerCommission;

    /**
     * 客户佣金
     */
    @Schema(description = "客户佣金", example = "10.99", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal customerCommission;

    /**
     * 营销费用
     */
    @Schema(description = "营销费用", example = "100.00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal marketingFee;

    /**
     * 业绩分配标志：auto-自动、manual-手工
     */
    @Schema(description = "业绩分配标志：auto-自动、manual-手工", example = "true", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean allocationFlag;

    private List<ManualAllocationParamsParams> manualAllocation;
    /**
     * 土地证号
     */
    @Schema(description = "土地证号", example = "29394828kskjfusjs", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String landCertificateNum;

    /**
     * 产权证号
     */
    @Schema(description = "产权证号", example = "29394828kskjfusjs", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String propertyRightNum;

    /**
     * 权证人
     */
    @Schema(description = "权证人", example = "猪八戒", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String warrantHolder;

    /**
     * 意向金/订金ID
     */
    @Schema(description = "意向金/订金ID", example = "22839248", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long depositId;

    /**
     * 其他支出
     */
    @Schema(description = "其他支出", example = "10.99", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal otherExpenses;

    /**
     * 补充条款
     */
    @Schema(description = "补充条款", example = "还要再交钱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String supplementaryTerms;

    /**
     * 合同附件
     */
    @Schema(description = "合同附件数组(通用文件接口返回的 fileId 字段)", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<TransactionReportFileAddParams> reportFiles;

    /**
     * 签约日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "签约日期", example = "2024-09-08", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate signDate;

    /**
     * 楼栋
     */
    @Schema(description = "新房项目-楼栋", example = "24", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String building;

    /**
     * 单元
     */
    @Schema(description = "新房项目-单元", example = "3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String unit;

    /**
     * 房号
     */
    @Schema(description = "新房项目-房号", example = "601", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String houseNumber;

    /**
     * 新房项目Id
     */
    @Schema(description = "新房项目-项目Id", example = "1244222499288", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long newHouseCommunityId;

    /**
     * 合同双方信息
     */
    @Schema(description = "合同双方信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private ContractInfoAddParams contractInfo;


    /**
     * 收佣信息
     */
    @Schema(description = "收佣信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<ReceiveCommissionPlanAddParams> receiveCommissionPlans;
}
