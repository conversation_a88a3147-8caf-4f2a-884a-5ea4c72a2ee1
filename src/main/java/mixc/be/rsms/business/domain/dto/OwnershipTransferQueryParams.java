package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import mixc.be.rsms.business.vo.RequestVo;

import java.io.Serial;
import java.io.Serializable;

/**
 * 权证过户分页查询参数实体类
 * <AUTHOR>
 * @since 2024-10-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OwnershipTransferQueryParams extends RequestVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -6162230704733659885L;
    /**
	 * 成交合同编号
	 */
	@Schema(description = "成交合同编号", example = "HT001", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String transferContractNumber;
    
    /**
     * 客户名称
     */
    @Schema(description = "客户名称", example = "张三", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String customerName;

    /**
     * 房源
     */
    @Schema(description = "房源", example = "1栋1单元22层|2204|东里小区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String houseName;
    
    /**
     * 成交状态：unaudit-未审核、auditing-审核中、reviewing-复核中、reviewed-已复核、closed-已结案
     */
    @Schema(description = "成交状态(字典：unaudit-未审核、auditing-审核中、reviewing-复核中、reviewed-已复核、closed-已结案)", example = "unaudit", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String transferStatus;
    
    /**
     * 当前过户阶段
     */
    @Schema(description = "当前过户阶段(字典：ts0-房屋挂牌、ts1-签订合同、ts2-抵押中过户、ts3-业主解抵押、ts4-签订网签合同、ts5-客户贷款面签、ts6-贷款审批通过、ts7-网上预审、ts8-网审通过预约、ts9-资金监管、ts10-过户递件、ts11-核税、ts12-领证、ts13-办理抵押、ts14-银行放款给卖方、ts15-物业交割、ts16-完成)", example = "1栋1单元22层|2204|东里小区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String currentTransferStage;

}
