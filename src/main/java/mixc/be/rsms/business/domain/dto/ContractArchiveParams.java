package mixc.be.rsms.business.domain.dto;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 合同归档参数实体类
 * <AUTHOR>
 * @date 2025-02-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractArchiveParams implements Serializable {

	@Serial
	private static final long serialVersionUID = 4474220494285741177L;

	/**
	 * 合同主键ID
	 */
	@NotNull(message = "合同主键不能为空")
	@Schema(description = "合同主键ID", example = "1050780972368437248", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long contractId;
	
	/**
	 * 甲方房屋产权资料复印件
	 */
	@Schema(description = "甲方房屋产权资料复印件(根据合同类型传，没有就不传)", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private List<FileInfoParam> leaserOwnershipAttachment;
	
	/**
	 * 甲乙双方身份证明复印件
	 */
	@Schema(description = "甲乙双方身份证明复印件(根据合同类型传，没有就不传)", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private List<FileInfoParam> identityAttachment;
	
	/**
	 * 房屋交接清单
	 */
	@Schema(description = "房屋交接清单(根据合同类型传，没有就不传)", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private List<FileInfoParam> houseHandoverAttachment;
	
	/**
	 * 乙方身份证明附件
	 */
	@Schema(description = "乙方身份证明附件(根据合同类型传，没有就不传)", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private List<FileInfoParam> customerIdentityAttachment;
	
	/**
	 * 甲方车位产权资料复印件
	 */
	@Schema(description = "甲方车位产权资料复印件(根据合同类型传，没有就不传)", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private List<FileInfoParam> leaserCarOwnershipAttachment;
	
	/**
	 * 乙方车辆资料复印件
	 */
	@Schema(description = "乙方车辆资料复印件(根据合同类型传，没有就不传)", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private List<FileInfoParam> customerCarInfoAttachment;
	
	/**
	 * 补充条款
	 */
	@Schema(description = "补充条款(根据合同类型传，没有就不传)", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private List<FileInfoParam> supplementaryTermsAttachment;
	
	/**
	 * 房屋平面图、装修一览表
	 */
	@Schema(description = "房屋平面图、装修一览表(根据合同类型传，没有就不传)", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private List<FileInfoParam> houseDesignAndDecorationAttachment;
	
}
