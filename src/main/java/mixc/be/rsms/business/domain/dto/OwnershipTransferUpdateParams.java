package mixc.be.rsms.business.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 权证过户更新参数实体类
 * <AUTHOR>
 * @since 2024-10-20
 */
@Data
public class OwnershipTransferUpdateParams implements Serializable {

    @Serial
    private static final long serialVersionUID = 6509614445943487481L;
    /**
	 * 主键
	 */
    @Schema(description = "主键(新增为空，更新时不能为空)", example = "134345345", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Long id;
    
	/**
	 * 成交合同编号
	 */
    @Schema(description = "成交合同编号", example = "134345345", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String transferContractNumber;

    /**
     * 预计过户完成时间
     */
    @NotNull(message = "预计过户完成时间不能为空")
    @Schema(description = "预计过户完成时间", example = "2024-10-20", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate expectTransferDate;

    /**
     * 当前过户阶段：ts0-房屋挂牌、ts1-签订合同、ts2-抵押中过户、ts3-业主解抵押、ts4-签订网签合同、ts5-客户贷款面签、ts6-贷款审批通过、ts7-网上预审、ts8-网审通过预约、ts9-资金监管、ts10-过户递件、ts11-核税、ts12-领证、ts13-办理抵押、ts14-银行放款给卖方、ts15-物业交割、ts16-完成
     */
    @NotBlank(message = "当前过户阶段不能为空")
    @Schema(description = "当前过户阶段(字典：ts0-房屋挂牌、ts1-签订合同、ts2-抵押中过户、ts3-业主解抵押、ts4-签订网签合同、ts5-客户贷款面签、ts6-贷款审批通过、ts7-网上预审、ts8-网审通过预约、ts9-资金监管、ts10-过户递件、ts11-核税、ts12-领证、ts13-办理抵押、ts14-银行放款给卖方、ts15-物业交割、ts16-完成)", example = "ts1", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currentTransferStage;

    /**
     * 当前阶段完成时间
     */
    @NotNull(message = "当前过户完成时间不能为空")
    @Schema(description = "当前过户完成时间", example = "2024-10-20", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate currentStageFinishDate;

    /**
     * 下个过户阶段：ts0-房屋挂牌、ts1-签订合同、ts2-抵押中过户、ts3-业主解抵押、ts4-签订网签合同、ts5-客户贷款面签、ts6-贷款审批通过、ts7-网上预审、ts8-网审通过预约、ts9-资金监管、ts10-过户递件、ts11-核税、ts12-领证、ts13-办理抵押、ts14-银行放款给卖方、ts15-物业交割、ts16-完成
     */
    @Schema(description = "下个过户阶段(【当前过户阶段】选择【完成】，则该字段原值清空，且禁填)", example = "ts3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String nextTransferStage;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "备注1111", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remarks;
    
    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer revision;

}
