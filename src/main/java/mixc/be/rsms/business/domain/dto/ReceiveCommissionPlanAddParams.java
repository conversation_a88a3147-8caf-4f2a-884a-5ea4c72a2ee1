package mixc.be.rsms.business.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/21
 */
@Data
public class ReceiveCommissionPlanAddParams implements Serializable {


    @Serial
    private static final long serialVersionUID = -2391888139251050519L;
    /**
     * 费用项目名称(cost_item_1：住宅二手房买卖居间服务费、cost_item_2：住宅二手房租赁居间服务费、cost_item_3：车位二手房买卖居间服务费、cost_item_4：车位二手房租赁居间服务费、cost_item_5：金融机构居间服务费、cost_item_6：商铺二手房租赁居间服务费、cost_item_7：商铺二手房买卖居间服务费、cost_item_8：写字楼二手房租赁居间服务费、cost_item_9：写字楼二手房买卖居间服务费、cost_item_10：车位新盘分销居间服务费)
     */
    @Schema(description = "费用项目描述(收费系统返回)",  requiredMode = Schema.RequiredMode.REQUIRED)
    private String costItemDesc;

    /**
     * 费用项目表Id
     */
    @Schema(description = "收费系统 费用项目ID(收费系统返回)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String costItemId;

    /**
     * 租售系统费用项目 code
     */
    @Schema(description = "租售系统费用项目code", requiredMode = Schema.RequiredMode.REQUIRED)
    private String costItemCode;

    /**
     * 交费人：（owner:业主/customer:客户/other:第三方）
     */
    @Schema(description = "交费人(字典值)", example = "owner:业主/customer:客户/other:第三方", requiredMode = Schema.RequiredMode.REQUIRED)
    private String payer;

    /**
     * 应收佣金
     */
    @Schema(description = "应收佣金", example = "1000.00", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal receivableCommission;

    /**
     * 预期交费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "预期交费时间", example = "2024-09-28", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate expectPayment;

    /**
     * 实收金额
     */
    @Schema(description = "实收金额", example = "10.99", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal receivedCommission;

    /**
     * 实际交费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "实际交费时间", example = "2024-09-28", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate actualPayment;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "先交一部分", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remark;

    /**
     * 附件fileId
     */
    @Schema(description = "附件fileId(通用文件上传接口返回的 fileId)", example = "22342", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long enclosureFileId;

    /**
     * 意向金转佣 对应的意向金表ID
     */
    @Schema(description = "意向金转佣 对应的意向金表ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long depositId;

    /**
     * 主订单Id
     */
    @Schema(description = "主订单Id")
    private Long orderId;

    /**
     * 主订单编号
     */
    @Schema(description = "主订单编号")
    private String orderCode;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态")
    private String orderStatus;

    /**
     * 子订单ID
     */
    @Schema(description = "子订单ID")
    private Long subOrderId;

    /**
     * 子订单编号
     */
    @Schema(description = "子订单编号")
    private String subOrderCode;
}
