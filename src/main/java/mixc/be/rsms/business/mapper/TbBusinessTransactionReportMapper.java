package mixc.be.rsms.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import mixc.be.rsms.business.domain.dto.TransactionReportQueryParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 成交报告表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
public interface TbBusinessTransactionReportMapper extends BaseMapper<TbBusinessTransactionReport> {

    IPage<TbBusinessTransactionReport> selectByEntity(@Param("page") Page<TbBusinessTransactionReport> page,
                                                      @Param("dto") TransactionReportQueryParams params,
                                                      @Param("permissionUserIdList")List<Long> permissionUserIdList);

    List<TbBusinessTransactionReport> selectInfoOnOperateLog(@Param("businessIdList") List<Long> businessIdList);

}
