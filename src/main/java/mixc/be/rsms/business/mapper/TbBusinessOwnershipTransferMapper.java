package mixc.be.rsms.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import mixc.be.rsms.business.domain.dto.OwnershipTransferQueryParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessOwnershipTransfer;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权证过户接口
 * <AUTHOR>
 * @date 2024-10-20
 */
public interface TbBusinessOwnershipTransferMapper extends BaseMapper<TbBusinessOwnershipTransfer> {

	/**
	 * 权证过户分页查询
	 *
	 * @param page                 分页参数
	 * @param params               查询参数
	 * @param permissionUserIdList 权限用户ID集合
	 * @return
	 */
	IPage<TbBusinessOwnershipTransfer> pageOwnershipTransfer(@Param("page") Page<OwnershipTransferQueryParams> page,
													 @Param("params") OwnershipTransferQueryParams params,
													 @Param("userIdList") List<Long> permissionUserIdList,
													 @Param("keyWordHouseIds") List<Long> keyWordHouseIds,
													 @Param("customerIds") List<Long> customerIds);

}