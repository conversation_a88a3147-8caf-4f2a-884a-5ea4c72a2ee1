package mixc.be.rsms.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import mixc.be.rsms.business.domain.dto.DepositQueryParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessDeposit;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 成交意向金/订金表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
public interface TbBusinessDepositMapper extends BaseMapper<TbBusinessDeposit> {

    IPage<TbBusinessDeposit> selectPages(@Param("page") Page<TbBusinessDeposit> page
            , @Param("params") DepositQueryParams params
            , @Param("permissionUserIdList") List<Long> permissionUserIdList
            , @Param("customerIdList") List<Long> customerIdList);
}
