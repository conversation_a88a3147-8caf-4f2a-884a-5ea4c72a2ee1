package mixc.be.rsms.business.utils;

import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.feignclient.AuthorServerClient;
import mixc.be.rsms.common.enums.CommonEnum;
import mixc.be.rsms.common.enums.PermissionIsAllValueEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/25
 */
@Component
@Slf4j
public class PermissionUtil implements ApplicationContextAware {
    private static ApplicationContext context;


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        PermissionUtil.context = applicationContext;
    }

    /**
     * 检查是否有对应的权限
     *
     * @param permissionCode  权限码
     * @param belongingUserId 数据所有人
     */
    public static void checkPermission(List<String> permissionCode, Long belongingUserId) {
        if (ObjectUtils.isEmpty(permissionCode)) {
            throw new BusinessException(CommonEnum.NOT_PERMISSION_ERROR);
        }
        Map<String, List<Long>> userIdListByPermissionCode = getUserIdListByPermissionCode(permissionCode);
        boolean allMatch = userIdListByPermissionCode.keySet().stream()
                .allMatch(item -> {
                    return userIdListByPermissionCode.get(item).contains(belongingUserId) || userIdListByPermissionCode.get(item).contains(PermissionIsAllValueEnum.ALL.getValue());
                });
        if (!allMatch) {
            throw new BusinessException(CommonEnum.NOT_PERMISSION_ERROR);
        }
    }

    /**
     * 获取当前用户 拥有 对应数据操作权限 的 数据所属用户Id
     *
     * @param permissionCode
     * @return
     */
    public static Map<String, List<Long>> getUserIdListByPermissionCode(List<String> permissionCode) {
        Map<String, List<Long>> syncGetUserIdByPermissionCode = syncGetUserIdByPermissionCode(permissionCode);
        if (!ObjectUtils.isEmpty(syncGetUserIdByPermissionCode)) {
            return syncGetUserIdByPermissionCode.keySet()
                    .stream()
                    .map(key -> {
                        List<Long> value = syncGetUserIdByPermissionCode.getOrDefault(key, new ArrayList<>());
                        if (value.contains(PermissionIsAllValueEnum.ALL.getValue())) {
                            return Map.of(key, List.of(PermissionIsAllValueEnum.ALL.getValue()));
                        } else {
                            return Map.of(key, value.stream().distinct().toList());
                        }
                    }).flatMap(map -> map.entrySet().stream())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (existing, next) -> existing));
        }
        return Map.of();
    }

    /**
     * 异步返回 获取当前用户 拥有 对应数据操作权限 的 数据所属用户Id
     *
     * @param permissionList
     * @return
     */
    public static Map<String, List<Long>> syncGetUserIdByPermissionCode(List<String> permissionList) {
        AuthorServerClient authorServerClient = context.getBean(AuthorServerClient.class);
        // 权限查询
        return permissionList.stream()
                .map(item -> {
                    List<Long> userPermissionByCode = authorServerClient.getUserPermissionByCode(item);
                    return Map.of(item, userPermissionByCode);
                }).flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (existing, next) -> existing));
    }

    /**
     * 返回用户的所有权限码
     *
     * @param userId
     * @return
     */
    public static CompletableFuture<List<String>> asyncGetUserAllPermissionCode(Long userId) {
        AuthorServerClient authorServerClient = context.getBean(AuthorServerClient.class);
        // 创建一个虚拟线程执行器
        ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();
        return CompletableFuture.supplyAsync(() -> {
            return authorServerClient.getUserAllPermissions(userId);
        }, executor);
    }
    
    /**
     * 根据权限码集合和用户ID获取当前用户所拥有的权限码
     * @param permissionCodeList 权限码集合
     * @param userId 当前用户ID
     * @return
     */
    public static List<String> getUserPermissionList(List<String> permissionCodeList, Long userId){
    	List<String> resultPermissionList = new ArrayList<>();
    	if (CollectionUtils.isEmpty(permissionCodeList) || ObjectUtils.isEmpty(userId)) {
    		return null;
    	}
    	AuthorServerClient authorServerClient = context.getBean(AuthorServerClient.class);
    	permissionCodeList.stream().forEach(item -> {
    		List<Long> permissionUserIdList = authorServerClient.getUserPermissionByCode(item);
    		log.info("权限码：{} 获取到的用户ID为：{}", item, permissionUserIdList);
    		if (!CollectionUtils.isEmpty(permissionUserIdList)) {
    			if (permissionUserIdList.size() == 1 && permissionUserIdList.get(0).longValue() == PermissionIsAllValueEnum.ALL.getValue()) {
    				resultPermissionList.add(item);
    			} else if (permissionUserIdList.contains(userId)) {
    				resultPermissionList.add(item);
    			}
    		}
    	});
    	return resultPermissionList;
    }
    
    /**
     * 判断是否有权限
     * @param permissionUserIdList 通过权限码获取的权限范围内的用户ID集合
     * @param userId 当前记录创建人ID
     * @return
     */
    public static boolean isPermit(List<Long> permissionUserIdList, Long userId){
    	if (!CollectionUtils.isEmpty(permissionUserIdList)) {
			if (permissionUserIdList.size() == 1 && permissionUserIdList.get(0).longValue() == PermissionIsAllValueEnum.ALL.getValue()) {
				return true;
			} else if (permissionUserIdList.contains(userId)) {
				return true;
			}
		}
    	return false;
    }


    public static void main(String[] args) {
        String string = "1\n" +
                "租售-意向金\n" +
                "SF20250109091159501\n" +
                "意向金                     \n" +
                "SF20250109091159501\n" +
                "2\n" +
                "车位销售款(使用权)\n" +
                "A00356\n" +
                "车位销售款(使用权)\n" +
                "A00356\n" +
                "3\n" +
                "车位销售款(所有权)\n" +
                "A00022\n" +
                "车位销售款(所有权)\n" +
                "A00022\n" +
                "4\n" +
                "代收车位销售款\n" +
                "A00215\n" +
                "代收车位销售款\n" +
                "A00215\n" +
                "5\n" +
                "金融机构居间服务费\n" +
                "SF20240821095616348\n" +
                "金融机构居间服务费\n" +
                "SF20240821095616348\n" +
                "6\n" +
                "商铺新盘租赁居间服务费\n" +
                "A00124\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "7\n" +
                "车位二手房买卖居间服务费\n" +
                "A00020\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "8\n" +
                "非机动车库居间服务费\n" +
                "A00251\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "9\n" +
                "车位新盘租赁居间服务费\n" +
                "A00023\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "10\n" +
                "车位新盘分销居间服务费\n" +
                "A00013\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "11\n" +
                "车位二手房租赁居间服务费\n" +
                "A00024\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "12\n" +
                "商铺新盘分销居间服务费\n" +
                "A00122\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "13\n" +
                "商铺二手房租赁居间服务费\n" +
                "A00125\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "14\n" +
                "商铺二手房买卖居间服务费\n" +
                "A00123\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "15\n" +
                "写字楼新盘租赁居间服务费\n" +
                "A00151\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "16\n" +
                "写字楼新盘分销居间服务费\n" +
                "A00149\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "17\n" +
                "写字楼二手房租赁居间服务费\n" +
                "A00154\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "18\n" +
                "写字楼二手房买卖居间服务费\n" +
                "A00150\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "19\n" +
                "住宅新盘租赁居间服务费\n" +
                "A00172\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "20\n" +
                "住宅新盘分销居间服务费\n" +
                "A00174\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "21\n" +
                "住宅二手房租赁居间服务费\n" +
                "A00173\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319\n" +
                "22\n" +
                "住宅二手房买卖居间服务费\n" +
                "A00171\n" +
                "租售居间服务费        \n" +
                "SF20250109091144319";


        String sql = "insert into `tb_data_cost_item_info`(`id`, `cost_item_code`,`cost_item_name`,`sf_cost_item_code`,`sf_cost_item_name`,`achievement_flag`,`status`,`create_user`) values(%s, \"%s\", \"%s\", \"%s\", \"%s\",1,0,8);";
        String[] split = string.split("\n");
        for (int i = 0; i < split.length / 5; i++) {
            String index = split[i * 5 + 0];
            String cost_item_name = split[i * 5 + 1];
            String cost_item_code = split[i * 5 + 2];
            String sf_cost_item_name = split[i * 5 + 3];
            String sf_cost_item_code = split[i * 5 + 4];

            System.out.println(String.format(sql, index, cost_item_code.strip(), cost_item_name.strip(), sf_cost_item_code.strip(), sf_cost_item_name.strip()));

        }

    }
}





