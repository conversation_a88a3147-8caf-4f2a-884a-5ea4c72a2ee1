package mixc.be.rsms.business.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.enums.BusinessEnum;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.pojo.business.ThirdPayResultResp;
import mixc.be.rsms.common.utils.JacksonUtil;
import mixc.be.rsms.pojo.third.SFOrderCreateContentResp;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/03/27
 */
@Slf4j
public class ThirdPayUtil {

    public static ThirdPayResultResp getCmbPayInfo(SFOrderCreateContentResp pay, String myEvn) {
        String paramPath = "pages/newPay/index?cmbOrderId=%s&orderId=%s&encryptedTradeInfo=%s&merId=%s&encryptedCmbOrderId=%s";
        Map<String, String> payInfoMap = JacksonUtil.fromJSON(pay.getPayInfo().toString(), new TypeReference<LinkedHashMap<String, String>>() {
        });
        if (ObjectUtils.isEmpty(payInfoMap)) {
            throw new BusinessException(BusinessEnum.PARAMETER_EXCEPTION);
        }

        String path = String.format(
                paramPath,
                payInfoMap.get("cmbOrderId"),
                pay.getTransactionId(),
                payInfoMap.get("encryptedTradeInfo"),
                pay.getMerchantNumber(),
                payInfoMap.get("encryptedCmbOrderId")
        );
        if(StringUtils.isNotEmpty(myEvn)&& !"prod".equals(myEvn)) {
            myEvn = "uat";
        }
        else{
            myEvn = "";
        }

        path = path + "&myEnv=" + myEvn;
        ThirdPayResultResp ret = new ThirdPayResultResp();
        ret.setCmbMiniAppId(payInfoMap.get("cmbMiniAppId"));
        ret.setCmbMiniPath(path);
//        ret.put("cmbMiniPath",path);
//        ret.put("cmbMiniAppId",apiCmbPayInfoDTO.getString("cmbMiniAppId"));
        log.info("招商小程序调用参数:{}" ,ret);
        return ret;
    }
}
