package mixc.be.rsms.business.utils;

import mixc.be.rsms.business.vo.ResultVo;

/**
 * @Classname ResultVoUtil
 * @Date 2021/3/17 4:34 下午
 * @Created by wangxy
 */
public class ResultVoUtil {

    public static <T> ResultVo<T> success(T data){
        return ResultVo.<T>builder()
                .code("0")
                .msg("success")
                .data(data)
                .build();
    }

    public static <T> ResultVo<T> success(){
        return success(null);
    }

    public static <T> ResultVo<T> error(String code,String msg){
        return ResultVo.<T>builder()
                .code(code)
                .msg(msg)
                .build();
    }
}
