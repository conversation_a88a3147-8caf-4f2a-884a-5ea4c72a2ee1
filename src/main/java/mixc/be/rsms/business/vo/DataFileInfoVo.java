package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.net.URL;
import java.time.LocalDate;

@Data
public class DataFileInfoVo implements Serializable {

	private static final long serialVersionUID = -6539498252309975538L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "主键", example = "11222")
	private Long id;
	
	/**
	 * 文件所属业务ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "业务ID", example = "11222")
	private Long businessId;
	
	/**
	 * 业务类型
	 */
	@Schema(description = "业务类型", example = "rent")
	private String businessType;
	
	/**
	 * 文件ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "文件ID", example = "12312312")
	private Long fileId;
	
	/**
	 * 文件名称
	 */
	@Schema(description = "文件名称", example = "1122.jpg")
	private String fileName;
	
	/**
	 * 文件在S3的KEY
	 */
	@Schema(description = "文件在S3的KEY", example = "1122.jpg")
	private String fileKey;
	
	/**
	 * VR地址
	 */
	@Schema(description = "VR地址", example = "http://www.123,cn")
	private String fileUrl;
	
	/**
	 * 文件类型
	 */
	@Schema(description = "文件类型(字典：indoor-室内图、outdoor-室外图)", example = "indoor")
	private String fileType;
	
	/**
	 * 文件标签
	 */
	@Schema(description = "文件标签(字典：kicten-厨房)", example = "kicten")
	private String fileTag;
	
	/**
	 * 文件大小
	 */
	@Schema(description = "文件大小", example = "11222")
	private BigDecimal fileSize;
	
	/**
	 * 文件排序
	 */
	@Schema(description = "文件排序", example = "1")
	private Integer fileSort;
	
	/**
	 * 是否为封面
	 */
	@Schema(description = "是否为封面:0-否、1-是", example = "0")
	private Integer covered;
	
	/**
	 * 预览URL
	 */
	@Schema(description = "文件预览URL", example = "http://www.file20240113233.png?xxxxxxxx")
	private URL presignedUrl;
	
	/**
	 * 上传时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Schema(description = "上传时间", example = "2024-10-12")
	private LocalDate createTime;
	
	@Schema(description = "版本号", example = "1")
	private Integer revision;
}
