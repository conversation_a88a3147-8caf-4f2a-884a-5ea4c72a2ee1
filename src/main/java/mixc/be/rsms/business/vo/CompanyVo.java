package mixc.be.rsms.business.vo;

import java.io.Serial;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompanyVo implements Serializable {

	@Serial
	private static final long serialVersionUID = -7212179039414239679L;

	/**
	 * 公司ID
	 */
	@Schema(description = "公司ID", example = "123")
	private Long companyId;
	
	/**
	 * 公司名称
	 */
	@Schema(description = "公司名称", example = "万象天地")
	private String companyName;
}
