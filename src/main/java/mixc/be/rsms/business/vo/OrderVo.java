package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class OrderVo implements Serializable {


    @Serial
    private static final long serialVersionUID = 7391684617431876337L;
    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主键ID", example = "1929488229")
    private Long id;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号", example = "DD2024010100001")
    private String orderCode;

    /**
     * 订单状态：待支付-unpay、部分付款-partpay、已付款-payed、全额退款-refundall、已转佣-conversioned_commission
     */
    @Schema(description = "订单状态(字典值)", example = "待支付-unpay、部分付款-partpay、已付款-payed、全额退款-refundall、已转佣-conversioned_commission")
    private String status;

    @Schema(description = "成交合同编号")
    private String contractNum;


    @Schema(description = "单据号", example = "DEP20241125000013")
    private String documentNum;

    @Schema(description = "资产类型：1-房源、2-车位、3-新房项目")
    private String businessType;

    /**
     * 房源信息
     */
    @Schema(description = "房源信息(含项目信息)")
    private List<HouseInfoVo> houseInfo;

    /**
     * 车位信息
     */
    @Schema(description = "车位信息(预留字段，后期车位功能上线后，为车位信息对象)(含项目信息)")
    private List<ParkingSpaceVo> parking;

    /**
     * 新房项目对象
     */
    @Schema(description = "新房项目对象(含项目信息)")
    private NewHouseCommunityVo newHouseCommunity;

    /**
     * 客户姓名
     */
    @Schema(description = "客户姓名")
    private String customerName;

    /**
     * 客户电话
     */
    @Schema(description = "客户电话")
    private String customerMobile;

    /**
     * 订单日期
     */
    @Schema(description = "订单日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime orderDate;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * 交易类型:yxj-意向金、dj-定金、yj-佣金
     */
    @Schema(description = "交易类型(字典值)", example = "yxj-意向金、dj-定金、yj-佣金")
    private String transactionType;
    /**
     * 创建人
     */
    @Schema(description = "创建人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createUser;

    @Schema(description = "创建人姓名")
    private String createName;

    @Schema(description = "创建人手机号")
    private String createMobile;


    @Schema(description = "收款子订单信息")
    private List<OrderReceiveVo> receiveOrder;

    @Schema(description = "退款子订单信息")
    private List<OrderRefundVo> refundOrder;

    @Schema(description = "转佣子订单信息")
    private List<OrderCommissionVo> commissionOrder;

    @Schema(description = "权限码")
    private List<String> permissions;
}
