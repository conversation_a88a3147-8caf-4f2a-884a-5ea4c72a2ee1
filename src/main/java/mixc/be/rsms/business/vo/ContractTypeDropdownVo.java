package mixc.be.rsms.business.vo;

import java.io.Serial;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 合同类型下拉框实体类
 * <AUTHOR>
 * @date 2025-02-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractTypeDropdownVo implements Serializable {

	@Serial
	private static final long serialVersionUID = -7212179039414239679L;

	/**
	 * 合同分类
	 */
	@Schema(description = "合同分类", example = "proxy_sell")
	private String contractCategory;
	
	/**
	 * 合同类型
	 */
	@Schema(description = "合同类型", example = "resident_rential_service")
	private String contractType ;
	
	/**
	 * 合同类型名称
	 */
	@Schema(description = "合同类型名称", example = "房屋租赁居间服务合同（住宅）")
	private String contractTypeName;
}
