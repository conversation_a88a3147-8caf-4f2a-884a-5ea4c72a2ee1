package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/21
 */
@Data
public class CommissionSubOrderVo implements Serializable {
    @Serial
    private static final long serialVersionUID = -6102845509369469030L;

    @Schema(description = "Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "子订单编号")
    private String subOrderCode;

    @Schema(description = "金额")
    private BigDecimal amount;
}
