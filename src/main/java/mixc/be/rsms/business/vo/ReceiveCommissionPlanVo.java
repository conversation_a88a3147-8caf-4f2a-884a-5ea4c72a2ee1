package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/21
 */
@Data
public class ReceiveCommissionPlanVo implements Serializable {


    @Serial
    private static final long serialVersionUID = -3339194849316003478L;
    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 成交报告表ID
     */
    @Schema(description = "成交报告表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long reportId;

    /**
     * 费用项目表Id
     */
    @Schema(description = "费用项目表Id")
    private String costItemId;

    /**
     * 费用项目名称
     */
    @Schema(description = "费用项目名称")
    private String costItemDesc;

    /**
     * 租售系统费用项目
     */
    @Schema(description = "租售系统费用项目")
    private String costItemCode;

    /**
     * 交费人：（owner:业主/customer:客户/other:第三方）
     */
    @Schema(description = "交费人", example = "owner:业主/customer:客户/other:第三方")
    private String payer;

    /**
     * 应收佣金
     */
    @Schema(description = "应收佣金")
    private BigDecimal receivableCommission;

    /**
     * 预期交费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "预期交费时间")
    private LocalDate expectPayment;

    /**
     * 实际交费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "实际交费时间")
    private LocalDateTime actualPayment;



//    /**
//     * 本次收佣金额
//     */
//    @Schema(description = "本次收佣金额")
//    private BigDecimal actualCommission;

    /**
     * 已收佣金
     */
    @Schema(description = "已收佣金")
    private BigDecimal receivedCommission;

//    /**
//     * 实际交费时间
//     */
//    @Schema(description = "实际交费时间")
//    private LocalDate actualPayment;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;


    /**
     * 意向金转佣 对应的意向金表ID
     */
    @Schema(description = "意向金转佣 对应的意向金表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long depositId;

    /**
     * 主订单Id
     */
    @Schema(description = "主订单Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 主订单编号
     */
    @Schema(description = "主订单编号")
    private String orderCode;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态")
    private String orderStatus;

    /**
     * 子订单ID
     */
    @Schema(description = "子订单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long subOrderId;

    /**
     * 子订单编号
     */
    @Schema(description = "子订单编号")
    private String subOrderCode;

    /**
     * 附件fileId
     */
    @Schema(description = "附件fileId(通用文件上传接口返回的 fileId)")
    private DataFileInfoVo enclosureFile;

    @Schema(description = "是否允许修改")
    private Boolean edit;
}
