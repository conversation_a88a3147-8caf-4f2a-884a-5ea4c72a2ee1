package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 通用分成父类
 *
 * <AUTHOR>
 * @description
 * @date 2025/05/29
 */
@Data
public class AllocationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -2023874992047946757L;


    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主键")
    private Long id;


    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "角色人id")
    private Long userId;


    @Schema(description = "角色姓名")
    private String userName;

    @Schema(description = "角色手机号")
    private String userMobile;


    @Schema(description = " 分成方式编码【字典值：percentage_type】(percentage:比例分成,fixed_amount:固定金额分成)")
    private String percentageCode;

    @Schema(description = "固定金额分成")
    private String percentageValue;

    @Schema(description = "分成比例")
    private BigDecimal tenthsRatio;

    @Schema(description = "分成金额")
    private BigDecimal tenthsMoney;
}
