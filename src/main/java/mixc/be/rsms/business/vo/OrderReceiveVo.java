package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/09
 */
@Data
public class OrderReceiveVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 2598657080907616735L;

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "收款子订单ID")
    private Long id;

    /**
     * 主订单Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主订单Id")
    private Long parentOrderId;

    /**
     * 子订单编号
     */
    @Schema(description = "子订单编号")
    private String subOrderCode;

    /**
     * 支付模式:payall-全额支付、installment-分期
     */
    @Schema(description = "支付模式(字典值)", example = "payall-全额支付、installment-分期")
    private String payMode;

    /**
     * 费用项目id(来源：租售项目接口)
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "费用项目id")
    private String receiveCommunityId;

    /**
     * 费用项目名(来源：租售项目接口)
     */
    @Schema(description = "费用项目名")
    private String receiveCommunityName;


    /**
     * 租售系统收费项编码
     */
    @Schema(description = "租售系统收费项编码")
    private String zsReceiveCommunityCode;

    /**
     * 租售系统收费项 名称
     */
    @Schema(description = "租售系统收费项 名称")
    private String zsReceiveCommunityName;


    /**
     * 订单状态:0-待支付，1-已支付
     */
    @Schema(description = "订单状态(字典值)", example = "0-待支付，1-已支付")
    private String orderStatus;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String goodName;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额")
    private BigDecimal discountAmount;

    /**
     * 支付渠道
     */
    @Schema(description = "支付渠道")
    private String payChannel;

    /**
     * 业务记录Id
     */
    @Schema(description = "业务记录Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long businessId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createUser;

    @Schema(description = "创建人姓名")
    private String createName;

    @Schema(description = "创建人手机号")
    private String createMobile;
}
