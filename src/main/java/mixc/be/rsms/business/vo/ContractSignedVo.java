package mixc.be.rsms.business.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractSignedVo implements Serializable  {

	private static final long serialVersionUID = -521203628664540868L;

	/**
	 * 合同签署日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Schema(description = "合同签署日期", example = "2024-12-17")
	private LocalDateTime contractSignDate;
	
	/**
	 * 成交价格
	 */
	@Schema(description = "成交价格", example = "120.00")
	private BigDecimal transactionPrice;
	
	/**
	 * 业主佣金总额
	 */
	@Schema(description = "业主佣金总额", example = "120.00")
	private BigDecimal leaserCommission;
	
	/**
	 * 客户佣金总额
	 */
	@Schema(description = "客户佣金总额", example = "120.00")
	private BigDecimal customerCommission;
	
	/**
	 * 佣金总额
	 */
	@Schema(description = "佣金总额", example = "120.00")
	private BigDecimal commission;
	
	/**
	 * 房源租售状态
	 */
	@Schema(description = "房源租售状态", example = "rent")
	private String rentStatus;
	
	/**
	 * 车位租售状态
	 */
	@Schema(description = "车位租售状态", example = "sale_or_rent")
	private String parkRentStatus;
}
