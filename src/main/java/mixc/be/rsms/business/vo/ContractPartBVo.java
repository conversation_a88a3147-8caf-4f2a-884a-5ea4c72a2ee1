package mixc.be.rsms.business.vo;

import java.io.Serializable;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 甲方信息参数实体类
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractPartBVo implements Serializable  {

	private static final long serialVersionUID = -5625739021373319682L;

	/**
     * 乙方类型
     */
	@Schema(description = "乙方类型(字典：personal-个人、company-公司)", example = "personal")
    private String customerType;
	
	/**
	 * 乙方（客户）ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@NotNull(message = "乙方（客户）ID不能为空")
	@Schema(description = "乙方（客户）", example = "12")
	private Long customerId;
	
	/**
	 * 乙方（客户）姓名
	 */
	@NotBlank(message = "乙方（客户）姓名不能为空")
	@Schema(description = "乙方（客户）姓名", example = "李四")
	private String customerName;
	
	/**
	 * 乙方（客户）手机号码
	 */
	@NotBlank(message = "乙方（客户）手机号码不能为空")
	@Schema(description = "乙方（客户）手机号码", example = "13245678123")
	private String customerMobile;
	
	/**
	 * 乙方（客户）手机号码-BASE64
	 */
	@Schema(description = "乙方（客户）手机号码-BASE64", example = "13245678123")
	private String customerMobileEncode;
	
	/**
     * 乙方证件类型
     */
	@Schema(description = "证件类型：字典", example = "idCard")
    private String identityType;
	
	/**
     * 乙方证件号码
     */
	@Schema(description = "证件号码（当甲方类型选择企业时此字段为：乙方营业执照编号）", example = "321909213456713456")
    private String identityNumber;
	
	/**
	 * 乙方（客户）联系地址
	 */
	@NotBlank(message = "乙方（客户）联系地址不能为空")
	@Schema(description = "乙方（客户）联系地址", example = "西安市雁塔区")
	private String customerAddress;
	
	/**
	 * 乙方（客户）代理人姓名
	 */
	@Schema(description = "乙方（客户）代理人姓名", example = "王五")
	private String customerProxyUserName;
	
	/**
	 * 乙方（客户）代理人手机号
	 */
	@Schema(description = "乙方（客户）代理人手机号", example = "13246578901")
	private String customerProxyUserMobile;
	
	/**
	 * 乙方（客户）代理人手机号
	 */
	@Schema(description = "乙方（客户）代理人手机号", example = "13246578901")
	private String customerProxyUserMobileEncode;
	
	/**
	 * 乙方（客户）代理人身份证编号
	 */
	@Schema(description = "乙方（客户）代理人身份证编号", example = "321909213456713456")
	private String customerProxyUserIdNumber;
	
	/**
     * 共同购买人
     */
	@Schema(description = "共同购买人", example = "张三")
    private String coPurchaser;
}
