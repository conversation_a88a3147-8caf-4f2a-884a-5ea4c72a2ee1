package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
/**
 * 权证过户VO
 * <AUTHOR>
 * @date 2024-10-20
 */
@Data
public class OwnershipTransferVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -365053510348592716L;
    /**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "主键", example = "1043257740660826112")
	private Long id;
	
	/**
	 * 成交合同编号
	 */
	@Schema(description = "成交合同编号", example = "HT001")
	private String transferContractNumber;
	
	/**
     * 成交状态
     */
    @Schema(description = "成交状态(字典：unaudit-未审核、auditing-审核中、reviewing-复核中、reviewed-已复核、closed-已结案)", example = "closed")
    private String transferStatus;
    
    /**
     * 房源
     */
    @Schema(description = "房源", example = "1栋1单元22层|2204|东里小区")
    private String houseName;

    /**
     * 车位
     */
    @Schema(description = "车位", example = "车位名称|车位号|车位编码")
    private String parkingSpaceName;
    
    /**
     * 客户
     */
    @Schema(description = "客户", example = "赵六|KH2024010112345678")
    private String customerName;


    /**
     * 楼栋
     */
    @Schema(description = "楼栋", example = "23")
    private String building;

    /**
     * 单元
     */
    @Schema(description = "单元", example = "3")
    private String unit;

    /**
     * 房号
     */
    @Schema(description = "房号", example = "601")
    private String houseNumber;

    /**
     * 新房项目ID
     */
    @Schema(description = "新房项目ID", example = "2024010112345678")
    private Long newHouseCommunityId;


    /**
     * 预计过户完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "预计过户完成时间", example = "2024-10-08")
    private LocalDate expectTransferDate;
    
    /**
     * 当前过户阶段
     */
    @Schema(description = "当前过户阶段(字典：ts0-房屋挂牌、ts1-签订合同、ts2-抵押中过户、ts3-业主解抵押、ts4-签订网签合同、ts5-客户贷款面签、ts6-贷款审批通过、ts7-网上预审、ts8-网审通过预约、ts9-资金监管、ts10-过户递件、ts11-核税、ts12-领证、ts13-办理抵押、ts14-银行放款给卖方、ts15-物业交割、ts16-完成)", example = "ts16")
    private String currentTransferStage;
    
    /**
     * 当前阶段完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "当前阶段完成时间", example = "2024-10-08")
    private LocalDate currentStageFinishDate;
    
    /**
     * 下个过户阶段
     */
    @Schema(description = "下个过户阶段(字典：ts0-房屋挂牌、ts1-签订合同、ts2-抵押中过户、ts3-业主解抵押、ts4-签订网签合同、ts5-客户贷款面签、ts6-贷款审批通过、ts7-网上预审、ts8-网审通过预约、ts9-资金监管、ts10-过户递件、ts11-核税、ts12-领证、ts13-办理抵押、ts14-银行放款给卖方、ts15-物业交割、ts16-完成)", example = "ts1")
    private String nextTransferStage;
    
    /**
     * 备注
     */
    @Schema(description = "备注", example = "备注内容；111")
    private String remarks;
    
    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1111")
    private Long createUser;
    
    /**
     * 权限列表
     */
    @Schema(description = "权限列表", example = "[\"transaction_manage.ownership_transfer_manager_view.edit\"]")
    private List<String> permissions;
}
