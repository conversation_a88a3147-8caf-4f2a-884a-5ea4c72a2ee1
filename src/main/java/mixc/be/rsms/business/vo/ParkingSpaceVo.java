package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/01/08
 */
@Data
public class ParkingSpaceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -6089533115949672969L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 项目id
     */
    @Schema(description = "项目ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long communityId;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String communityName;

    /**
     * 编码
     */
    @Schema(description = "编码，车位ID")
    private String code;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 车位号
     */
    @Schema(description = "车位号")
    private String number;

    /**
     * 车位类型: 地面车位、地下车位、其他
     */
    @Schema(description = "车位类型: 地面车位、地下车位、其他")
    private String type;

    /**
     * 车位子类: 标准车位、微型车位、子母车位、机械车位、无障碍车位
     */
    @Schema(description = "车位子类: 标准车位、微型车位、子母车位、机械车位、无障碍车位")
    private String subType;

    /**
     * 朝昔用途: 租赁、销售、临停
     */
    @Schema(description = "朝昔用途: 租赁、销售、临停")
    private String purpose;

    /**
     * 车位产权: 产权车位、产权人防车位、人防车位、非产权非人防车位
     */
    @Schema(description = "车位产权: 产权车位、产权人防车位、人防车位、非产权非人防车位")
    private String propertyRight;

    /**
     * 产权归属: 政府、地产、物业、业主共有、业主私有
     */
    @Schema(description = "产权归属: 政府、地产、物业、业主共有、业主私有")
    private String ownership;

    /**
     * 车位面积
     */
    @Schema(description = "车位面积")
    private BigDecimal area;

    /**
     * 朝昔使用情况: 未知、已租、已售、未租、未售
     */
    @Schema(description = "朝昔使用情况: 未知、已租、已售、未租、未售")
    private String usageStatus;

    /**
     * 置地车位编码
     */
    @Schema(description = "置地车位编码")
    private String landCode;

    /**
     * 开盘人
     */
    @Schema(description = "开盘人")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long opener;

    /**
     * 开盘人名称
     */
    @Schema(description = "开盘人名称")
    private String openerName;

    /**
     * 维护人
     */
    @Schema(description = "维护人")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long maintainer;

    /**
     * 维护人名称
     */
    @Schema(description = "维护人名称")
    private String maintainerName;

    /**
     * 所属楼栋
     */
    @Schema(description = "所属楼栋")
    private String building;

    /**
     * 租售状态: 新车位出售/二手车位出售/二手车位出租/租售/我售/他售/暂缓（出售）/暂缓（租售）/无效/未开盘
     */
    @Schema(description = "租售状态: 新车位出售/二手车位出售/二手车位出租/租售/我售/他售/暂缓（出售）/暂缓（租售）/无效/未开盘")
    private String saleStatus;

    /**
     * 属性: 公盘/私盘/淘宝池/封盘
     */
    @Schema(description = "属性: 公盘/私盘/淘宝池/封盘")
    private String attribute;

    /**
     * 售价
     */
    @Schema(description = "售价")
    private BigDecimal price;

    /**
     * 租价
     */
    @Schema(description = "租价")
    private BigDecimal rent;

    /**
     * 车位标题
     */
    @Schema(description = "车位标题")
    private String title;

    /**
     * 是否同步客户端:0-否、1-是
     */
    @Schema(description = "是否同步客户端:0-否、1-是")
    private Integer syncClient;

    /**
     * 车位位置: 左侧、右侧、中间
     */
    @Schema(description = "车位位置: 左侧、右侧、中间")
    private String location;

    /**
     * 来源
     */
    @Schema(description = "来源")
    private String source;

    /**
     * 最后跟进日期
     */
    @Schema(description = "最后跟进日期")
    private Date lastFollowUpDate;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 是否朝昔同步:0-否、1-是
     */
    @Schema(description = "是否朝昔同步:0-否、1-是")
    private Integer outSync;

    /**
     * 乐观锁：版本号
     */
    @Schema(description = "版本号")
    private Integer revision;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "图片列表")
    private List<DataFileInfoVo> pictureList;
}
