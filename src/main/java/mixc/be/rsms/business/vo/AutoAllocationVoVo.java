package mixc.be.rsms.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 自动分配业绩对象
 * <AUTHOR>
 * @description
 * @date 2025/05/26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class AutoAllocationVoVo extends AllocationVo {
    @Serial
    private static final long serialVersionUID = 1964471400593882432L;


    @Schema(description = "分成角色编码(house_input:房源录入人,house_maintainer:房源维护人,house_opener:房源开盘人,house_pic:房源图片人,house_video:房源视频人,house_key:房源钥匙人,house_delegate:房源委托人,house_bargain:房源议价人,client_first_input:客源首录人,client_maintainer:客源维护人,deal_maker:成交人,reserve:预留)")
    private String tenthsRoleCode = "100";

    @Schema(description = "分成角色编码 描述值")
    private String tenthsRoleValue;

    @Schema(description = " 分成转移编码【字典值：tenths_role】(house_input:房源录入人,house_maintainer:房源维护人,house_opener:房源开盘人,client_first_input:客源首录人,client_maintainer:客源维护人,deal_maker:成交人)'")
    private String tenthsTransferCode;

    @Schema(description = "房源录入人")
    private String tenthsTransferValue;

    @Schema(description = "实得 金额")
    private BigDecimal amount;



}
