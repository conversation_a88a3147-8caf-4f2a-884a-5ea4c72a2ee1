package mixc.be.rsms.business.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractDetailVo implements Serializable {

	@Serial
	private static final long serialVersionUID = 361869937887126581L;

	/**
	 * 合同基础信息
	 */
	@Schema(description = "合同基础信息", example = "12345667")
	private ContractBaseInfoVo contractBaseInfoVo;
	
	/**
	 * 甲方信息
	 */
	@Schema(description = "甲方信息", example = "")
	private ContractPartAVo contractPartAVo;
	
	/**
	 * 乙方信息
	 */
	@Schema(description = "乙方信息", example = "")
	private ContractPartBVo contractPartBVo;
    
	/**
	 * 成交信息
	 */
	@Schema(description = "成交信息", example = "")
	private ContractSignedVo contractSignedVo;
	
	/**
	 * 合同附件VO
	 */
	private ContractAttachmentVo contractAttachementVo;
	
	/**
	 * 按钮权限
	 */
	@Schema(description = "权限列表", example = "[esign.contract_manage.view]")
	private List<String> permissions;
	
}
