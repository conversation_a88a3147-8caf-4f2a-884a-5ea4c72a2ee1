package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/09
 */
@Data
public class OrderCommissionVo implements Serializable {
    @Serial
    private static final long serialVersionUID = -2284536182143152320L;
    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "转佣子订单ID")
    private Long id;

    /**
     * 主订单Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主订单Id")
    private Long parentOrderId;

    /**
     * 子订单编号
     */
    @Schema(description = "子订单编号")
    private String subOrderCode;

    @Schema(description = "转出主订单编号")
    private String exportOrderCode;

    /**
     * 转出金额总计
     */
    @Schema(description = "转出金额总计")
    private BigDecimal exportAmount;

    @Schema(description = "转出子订单列表")
    private List<CommissionSubOrderVo> exportCommissionSubOrderVos;

    /**
     * 转入金额
     */
    @Schema(description = "转入金额总计")
    private BigDecimal importAmount;

    @Schema(description = "转入主订单号")
    private String importOrderCode;


    @Schema(description = "转入子订单列表")
    private List<CommissionSubOrderVo> importCommissionSubOrderVos;

    /**
     * 订单状态:新建-create、进行中-running、已完成-ok、失败-error
     */
    @Schema(description = "订单状态(字典值)", example = "新建-create、进行中-running、已完成-ok、失败-error")
    private String orderStatus;

    /**
     * 业务记录Id
     */
    @Schema(description = "业务记录Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long businessId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createUser;

    @Schema(description = "创建人姓名")
    private String createName;

    @Schema(description = "创建人手机号")
    private String createMobile;
}
