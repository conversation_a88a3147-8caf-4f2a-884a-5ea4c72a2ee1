package mixc.be.rsms.business.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractBaseInfoVo implements Serializable  {

	private static final long serialVersionUID = 8312492246636866116L;

	/**
	 * 合同主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "合同主键", example = "12345678")
	private Long contractId;
	
	/**
	 * 朝昔合同编号
	 */
	@Schema(description = "朝昔合同ID", example = "234234")
	private String joyContractId;
	
	/**
	 * 合同状态
	 */
	@Schema(description = "合同状态：字典", example = "draft")
	private String contractStatus;
	
	/**
	 * 合同类型
	 */
	@Schema(description = "合同类型:字典", example = "resident_rential_service")
	private String contractType;
	
	/**
	 * 合同编号
	 */
	@Schema(description = "合同编号", example = "HT20241201001")
	private String contractNumber;
	
	/**
	 * 居间方公司ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "居间方公司ID", example = "100")
	private Long intermediaryCompanyId;
	
	/**
	 * 居间方公司名称
	 */
	@Schema(description = "居间方公司名称", example = "万象置地")
	private String intermediaryCompanyName;
	
	/**
	 * 签约房源ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "房源ID", example = "12345667")
	private Long houseId;
	
	/**
	 * 签约车位ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = " 签约车位ID", example = "12345667")
	private Long spaceParkId;
	/**
	 * 签约房源信息
	 */
	@Schema(description = "签约房源信息", example = "3栋2单元|10234|友林小区")
	private String contractHouseInfo;
	
	/**
	 * 签约车位信息
	 */
	@Schema(description = "签约车位信息", example = "3栋|10234|友林小区")
	private String contractParkSpaceInfo;
	
	/**
	 * 面积
	 */
	@Schema(description = "面积", example = "120.00")
	private BigDecimal area;
	
	/**
	 * 创建人ID
	 */
	@Schema(description = "创建人ID", example = "12344")
	private Long createUser;
	
	/**
	 * 创建人
	 */
	@Schema(description = "创建人", example = "张三")
	private String creator;
	
	/**
	 * 创建人所属门店
	 */
	@Schema(description = "创建人所属门店", example = "万象店")
	private String storeName;
	
	/**
	 * 合同签署日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Schema(description = "合同签署日期", example = "2024-12-17")
	private LocalDateTime createTime; 
}
