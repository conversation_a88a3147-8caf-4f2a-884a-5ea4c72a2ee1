package mixc.be.rsms.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2025/03/25
 */
@Data
public class NewHouseCommunityVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -8073086164421987069L;

    /**
     * 楼栋
     */
    @Schema(description = "新房项目-楼栋", example = "24", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String building;

    /**
     * 单元
     */
    @Schema(description = "新房项目-单元", example = "3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String unit;

    /**
     * 房号
     */
    @Schema(description = "新房项目-房号", example = "601", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String houseNumber;

    @Schema(description = "新房项目-项目Id", example = "1244222499288", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long newHouseCommunityId;

    @Schema(description = "新房项目名", example = "花园小区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String newHouseCommunityName;
}
