package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HouseInfoVo implements Serializable {


    @Serial
    private static final long serialVersionUID = 4087626875141827326L;
    /**
     * 房源主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "房源ID")
    private Long id;

    /**
     * 房源位置
     */
    @Schema(description = "房源位置")
    private String location;

    /**
     * 房源编号
     */
    @Schema(description = "房屋编号", example = "2301")
    private String houseNumber;

    /**
     * 楼栋
     */
    @Schema(description = "楼栋", example = "1")
    private String building;

    /**
     * 单元
     */
    @Schema(description = "单元", example = "1")
    private String unit;

    /**
     * 房屋楼层
     */
    @Schema(description = "房屋楼层", example = "8")
    private String floor;

    /**
     * 项目Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "项目ID")
    private Long communityId;

    /**
     * 朝昔的项目id或者系统自建的项目ID编码RES+8位数字
     */
    @Schema(description = "朝昔项目编码")
    private String zxCommunityCode;

    /**
     * 房源所属项目名称
     */
    @Schema(description = "项目名称")
    private String communityName;
    
    /**
     * 房源状态
     */
    @Schema(description = "房源状态")
    private String rentStatus;

    /**
     * 房屋类型：配套、商铺、住宅、其他、未知、公寓、办公室、教室、宿舍
     */
    @Schema(description = "房屋类型：配套、商铺、住宅、其他、未知、公寓、办公室、教室、宿舍")
    private String houseType;

    /**
     * 面积
     */
    @Schema(description = "面积")
    private BigDecimal area;
    
    /**
     * 售价
     */
    @Schema(description = "售价")
    private BigDecimal sellPrice;


    /**
     * 租赁价格
     */
    @Schema(description = "租赁价格")
    private BigDecimal rentPrice;

    /**
     * 户型：3室2厅1卫
     */
    @Schema(description = "户型")
    private String layoutCode;
    
    /**
     * 朝向
     */
    @Schema(description = "朝向")
    private String orient;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "123")
    private Long createUser;

    /**
     * 维护人ID
     */
    @Schema(description = "维护人ID", example = "123")
    private Long maintenanceBy;

    /**
     * 开盘人
     */
    @Schema(description = "开盘人", example = "123")
    private Long openedBy;

    /**
     * 图片人
     */
    @Schema(description = "图片人", example = "123")
    private Long picBy;

    /**
     * 视频人
     */
    @Schema(description = "视频人", example = "123")
    private Long videoBy;

    /**
     * 钥匙人
     */
    @Schema(description = "钥匙人", example = "123")
    private Long keyBy;

    /**
     * 委托人
     */
    @Schema(description = "委托人", example = "123")
    private Long delegateBy;

    /**
     * "议价人
     */
    @Schema(description = "议价人", example = "123")
    private Long dickBy;
    
    /**
     * 业主/联系人
     */
    @Schema(description = "业主/联系人", example = "联系人")
    private String concatUser;
    
    /**
     * 房源封面图片
     */
    @Schema(description = "房源封面图片", example = "http://123.png?file_202411291614")
    private String coverUrl;
}
