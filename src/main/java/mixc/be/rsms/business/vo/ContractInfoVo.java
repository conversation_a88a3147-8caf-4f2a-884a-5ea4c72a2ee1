package mixc.be.rsms.business.vo;

import java.io.Serial;
import java.io.Serializable;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -7971451082111032331L;
    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 成交报告表ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "成交报告表ID")
    private Long reportId;

    /**
     * 业主电话
     */
    @Schema(description = "业主电话(脱敏)")
    private String ownerMobile;

    @Schema(description = "业主电话(密文)")
    private String ownerMobileEncode;

    /**
     * 甲方姓名
     */
    @Schema(description = "甲方姓名")
    private String ownerName;

    /**
     * 甲方证件号
     */
    @Schema(description = "甲方证件号")
    private String ownerIcardNum;

    /**
     * 甲方联系地址
     */
    @Schema(description = "甲方联系地址")
    private String ownerAddress;

    /**
     * 客户电话
     */
    @Schema(description = "客户电话(脱敏)")
    private String customerMobile;


    @Schema(description = "客户电话(密文)")
    private String customerMobileEncode;

    /**
     * 乙方姓名
     */
    @Schema(description = "乙方姓名")
    private String customerName;

    /**
     * 乙方证件号
     */
    @Schema(description = "乙方证件号")
    private String customerIcardNum;

    /**
     * 乙方联系地址
     */
    @Schema(description = "乙方联系地址")
    private String customerAddress;
}
