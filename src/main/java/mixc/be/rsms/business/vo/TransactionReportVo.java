package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/21
 */
@Data
public class TransactionReportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 3495699752610955853L;
    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 成交报告编号
     */
    @Schema(description = "成交报告编号")
    private String reportCode;

    /**
     * 成交合同Id
     */
    @Schema(description = "成交合同Id")
    private Long transactionContractId;
    /**
     * 成交合同编号
     */
    @Schema(description = "成交合同编号")
    private String transactionContractNum;

    /**
     * 成交报告状态：unaudit-未审核，auditing-审核中，reviewing-复核中，reviewed-已复核，closed-已结案
     */
    @Schema(description = "成交报告状态(字典值)", example = "unaudit-未审核，auditing-审核中，reviewing-复核中，reviewed-已复核，closed-已结案")
    private String status;

    /**
     * 类目:(dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖)
     */
    @Schema(description = "类目(字典值)", example = "dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖")
    private String category;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long customerId;


    /**
     * 客户姓名
     */
    @Schema(description = "客户姓名")
    private String customerName;
    /**
     * 客户电话
     */
    @Schema(description = "客户电话(脱敏)")
    private String customerMobile;

    @Schema(description = "客户电话(密文)")
    private String customerMobileEncode;

    /**
     * 房源信息
     */
    @Schema(description = "房源信息")
    private HouseInfoVo houseInfo;

    /**
     * 车位信息
     */
    @Schema(description = "车位信息")
    private ParkingSpaceVo parkingSpaceInfo;

    /**
     * 业主信息
     */
    @Schema(description = "业主信息")
    private String ownerInfo;

    /**
     * 成交日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "成交日期", example = "2024-09-08")
    private LocalDate dealDate;

    /**
     * 成交金额
     */
    @Schema(description = "成交金额")
    private BigDecimal dealAmount;

    /**
     * 经纪人ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "经纪人ID")
    private Long brokerId;
    /**
     * 经纪人姓名
     */
    @Schema(description = "经纪人姓名")
    private String brokerName;
    /**
     * 经纪人手机号
     */
    @Schema(description = "经纪人手机号(脱敏)")
    private String brokerMobile;

    @Schema(description = "经纪人手机号(密文)")
    private String brokerMobileEncode;

    /**
     * 经纪人所在门店
     */
    @Schema(description = "经纪人所在门店")
    private String brokerStore;



    /**
     * 业主佣金
     */
    @Schema(description = "业主佣金")
    private BigDecimal ownerCommission;

    /**
     * 客户佣金
     */
    @Schema(description = "客户佣金")
    private BigDecimal customerCommission;

    /**
     * 营销费用
     */
    @Schema(description = "营销费用")
    private BigDecimal marketingFee;

    /**
     * 土地证号
     */
    @Schema(description = "土地证号")
    private String landCertificateNum;

    /**
     * 产权证号
     */
    @Schema(description = "产权证号")
    private String propertyRightNum;

    /**
     * 权证人
     */
    @Schema(description = "权证人")
    private String warrantHolder;

    /**
     * 意向金/订金ID
     */
    @Schema(description = "意向金/订金ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long depositId;

    /**
     * 其他支出
     */
    @Schema(description = "其他支出")
    private BigDecimal otherExpenses;

    /**
     * 补充条款
     */
    @Schema(description = "补充条款")
    private String supplementaryTerms;

    /**
     * 合同附件
     */
    @Schema(description = "合同附件数组(通用文件接口返回的 fileId 字段)")
    private List<DataFileInfoVo> reportFiles;

    /**
     * 签约日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "签约日期", example = "2024-09-08")
    private LocalDate signDate;

    /**
     * 楼栋
     */
    @Schema(description = "新房项目-楼栋", example = "24", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String building;

    /**
     * 单元
     */
    @Schema(description = "新房项目-单元", example = "3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String unit;

    /**
     * 房号
     */
    @Schema(description = "新房项目-房号", example = "601", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String houseNumber;

    @Schema(description = "新房项目-项目名", example = "四季城", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String newHouseCommunityName;


    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "新房项目-项目Id", example = "1244222499288", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long newHouseCommunityId;

    /**
     * 收齐日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    @Schema(description = "收齐日期", example = "2024-09-08")
    private LocalDate collectAllDate;


    /**
     * 提成结算状态(字典值: settlement_status)
     */
    @Schema(description = "提成结算状态(字典值: settlement_status)")
    private String commissionSettleStatus;

    /**
     * 签约业绩结算状态(字典值: settlement_status)
     */
    @Schema(description = "签约业绩结算状态(字典值: settlement_status)")
    private String contractPerSettleStatus;

    /**
     * 实收业绩结算状态(字典值: settlement_status)
     */
    @Schema(description = "实收业绩结算状态(字典值: settlement_status)")
    private String actualPerSettleStatus;

    /**
     * 核算业绩结算状态(字典值: settlement_status)
     */
    @Schema(description = "核算业绩结算状态(字典值: settlement_status)")
    private String accountingPerSettleStatus;

    /**
     * 复核通过时间
     */
    @Schema(description = "复核通过时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewDateTime;


    @Schema(description = "业绩分配标志：true-自动、false-手工")
    private Boolean allocationFlag;

    @Schema(description = "分配业绩对象")
    private List<? extends AllocationVo> allocationItem;

    /**
     * 合同双方信息
     */
    @Schema(description = "合同双方信息")
    private ContractInfoVo contractInfo;


    /**
     * 收佣信息
     */
    @Schema(description = "收佣信息")
    private List<ReceiveCommissionPlanVo> receiveCommissionPlans;

    /**
     * 意向金信息
     */
    @Schema(description = "意向金信息")
    private DepositVo deposit;

    /**
     * 权证过户信息
     */
    @Schema(description = "权证过户信息")
    private OwnershipTransferVo ownershipTransfer;

    /**
     * 待收佣金
     */
    @Schema(description = "待收佣金")
    private BigDecimal awaitCommission;

    @Schema(description = "权限码列表")
    private List<String> permissions;

    @Schema(description = "是否关联合同（作为合同导入相关字段是否可修改的依据）")
    private Boolean linkContract;
}
