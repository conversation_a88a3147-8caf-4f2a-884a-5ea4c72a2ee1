package mixc.be.rsms.business.vo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractPageVo implements Serializable {

	@Serial
	private static final long serialVersionUID = 6339828096395296545L;

	/**
	 * 主键ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "主键ID", example = "1050780972368437248")
	private Long id;
	
	/**
	 * 合同类型
	 */
	@Schema(description = "合同类型", example = "resident_rent_service")
	private String contractType;
	
	/**
	 * 合同类型名称
	 */
	@Schema(description = "合同类型名称", example = "房屋租赁居间服务合同（住宅）")
	private String contractTypeName;
	
	/**
	 * 合同编号
	 */
	@Schema(description = "合同编号", example = "HT20241201001")
	private String contractNumber;
	
	/**
	 * 居间方公司ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "居间方公司ID", example = "100")
	private Long intermediaryCompanyId;
	
	/**
	 * 居间方公司名称
	 */
	@Schema(description = "居间方公司名称", example = "万象置地")
	private String intermediaryCompanyName;
	
	/**
	 * 合同状态
	 */
	@Schema(description = "合同状态：字典", example = "draft")
	private String contractStatus;
	
	/**
	 * 签约房源ID
	 */
	@Schema(description = "房源ID", example = "12345667")
	private Long houseId;
	
	/**
	 * 签约车位ID
	 */
	@Schema(description = "车位ID", example = "12345667")
	private Long parkingSpaceId;
	
	/**
	 * 签约房源信息
	 */
	@Schema(description = "签约房源信息", example = "3栋2单元|10234|友林小区")
	private String contractHouseInfo;
	
	/**
	 * 签约车位信息
	 */
	@Schema(description = "签约车位信息", example = "地上车位|10234|友林小区")
	private String contractParkSpaceInfo;
	
	/**
	 * 乙方（客户）ID
	 */
	@Schema(description = "乙方（客户）ID", example = "123")
	private Long customerId;
	
	/**
	 * 乙方（客户）姓名
	 */
	@Schema(description = "乙方（客户）姓名", example = "李四")
	private String customerName;
	
	/**
	 * 乙方（客户）手机号码
	 */
	@Schema(description = "乙方（客户）手机号码", example = "132****123")
	private String customerMobile;
	
	/**
	 * 乙方（客户）手机号码-BASE64
	 */
	@Schema(description = "乙方（客户）手机号码-BASE64", example = "13245678123")
	private String customerMobileEncode;
	
    /**
     * 甲方(业主)姓名
     */
	@Schema(description = "甲方(业主)姓名", example = "张三")
    private String leaserUserName;
	
	/**
     * 甲方(业主)手机号码
     */
	@Schema(description = "甲方(业主)手机号码", example = "13245678123")
    private String leaserMobile;
	
	/**
     * 甲方(业主)手机号码-BASE64
     */
	@Schema(description = "甲方(业主)手机号码-BASE64", example = "13245678123")
    private String leaserMobileEncode;
    
	/**
	 * 合同签署日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Schema(description = "合同签署日期", example = "2024-12-17")
	private LocalDateTime contractSignDate;
	
	/**
	 * 成交价格
	 */
	@Schema(description = "成交价格", example = "120.00")
	private BigDecimal transactionPrice;
	
	/**
	 * 业主佣金总额
	 */
	@Schema(description = "业主佣金总额", example = "120.00")
	private BigDecimal leaserCommission;
	
	/**
	 * 客户佣金总额
	 */
	@Schema(description = "客户佣金总额", example = "120.00")
	private BigDecimal customerCommission;
	
	/**
	 * 佣金总额
	 */
	@Schema(description = "佣金总额", example = "120.00")
	private BigDecimal commission;
	
	/**
	 * 创建人ID
	 */
	@Schema(description = "创建人ID", example = "12344")
	private Long createUser;
	
	/**
	 * 创建人
	 */
	@Schema(description = "创建人", example = "张三")
	private String creator;
	
	/**
	 * 权限码
	 */
	@Schema(description = "权限码", example = "[esign.contract_manage.view]")
	private List<String> permissions;
}
