package mixc.be.rsms.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2025/04/09
 */
@Data
public class SubItemRefundOrderVo implements Serializable {
    @Serial
    private static final long serialVersionUID = -9081766441217386126L;

    @Schema(description = "收款子订单号")
    private String receiveSubOrderCode;

    @Schema(description = "金额")
    private BigDecimal amount;

}
