package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/09
 */
@Data
public class OrderRefundVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1494607787219678980L;


    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "退款子订单ID")
    private Long id;

    /**
     * 主订单Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主订单Id")
    private Long parentOrderId;

    /**
     * 子订单编号
     */
    @Schema(description = "子订单编号")
    private String subOrderCode;

    /**
     * 退款金额
     */
    @Schema(description = "退款金额")
    private BigDecimal amount;

    /**
     * 退款渠道
     */
    @Schema(description = "退款渠道")
    private String refundChannel;

    /**
     * 收款银行账号
     */
    @Schema(description = "收款银行账号")
    private String bankNum;

    /**
     * 收款账号名
     */
    @Schema(description = "收款账号名")
    private String accountName;

    /**
     * 收款开户行
     */
    @Schema(description = "收款开户行")
    private String bankName;

    /**
     * 收款账户城市
     */
    @Schema(description = "收款账户城市")
    private String city;

    /**
     * 退款对应的 收款子订单信息
     */
    @Schema(description = "退款子单")
    private List<SubItemRefundOrderVo> subItemOrderList;

    /**
     * 订单状态(字典值:order_status)
     */
    @Schema(description = "订单状态(字典值:order_status)")
    private String orderStatus;


    /**
     * 收费系统退款订单号
     */
    @Schema(description = "收费系统退款订单号", example = "R202504110070")
    private String sfRefundOrderNum;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createUser;

    @Schema(description = "创建人姓名")
    private String createName;

    @Schema(description = "创建人手机号")
    private String createMobile;

    /**
     * 业务记录Id
     */
    @Schema(description = "业务记录Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long businessId;
}
