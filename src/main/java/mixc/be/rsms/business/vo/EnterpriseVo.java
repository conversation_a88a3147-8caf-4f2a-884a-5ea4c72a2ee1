package mixc.be.rsms.business.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName EnterpriseVo
 * @Description 企业管理vo
 * <AUTHOR>
 * @date 2025-02-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnterpriseVo implements Serializable {

	private static final long serialVersionUID = -5544404547291582452L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "主键", example = "11222")
	private Long id;
	
	/**
	 * 企业编码
	 */
	@Schema(description = "企业编码", example = "QY0001")
	private String enterpriseCode;
	
	/**
	 * 企业名称
	 */
	@Schema(description = "企业名称", example = "华润置地")
	private String enterpriseName;
	
	/**
	 * 状态：0-禁用、1-启用
	 */
	@Schema(description = "状态：0-禁用、1-启用", example = "0")
	private Integer status;
	
	/**
	 * 所属城市ID
	 */
	@Schema(description = "所属城市ID", example = "110100")
	private Integer cityId;
	
	/**
	 * 所属城市名称
	 */
	@Schema(description = "所属城市名称", example = "深圳")
	private String cityName;
	
	/**
	 * 签章类型：公章-official_seal, 合同专用章-contract_seal, 财务专用章-financial_seal
	 */
	@Schema(description = "签章类型（枚举）：公章-official_seal, 合同专用章-contract_seal, 财务专用章-financial_seal", example = "financial_seal")
	private String signatureType;
	
	/**
	 * 印章名称
	 */
	@Schema(description = "印章名称", example = "深圳市华润置地有限公司")
	private String sealName;
	
	/**
	 * 权限列表
	 */
	@Schema(description = "权限列表", example = "[enterprise_manage.close_house.edit_vr]")
	private List<String> permissions;
	
}
