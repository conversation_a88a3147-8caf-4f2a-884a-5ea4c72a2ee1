package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 交易信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
public class TransactionInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -2104031268117954462L;
    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 交易时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "交易时间", example = "2023-10-08 18:28:20")
    private LocalDateTime transactionDateTime;

    /**
     * 交易类型 (t_type_01：收款、t_type_02：退回、t_type_03：转佣、t_type_03_01：住宅二手房买卖居间服务费、t_type_03_02：住宅二手房租赁居间服务费、t_type_03_03：车位二手房买卖居间服务费、t_type_03_04：车位二手房租赁居间服务费、t_type_03_05：金融机构居间服务费、t_type_03_06：商铺二手房租赁居间服务费、t_type_03_07：商铺二手房买卖居间服务费、t_type_03_08：写字楼二手房租赁居间服务费、t_type_03_09：写字楼二手房买卖居间服务费、t_type_03_10：车位新盘分销居间服务费)
     */
    @Schema(description = "交易类型(字典值)", example = "t_type_01：收款、t_type_02：退回、t_type_03：转佣、t_type_03_01：住宅二手房买卖居间服务费、t_type_03_02：住宅二手房租赁居间服务费、t_type_03_03：车位二手房买卖居间服务费、t_type_03_04：车位二手房租赁居间服务费、t_type_03_05：金融机构居间服务费、t_type_03_06：商铺二手房租赁居间服务费、t_type_03_07：商铺二手房买卖居间服务费、t_type_03_08：写字楼二手房租赁居间服务费、t_type_03_09：写字楼二手房买卖居间服务费、t_type_03_10：车位新盘分销居间服务费")
    private String transactionType;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额", example = "100.00")
    private BigDecimal transactionAmount;


    /**
     * 成交合同编号/成交报告
     */
    @Schema(description = "合同编号", example = "22eewwssd")
    private String contractNum;

    /**
     * 合同附件Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "合同附件Id(文件上传接口返回的id)", example = "234422834")
    private Long enclosureFileId;

    /**
     * 合同文件名
     */
    @Schema(description = "合同文件名", example = "12342.pdf")
    private String enclosureFileName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 交易类型的状态 unmodified_pass:审核过但未修改过记录、modify_pass:审核过且修改过记录、insert:新增记录
     */
    @Schema(description = "交易类型的状态：unmodified_pass:审核过但未修改过记录、modify_pass:审核过且修改过记录、insert:新增记录")
    private String status;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态")
    private String orderStatus;

    /**
     * 子订单ID
     */
    @Schema(description = "子订单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long subOrderId;

    /**
     * 子订单编号
     */
    @Schema(description = "子订单编号")
    private String subOrderCode;

    @Schema(description = "是否允许修改")
    private Boolean edit;


    @Schema(description = "可用余额[可用于退款的金额]，默认为：0.00")
    private BigDecimal availableAmount;

    /**
     * 收款订单Id
     */
    @Schema(description = "收款订单Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long receiveSubOrderId;


    /**
     * 退款渠道
     */
    @Schema(description = "退款渠道")
    private String refundChannel;

    /**
     * 收款银行账号
     */
    @Schema(description = "收款银行账号")
    private String bankNum;

    /**
     * 收款账号名
     */
    @Schema(description = "收款账号名")
    private String accountName;

    /**
     * 收款开户行
     */
    @Schema(description = "收款开户行")
    private String bankName;

    /**
     * 收款账户城市
     */
    @Schema(description = "收款账户城市")
    private String city;
}
