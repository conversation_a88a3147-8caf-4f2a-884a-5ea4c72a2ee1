package mixc.be.rsms.business.vo;

import java.io.Serializable;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractAttachmentVo implements Serializable  {

	private static final long serialVersionUID = 7837694663691724327L;

	/**
	 * 甲方房屋产权资料复印件
	 */
	@Schema(description = "甲方房屋产权资料复印件", example = "")
	private List<DataFileInfoVo> leaserOwnershipAttachment;
	
	/**
	 * 甲乙双方身份证明复印件
	 */
	@Schema(description = "甲乙双方身份证明复印件", example = "")
	private List<DataFileInfoVo> identityAttachment;
	
	/**
	 * 房屋交接清单
	 */
	@Schema(description = "房屋交接清单", example = "")
	private List<DataFileInfoVo> houseHandoverAttachment;
	
	/**
	 * 乙方身份证明证件
	 */
	@Schema(description = "乙方身份证明证件", example = "")
	private List<DataFileInfoVo> customerIdentityAttachment;
	
	/**
	 * 甲方车位产权资料复印件
	 */
	@Schema(description = "甲方车位产权资料复印件", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
	private List<DataFileInfoVo> leaserCarOwnershipAttachment;
	
	/**
	 * 乙方车辆资料复印件
	 */
	@Schema(description = "乙方车辆资料复印件", example = "")
	private List<DataFileInfoVo> customerCarInfoAttachment;
	
	/**
	 * 补充条款
	 */
	@Schema(description = "补充条款", example = "")
	private List<DataFileInfoVo> supplementaryTermsAttachment;
	
	/**
	 * 房屋平面图、装修一览表
	 */
	@Schema(description = "房屋平面图、装修一览表", example = "")
	private List<DataFileInfoVo> houseDesignAndDecorationAttachment;
	
	/**
	 * 合同附件
	 */
	@Schema(description = "合同附件", example = "")
	private List<DataFileInfoVo> contractAttachment;
}
