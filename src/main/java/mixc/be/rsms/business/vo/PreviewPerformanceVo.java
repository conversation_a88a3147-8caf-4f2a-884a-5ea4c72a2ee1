package mixc.be.rsms.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/05/29
 */
@Data
public class PreviewPerformanceVo implements Serializable {
    @Serial
    private static final long serialVersionUID = -737253468497399361L;

    @Schema(description = "房源推荐人Id")
    private Long houseRecommendPersonId;


    @Schema(description = "房源推荐人姓名")
    private String houseRecommendPersonName;

    @Schema(description = "客源推荐人Id")
    private Long customerRecommendPersonId;

    @Schema(description = "客源推荐人姓名")
    private String customerRecommendPersonName;

    @Schema(description = "业绩自动分配信息")
    private List<? extends AllocationVo> allocationItem;
}
