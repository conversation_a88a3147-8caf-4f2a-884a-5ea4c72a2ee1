package mixc.be.rsms.business.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 成交意向金/订金表 新增参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
@Builder
public class DepositVo implements Serializable {


    @Serial
    private static final long serialVersionUID = -3016135117513669656L;
    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 编号
     */
    @Schema(description = "编号")
    private String depositCode;

    /**
     * 类型:(intention:意向金、deposit:订金)
     */
    @Schema(description = "类型(字典值)", example = "intention:意向金、deposit:订金")
    private String type;

    /**
     * 类目:(dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖)
     */
    @Schema(description = "类目(字典值)", example = "dps_c_1:住宅二手房买卖、dps_c_2:住宅二手房租赁、dps_c_3:车位二手房买卖、dps_c_4:车位二手房租赁、dps_c_5:商铺二手房租赁、dps_c_6:商铺二手房买卖、dps_c_7:写字楼二手房租赁、dps_c_8:写字楼二手房买卖、dps_c_9:住宅新盘买卖、dps_c_10:车位新盘买卖")
    private String category;

    /**
     * 成交合同编号
     */
    @Schema(description = "成交合同编号")
    private String transactionContractNum;

    /**
     * 关联房源/车位信息列表
     */
    @Schema(description = "关联房源信息列表", example = "")
    private List<HouseInfoVo> houseDescList;

    /**
     * 关联车位信息列表
     */
    @Schema(description = "关联车位信息列表")
    private List<ParkingSpaceVo> parkingSpaceList;

    /**
     * 客户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "客户Id", example = "2")
    private Long customerId;

    /**
     * 客户姓名
     */
    @Schema(description = "客户姓名", example = "张三")
    private String customerName;

    /**
     * 客户联系方式
     */
    @Schema(description = "客户联系方式(脱敏)")
    private String customerMobile;

    @Schema(description = "客户联系方式(加密)")
    private String customerMobileEncode;


    /**
     * 所属经纪人Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "所属经纪人Id", example = "192838932438")
    private Long brokerId;

    /**
     * 所属经纪人姓名
     */
    @Schema(description = "所属经纪人姓名", example = "熊大")
    private String brokerName;

    /**
     * 所属经纪人手机号
     */
    @Schema(description = "所属经纪人手机号(脱敏)", example = "12839294829")
    private String brokerMobile;


    @Schema(description = "所属经纪人手机号(加密)")
    private String brokerMobileEncode;

    /**
     * 所属经纪人所属门店
     */
    @Schema(description = "所属经纪人所属门店", example = "XX门店")
    private String brokerStoreName;

    /**
     * 状态:(unexamination:未审核、examinationing:审核中、examinationed:已审核)
     */
    @Schema(description = "状态(字典值)", example = "unexamination:未审核、examinationing:审核中、examinationed:已审核")
    private String status;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    /**
     * 实收金额
     */
    @Schema(description = "实收金额")
    private BigDecimal actualReceiveAmount;

    /**
     * 收款金额
     */
    @Schema(description = "收款金额")
    private BigDecimal receiveAmount;

    /**
     * 退款金额
     */
    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    /**
     * 转佣金额
     */
    @Schema(description = "转佣金额")
    private BigDecimal commissionAmount;

    /**
     * 剩余金额
     */
    @Schema(description = "剩余金额")
    private BigDecimal balanceAmount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;


    /**
     * 主订单Id
     */
    @Schema(description = "主订单Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 主订单编号
     */
    @Schema(description = "主订单编号")
    private String orderCode;

    /**
     * 交易信息
     */
    @Schema(description = "交易信息列表")
    private List<TransactionInfoVo> transactionInfoList;

    /**
     * 交易信息的合同附件ID（近合同报告 详情接口返回使用）
     */
    @Schema(description = "交易信息的合同附件文件信息（成交合同 详情接口返回使用）")
    private List<DataFileInfoVo> transactionInfoFile;


    @Schema(description = "权限码")
    private List<String> permissions;
}
