package mixc.be.rsms.business.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.dto.ContractArchiveParams;
import mixc.be.rsms.business.domain.dto.ContractSignAddParams;
import mixc.be.rsms.business.domain.dto.ContractSignQueryParams;
import mixc.be.rsms.business.service.IBizContractService;
import mixc.be.rsms.business.service.ICompanyService;
import mixc.be.rsms.business.service.IContractService;
import mixc.be.rsms.business.service.IContractSignTypeService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.CompanyVo;
import mixc.be.rsms.business.vo.ContractDetailVo;
import mixc.be.rsms.business.vo.ContractPageVo;
import mixc.be.rsms.business.vo.ContractTypeDropdownVo;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.business.ContractSignTypeResp;
import mixc.be.rsms.pojo.mobile.CustomerAssetDetailResp;
import mixc.be.rsms.pojo.mobile.CustomerAssetResp;
import mixc.be.rsms.pojo.third.JoyContractPreview;

/**
 * 
 * @ClassName ContractSignController
 * @Description 合同签约控制器
 * <AUTHOR>
 * @create 2024-12-13 16:00
 */
@RestController
@RequestMapping("/contract")
@Tag(name = "合同签约控制器")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class ContractSignController {
	
	private final IContractSignTypeService contractSignTypeService;
	private final IContractService contractService;
	private final IBizContractService bizContractService;
	private final ICompanyService companyService;
	
    @Operation(summary = "合同类型获取-服务内部调用")
    @GetMapping("listContractSignType")
    public ResultVo<List<ContractSignTypeResp>> listContractSignType(@RequestParam(required = false) String cityCode) {
        return ResultVoUtil.success(contractSignTypeService.listContractSignType(cityCode, null));
    }
    
    @Operation(summary = "新增合同签约-服务内部调用")
    @PostMapping("addContractSign")
    public ResultVo<JoyContractPreview> addContractSign(@RequestBody ContractSignAddParams addParams) {
        return contractService.addContractSign(addParams);
    }
    
    @Operation(summary = "合同签约分页查询-服务内部调用")
    @GetMapping("appPageContractSign")
    public ResultVo<Page<ContractPageVo>> appPageContractSign(ContractSignQueryParams pageParams) {
        return ResultVoUtil.success(bizContractService.appPageContractSign(pageParams));
    }
    
    @Operation(summary = "合同签约分页查询")
    @GetMapping("pageContractSign")
    public ResultVo<Page<ContractPageVo>> pageContractSign(ContractSignQueryParams pageParams) {
        return ResultVoUtil.success(bizContractService.pageContractSign(pageParams));
    }
    
    @Operation(summary = "合同签约详情")
    @GetMapping("detail")
    public ResultVo<ContractDetailVo> contractDetail(@RequestParam Long contractId, @RequestParam(required = false) String sourceType) {
        return ResultVoUtil.success(contractService.contractDetail(contractId, sourceType));
    }
    
    @Operation(summary = "合同签约更新")
    @PostMapping("update")
    public ResultVo<JoyContractPreview> updateContract(@RequestBody ContractSignAddParams updateParams) {
    	return contractService.updateContract(updateParams);
    }
    
    @Operation(summary = "电子签约提交审核-服务内部调用")
    @PostMapping("submitApproval")
    public ResultVo<Void> submitApproval(@RequestParam Long contractId) {
    	bizContractService.submitApproval(contractId);
        return ResultVoUtil.success();
    }
    
    @Operation(summary = "电子签约审核通过/拒绝-服务内部调用")
    @PostMapping("approval")
    public ResultVo<String> approvalContract(@RequestParam Long contractId, @RequestParam String approvalType) {
    	return bizContractService.contractApproval(contractId, approvalType);
    }
    
    @Operation(summary = "电子签约合同删除")
    @PostMapping("delete")
    public ResultVo<String> deleteContract(@RequestParam Long contractId) {
    	return bizContractService.deleteContract(contractId);
    }
    
    @Operation(summary = "电子签约重新签约")
    @PostMapping("resign")
    public ResultVo<Void> resignContract(@RequestParam Long contractId) {
    	bizContractService.resignContract(contractId);
    	return ResultVoUtil.success();
    }
    
    @Operation(summary = "居间方公司列表获取")
    @GetMapping("/listCompanys")
    public ResultVo<List<CompanyVo>> listCompanys() {
        return ResultVoUtil.success(companyService.listCompanys());
    }
	
    @Operation(summary = "客户资产获取")
    @GetMapping("/listCustomerAssets")
    public List<CustomerAssetResp> listCustomerAssets(@RequestParam String customerMobile) {
        return bizContractService.listCustomerAssets(customerMobile);
    }
    
    @Operation(summary = "客户资产详情-房源信息获取")
    @GetMapping("/customerAssetsDetail")
    public CustomerAssetDetailResp customerAssetsDetail(
    		@RequestParam Long assetsId, @RequestParam String customerMobile, @RequestParam String assetsType) {
        return bizContractService.customerAssetsDetail(assetsId, customerMobile, assetsType);
    }
    
    @Operation(summary = "合同签约-内部调用")
    @Parameters({
        @Parameter(name = "joyContractId",description = "朝昔合同ID",required = true,in = ParameterIn.QUERY),
        @Parameter(name = "customerMobile",description = "客户ID",required = true,in = ParameterIn.QUERY)
    })
    @GetMapping("/getJoyContractLockUrl")
    public ResultVo<String> getJoyContractLockUrl(@RequestParam String joyContractId, @RequestParam String customerMobile) {
		return bizContractService.getJoyContractLockUrl(joyContractId, customerMobile);
    }
    
    @Operation(summary = "朝昔合同详情地址-内部调用")
    @Parameters({
        @Parameter(name = "joyContractId",description = "朝昔合同ID",required = true,in = ParameterIn.QUERY)
    })
    @GetMapping("/getJoyContractDetailUrl")
    public ResultVo<String> getJoyContractDetailUrl(@RequestParam String joyContractId, @RequestParam String source) {
		return bizContractService.getJoyContractDetailUrl(joyContractId, source);
    }
    
    @Operation(summary = "朝昔合同状态定时同步")
    @GetMapping("/syncJoyContractStatus")
    public ResultVo<Boolean> syncJoyContractStatus() {
    	bizContractService.syncJoyContractStatusToRsms();
    	return ResultVoUtil.success();
    }
    
    @Operation(summary = "电子签约合同归档")
    @PostMapping("archive")
    public ResultVo<Void> archiveContract(@RequestBody ContractArchiveParams archiveParams) {
    	bizContractService.archiveContract(archiveParams);
        return ResultVoUtil.success();
    }
    
    @Operation(summary = "合同管理-合同类型下拉框获取")
    @PostMapping("contractTypeDropdown")
    public ResultVo<List<ContractTypeDropdownVo>> contractTypeDropdown() {
        return ResultVoUtil.success(contractSignTypeService.listContractTypeDropdown(null, true));
    }
    
    @Operation(summary = "合同租约到期提醒")
    @GetMapping("/leaseExpiationNotice")
    public ResultVo<Boolean> leaseExpiationNotice(@RequestParam(required = false) String jobDate) {
    	bizContractService.inspectContractLeaseTask(jobDate);
    	return ResultVoUtil.success();
    }
    
    @Operation(summary = "通过合同编号列表查询合同-服务内部调用")
    @GetMapping("/queryContractByNumbers")
    public List<String> queryContractByNumbers(@RequestParam List<String> contractNumbers) {
        return contractService.listContractByNumbers(contractNumbers);
    }
    
    @Operation(summary = "合同类型下拉框获取-内部服务调用")
    @GetMapping("listContractType")
    public List<ContractTypeDropdownVo> listContractType(@RequestParam boolean flag) {
        return contractSignTypeService.listContractTypeDropdown(null, flag);
    }
    
}
