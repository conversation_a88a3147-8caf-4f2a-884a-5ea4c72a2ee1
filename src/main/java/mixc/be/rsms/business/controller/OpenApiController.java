package mixc.be.rsms.business.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import mixc.be.rsms.business.domain.dto.JoyContractCallbackParam;
import mixc.be.rsms.business.service.IContractService;
import mixc.be.rsms.business.service.IOrderService;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.third.SFCallBackResp;
import mixc.be.rsms.pojo.third.SFOrderCallBackParam;
import mixc.be.rsms.pojo.third.SFOrderRefundCallBackParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/25
 */
@RestController
@RequestMapping("/openAPI")
@Tag(name = "开放接口控制器")
@AllArgsConstructor(onConstructor = @__(@Autowired))
public class OpenApiController {

    private final IOrderService orderService;
    private final IContractService contractService;


    @Operation(summary = "订单回调(收费系统回调调用)")
    @PostMapping("/callBack")
    public SFCallBackResp payCallBackOnSF(@RequestBody SFOrderCallBackParam callBackParam) {
        return orderService.payCallBackOnSF(callBackParam);
    }

    @Operation(summary = "订单退款回调(收费系统回调调用)")
    @PostMapping("/refundCallBack")
    public SFCallBackResp refundCallBackOnSF(@RequestBody SFOrderRefundCallBackParam callBackParam) {
        return orderService.refundCallBackOnSF(callBackParam);
    }

    @Operation(summary = "合同状态回调(朝昔轻合同回调调用)")
    @PostMapping("/joyContractStatus/callBack")
    public ResultVo<String> joyContractStatusCallBack(
    		@RequestHeader("Joy-Contract-Signature") String joyContractSignature,
    		@RequestHeader("Joy-Contract-Timestamp") String joyContractTimestamp,
    		@RequestHeader("Joy-Contract-Nonce") String joyContractNonce,
    		@RequestBody JoyContractCallbackParam joyContractCallbackParam) {
        return contractService.contractStatusCallBack(joyContractSignature, joyContractTimestamp, joyContractNonce, joyContractCallbackParam.getCiphertext());
    }

}
