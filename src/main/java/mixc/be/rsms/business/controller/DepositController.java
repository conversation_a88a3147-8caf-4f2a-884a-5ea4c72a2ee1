package mixc.be.rsms.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.dto.DepositAddParams;
import mixc.be.rsms.business.domain.dto.DepositAuditParams;
import mixc.be.rsms.business.domain.dto.DepositQueryParams;
import mixc.be.rsms.business.domain.dto.DepositRefundParam;
import mixc.be.rsms.business.service.IDepositService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.DepositVo;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.business.DepositResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 成交意向金/订金 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@RestController
@RequestMapping("/deposit")
@Tag(name = "成交意向金/订金前端控制器")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class DepositController {
    private final IDepositService depositService;

    @Operation(summary = "列表分页查询")
    @GetMapping("/page")
    public ResultVo<IPage<DepositVo>> page(DepositQueryParams params) {
        return ResultVoUtil.success(depositService.page(params));
    }

    @Operation(summary = "新增/修改(通过ID区分)")
    @PostMapping("/saveOrUpdate")
    public ResultVo<String> addDeposit(@Valid @RequestBody DepositAddParams depositAddParams) {
        return ResultVoUtil.success(depositService.saveOrUpdate(depositAddParams).toString());
    }

    @Operation(summary = "删除")
    @DeleteMapping("/deleteById")
    public ResultVo<Integer> deleteById(@RequestParam("id") Long id) {
        return ResultVoUtil.success(depositService.deleteDeposit(id));
    }

    @Operation(summary = "提交")
    @PostMapping("/submit")
    public ResultVo<String> submitDeposit(@RequestBody DepositAuditParams params) {
        return ResultVoUtil.success(depositService.submitDeposit(params).toString());
    }

    @Operation(summary = "审核/驳回")
    @PostMapping("/auditOrReject")
    public ResultVo<Integer> auditOrReject(@RequestBody DepositAuditParams params) {
        return ResultVoUtil.success(depositService.auditOrReject(params));
    }

    @Operation(summary = "反审核")
    @PostMapping("/reverseAudit")
    public ResultVo<Integer> reverseAudit(@RequestBody DepositAuditParams params) {
        return ResultVoUtil.success(depositService.reverseAudit(params));
    }

    @Operation(summary = "详情")
    @GetMapping("/getDetail/{id}")
    public ResultVo<DepositVo> getDeposit(@PathVariable("id") Long id) {
        return ResultVoUtil.success(depositService.getDeposit(id));
    }

    @Operation(summary = "新增意向金退款接口")
    @PostMapping("/refund")
    public ResultVo<Void> refund(@Valid @RequestBody DepositRefundParam param) {
        depositService.refund(param);
        return ResultVoUtil.success();
    }

    @Operation(summary = "查询绑定/未绑定成交报告的意向金记录")
    @GetMapping("/queryUnBindingTransactionsReportList")
    public ResultVo<List<DepositVo>> queryUnBindingTransactionsReportList(@RequestParam(value = "depositCode", required = false) String depositCode
            , @RequestParam(value = "customerId") Long customerId
            , @RequestParam("bindingTransactionReport") Boolean bindingTransactionReport) {
        return ResultVoUtil.success(depositService.queryUnBindingTransactionsReportList(depositCode, customerId, bindingTransactionReport));
    }

    @Operation(summary = "根据客户ID 查询意向金(微服务调用)")
    @GetMapping("/queryDepositByCustomerId")
    public List<DepositResp> queryDepositByCustomerId(@RequestParam("customerIds") List<Long> customerIds) {
        return depositService.queryDepositByCustomerId(customerIds);
    }

    @Operation(summary = "根据id 查询意向金(微服务调用)")
    @GetMapping("/queryDepositById")
    public List<DepositResp> queryDepositById(@RequestParam("ids") List<Long> ids){
        return depositService.queryDepositById(ids);
    }

    @Operation(summary = "根据资产ID(房源id、车位ID) 查询意向金(微服务调用)")
    @GetMapping("/queryDepositByAssetsId")
    public List<DepositResp> queryDepositByAssetsId(@RequestParam("assetsIds") List<Long> assetsIds){
        return depositService.queryDepositByAssetsId(assetsIds);
    }

    @Operation(summary = "根据房源id、客户手机号 查询意向金(微服务调用)")
    @GetMapping("/queryDepositByAssetsIdAndCustomerMobile")
    public List<DepositResp> queryDepositByAssetsIdAndCustomerMobile(@RequestParam(value = "assetsId", required = false) Long assetsId, @RequestParam("customerMobile") String customerMobile) {
        return depositService.queryDepositByAssetsIdAndCustomerMobile(assetsId, customerMobile);
    }


}
