package mixc.be.rsms.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.dto.OwnershipTransferQueryParams;
import mixc.be.rsms.business.domain.dto.OwnershipTransferUpdateParams;
import mixc.be.rsms.business.service.IOwnershipTransferService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.OwnershipTransferVo;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.business.OwnershipTransferResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权证过户控制器
 * @ClassName OwnershipTransferController
 * <AUTHOR>
 * @date 2024-10-20
 */
@RestController
@RequestMapping("/ownership")
@Tag(name = "权证过户")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class OwnershipTransferController {

	private final IOwnershipTransferService service;
	
	@Operation(summary = "权证过户查询")
    @GetMapping("/query")
    public ResultVo<IPage<OwnershipTransferVo>> pageOwnershipTransfer(OwnershipTransferQueryParams queryParams) {
        return ResultVoUtil.success(service.pageOwnershipTransfer(queryParams));
    }
	
	@Operation(summary = "权证过户详情")
    @GetMapping("/detail")
    public ResultVo<OwnershipTransferVo> queryOwnershipTransferDetail(@RequestParam Long id) {
        return ResultVoUtil.success(service.queryOwnershipTransferDetail(id));
    }
	
	@Operation(summary = "权证过户更新")
    @PostMapping("/update")
    public ResultVo<Void> updateOwnershipTransferDetail(@Valid @RequestBody OwnershipTransferUpdateParams updateParams) {
		service.saveOrUpdate(updateParams);
        return ResultVoUtil.success();
    }


    @Operation(summary = "权证过户分页查询(微服务调用)")
    @GetMapping("/pageOwnershipByMobile")
    public ResultVo<IPage<OwnershipTransferVo>> pageOwnershipByMobile(String mobile, Integer pageNum, Integer pageSize) {
        return ResultVoUtil.success(service.pageOwnershipByMobile(mobile, pageNum, pageSize));
    }

    @Operation(summary = "根据合同编号查询权证过户")
    @GetMapping("/mobileGetDetailByTransactionContractNum")
    public List<OwnershipTransferResp> mobileGetDetailByTransactionContractNum(@RequestParam("transactionContractNums") List<String> transactionContractNums) {
        return service.mobileGetDetail(transactionContractNums);
    }

}
