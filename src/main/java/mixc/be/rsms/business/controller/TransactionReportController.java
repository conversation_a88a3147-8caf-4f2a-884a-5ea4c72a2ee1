package mixc.be.rsms.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.dto.PreviewPerformanceParams;
import mixc.be.rsms.business.domain.dto.TransactionReportAddParams;
import mixc.be.rsms.business.domain.dto.TransactionReportEditParams;
import mixc.be.rsms.business.domain.dto.TransactionReportQueryParams;
import mixc.be.rsms.business.service.ITransactionReportService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.*;
import mixc.be.rsms.pojo.business.TransactionReportResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/23
 */
@RestController
@RequestMapping("/transactionReport")
@Tag(name = "成交报告管理控制器")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class TransactionReportController {

    private final ITransactionReportService transactionReportService;

    @Operation(summary = "列表分页查询")
    @GetMapping("/page")
    public ResultVo<IPage<TransactionReportVo>> page(TransactionReportQueryParams params) {
        return ResultVoUtil.success(transactionReportService.page(params));
    }

    @Operation(summary = "删除")
    @DeleteMapping("/deleteById")
    public ResultVo<Integer> deleteById(Long id) {
        return ResultVoUtil.success(transactionReportService.deleteById(id));
    }
    @Operation(summary = "提交")
    @PostMapping("/submit")
    public ResultVo<String> submit(@Valid  @RequestBody TransactionReportEditParams params){
        return ResultVoUtil.success(transactionReportService.submit(params).toString());
    }

    @Operation(summary = "结案")
    @PostMapping("/close")
    public ResultVo<String>  close(@Valid @RequestBody TransactionReportEditParams params){
        return ResultVoUtil.success(transactionReportService.close(params).toString());
    }

    @Operation(summary = "审核或者驳回")
    @PostMapping("/auditOrReject")
    public ResultVo<String> auditOrReject(@Valid @RequestBody TransactionReportEditParams params){
        return ResultVoUtil.success(transactionReportService.auditOrReject(params).toString());
    }

    @Operation(summary = "复核或者驳回")
    @PostMapping("/reviewOrReject")
    public ResultVo<String> reviewOrReject(@Valid @RequestBody TransactionReportEditParams params){
        return ResultVoUtil.success(transactionReportService.reviewOrReject(params).toString());
    }

    @Operation(summary = "反审核")
    @PostMapping("/reverseAudit")
    public ResultVo<String> reverseAudit(@Valid @RequestBody TransactionReportEditParams params){
        return ResultVoUtil.success(transactionReportService.reverseAudit(params).toString());
    }

    @Operation(summary = "新增/修改")
    @PostMapping("/saveOrUpdate")
    public ResultVo<String> saveOrUpdate(@Valid @RequestBody TransactionReportAddParams params){
        return ResultVoUtil.success(transactionReportService.saveOrUpdate(params).toString());
    }

    @Operation(summary = "返回详情")
    @GetMapping("/getDetail/{id}")
    public ResultVo<TransactionReportVo> getDetail(@PathVariable("id") Long id){
        return ResultVoUtil.success(transactionReportService.getDetail(id));
    }

    @Operation(summary = "生成成交报告")
    @PostMapping("/generateInfo")
    public ResultVo<Void> generateInfo(@RequestBody TransactionReportAddParams params) {
        transactionReportService.generateInfo(params.getTransactionContractNum());
        return ResultVoUtil.success();
    }

    @Operation(summary = "获取收费项目")
    @GetMapping("/getReceiveCommunity")
    public ResultVo<List<ReceiveCommunityVo>> getReceiveCommunityList(@RequestParam(required = false) Long communityId,
                                                                      @RequestParam(required = false) Long newHouseCommunityId,
                                                                      @RequestParam(required = false) String transactionContractNum) {
        return ResultVoUtil.success(transactionReportService.getReceiveCommunityList(communityId, newHouseCommunityId, transactionContractNum));
    }

    @Operation(summary = "预览业绩提成信息")
    @GetMapping("/previewPerformance")
    public ResultVo<PreviewPerformanceVo> previewPerformance(PreviewPerformanceParams params) {
        return ResultVoUtil.success(transactionReportService.previewPerformance(params));
    }


    @Operation(summary = "根据客户ID 查询成交报告(微服务调用)")
    @GetMapping("/queryTransactionReportByCustomerId")
    public List<TransactionReportResp> queryTransactionReportByCustomerId(@RequestParam("customerIds") List<Long> customerIds){
        return transactionReportService.queryTransactionReportByCustomerId(customerIds);
    }


    @Operation(summary = "根据id 查询成交报告(微服务调用)")
    @GetMapping("/queryTransactionReportIdById")
    public List<TransactionReportResp> queryTransactionReportIdById(@RequestParam("ids") List<Long> ids) {
        return transactionReportService.queryTransactionReportIdById(ids);
    }



    @Operation(summary = "根据业务id 查询成交报告(微服务调用)")
    @GetMapping("/selectInfoOnOperateLog")
    public List<TransactionReportResp> selectInfoOnOperateLog(@RequestParam("businessIdList") List<Long> businessIdList) {
        return transactionReportService.selectInfoOnOperateLog(businessIdList);
    }

    @Operation(summary = "根据资产id（房源ID、车位ID、新房项目ID） 查询成交报告(微服务调用)")
    @GetMapping("/queryTransactionReportByAssetsId")
    public List<TransactionReportResp> queryTransactionReportByAssetsId(@RequestParam("assetsId") Long assetsId) {
        return transactionReportService.queryTransactionReportByAssetsId(assetsId);
    }

    @Operation(summary = "用户的业绩统计(微服务调用)")
    @GetMapping("/achievementStatistics")
    public BigDecimal achievementStatistics(@RequestParam("userId") Long userId) {
        return transactionReportService.achievementStatistics(userId);
    }

    @Operation(summary = "用户成交量统计(微服务调用)")
    @GetMapping("/turnoverStatistics")
    public Long turnoverStatistics(@RequestParam("userId") Long userId) {
        return transactionReportService.turnoverStatistics(userId);
    }
}
