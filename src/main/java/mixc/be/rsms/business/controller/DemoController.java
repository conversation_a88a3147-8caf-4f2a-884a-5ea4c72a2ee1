package mixc.be.rsms.business.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.common.mq.SendMsg;
import mixc.be.rsms.business.domain.FileResp;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.business.MessageContent;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * Description for this class
 *
 * <AUTHOR>
 * @create 2024/7/3 17:40
 */
@RestController
@RequestMapping("/demo")
@Tag(name = "Knife4j配置示例")
@RequiredArgsConstructor
public class DemoController {

    private final SendMsg sendMsg;

    @Operation(summary = "普通body请求")
    @PostMapping("/body")
    public ResultVo<FileResp> body(@RequestBody FileResp fileResp){
        return ResultVoUtil.success(fileResp);
    }

    @Operation(summary = "普通body请求+Param+Header+Path")
    @Parameters({
            @Parameter(name = "id",description = "文件id",in = ParameterIn.PATH),
            @Parameter(name = "token",description = "请求token",required = true,in = ParameterIn.HEADER),
            @Parameter(name = "name",description = "文件名称",required = true,in=ParameterIn.QUERY)
    })
    @PostMapping("/bodyParamHeaderPath/{id}")
    public ResponseEntity<FileResp> bodyParamHeaderPath(@PathVariable("id") String id, @RequestHeader("token") String token, @RequestParam("name")String name, @RequestBody FileResp fileResp){
        fileResp.setName(fileResp.getName()+",receiveName:"+name+",token:"+token+",pathID:"+id);
        return ResponseEntity.ok(fileResp);
    }

    @GetMapping("/hello")
    public String hello() {
        return "springboot3";
    }

    @GetMapping("/sendMsg")
    public String sendMsg() {
        MessageContent<List<String>> messageContent = new MessageContent<>();
        messageContent.setMsgId("123");
        messageContent.setData(List.of("1","2","3"));
        messageContent.setCreateTime(new Date());
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String s = objectMapper.writeValueAsString(messageContent);
            sendMsg.sendChargeOrderMsg(s);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return "springboot3";
    }


}
