package mixc.be.rsms.business.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.service.IPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 业绩主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@RequestMapping("/performance")
@Tag(name = "业绩")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class PerformanceController {

    private final IPerformanceService performanceService;


}
