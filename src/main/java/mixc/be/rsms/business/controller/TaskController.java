package mixc.be.rsms.business.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.service.ITradeOrderLogService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.ResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @date 2025/04/01
 */
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping("/task")
@Tag(name = "自动任务控制器")
public class TaskController {

    private final ITradeOrderLogService tradeOrderLogService;

    @GetMapping("/payOrderStatusInspect")
    @Operation(summary = "支付中订单支付状态检查定时任务")
    public ResultVo<Void> payOrderStatusInspect() {
        tradeOrderLogService.paySerialTimeOutTask();
        return ResultVoUtil.success();
    }


}
