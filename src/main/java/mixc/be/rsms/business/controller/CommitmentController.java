package mixc.be.rsms.business.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.service.ICommitmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 提成主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@RequestMapping("/commitment")
@Tag(name = "提成")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class CommitmentController {

    private final ICommitmentService commitmentService;


}
