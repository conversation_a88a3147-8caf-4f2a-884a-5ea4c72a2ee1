package mixc.be.rsms.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.dto.OrderAddParams;
import mixc.be.rsms.business.domain.dto.OrderQueryParams;
import mixc.be.rsms.business.domain.dto.SubOrderReceiveAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessOrder;
import mixc.be.rsms.business.service.IOrderService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.OrderVo;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.business.CancelPayReq;
import mixc.be.rsms.pojo.business.OrderResp;
import mixc.be.rsms.pojo.business.PayOrderReq;
import mixc.be.rsms.pojo.business.ThirdPayResultResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/09
 */
@RestController
@RequestMapping("/order")
@Tag(name = "订单前端控制器")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class OrderController {

    private final IOrderService orderService;

    @Operation(summary = "订单分页查询")
    @GetMapping("/page")
    public ResultVo<IPage<OrderVo>> page(OrderQueryParams orderQueryParams) {
        return ResultVoUtil.success(orderService.page(orderQueryParams));
    }

    @Operation(summary = "创建订单(微服务调用)")
    @PostMapping("/save")
    public TbBusinessOrder save(@RequestBody OrderAddParams params) {
        return orderService.save(params);
    }

    @Operation(summary = "新增子订单(微服务调用)")
    @PostMapping("/addSubOrder")
    public TbBusinessOrder addSubOrder(@RequestParam("orderId") Long orderId, @RequestParam("subOrders") List<SubOrderReceiveAddParams> receiveAddParams) {
        return orderService.addSubOrder(orderId, receiveAddParams, List.of(), List.of());
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "订单详情")
    public ResultVo<OrderVo> detail(@PathVariable Long id) {
        return ResultVoUtil.success(orderService.detail(id));
    }

    @PostMapping("/posPay")
    @Operation(summary = "POS支付订单")
    public ResultVo<Void> posPay(@RequestBody PayOrderReq params) {
        orderService.posPay(params);
        return ResultVoUtil.success();
    }

    @PostMapping("/thirdPay")
    @Operation(summary = "第三方支付（微信/阿里）")
    public ThirdPayResultResp thirdPay(@RequestBody PayOrderReq params) {
        return orderService.thirdPay(params);
    }

    @Operation(summary = "支付交易查询")
    @GetMapping("/queryTradeOrderInfo")
    public OrderResp queryTradeOrderInfo(@RequestParam Long orderId, @RequestParam Long subOrderId) {
        return orderService.queryTradeOrderInfo(orderId, subOrderId);
    }

    @Operation(summary = "取消聚合支付")
    @PostMapping("/cancelPay")
    public void cancelPay(@RequestBody CancelPayReq cancelPayReq) {
        orderService.cancelPay(cancelPayReq);
    }

    @Operation(summary = "员工端根据合同编号查询所有关联订单(微服务调用)")
    @GetMapping("/queryOrderByContractNum")
    public List<OrderResp> queryOrderByContractNum(@RequestParam(value = "contractNums", required = false) List<String> contractNums, @RequestParam("customerMobile") String customerMobile) {
        return orderService.queryOrderByContractNum(contractNums, customerMobile);
    }

    @Operation(summary = "客户端根据手机号查询关联的订单列表(微服务调用)")
    @GetMapping("/queryOrderByCustomerMobile")
    public Page<OrderResp> queryOrderByCustomerMobile(@RequestParam Integer pageNum, @RequestParam Integer pageSize, @RequestParam String customerMobile) {
        return orderService.queryOrderByCustomerMobile(pageNum, pageSize, customerMobile);
    }


    @Operation(summary = "员工端查询订单列表(微服务调用)")
    @GetMapping("/mobileQueryOrderByUserId")
    public Page<OrderResp> mobileQueryOrderByUserId(@RequestParam Integer pageNum,
                                                    @RequestParam Integer pageSize,
                                                    @RequestParam(required = false) String orderStatus,
                                                    @RequestParam(required = false) String transactionType,
                                                    @RequestParam(required = false) String customerNameKeyWord,
                                                    @RequestParam(required = false) String houseKeyWord,
                                                    @RequestParam(required = false) String parkingKeyWord,
                                                    @RequestParam(required = false) String mobileKeyWord) {
        return orderService.mobileQueryOrderByUserId(pageNum, pageSize, orderStatus, transactionType, customerNameKeyWord, houseKeyWord, parkingKeyWord, mobileKeyWord);
    }



}
