package mixc.be.rsms.business.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.domain.pojo.TbBusinessTradeOrderLog;
import mixc.be.rsms.business.mapper.TbBusinessTradeOrderLogMapper;
import mixc.be.rsms.business.service.IOrderService;
import mixc.be.rsms.business.service.ITradeOrderLogService;
import mixc.be.rsms.common.config.ThreadPoolConfig;
import mixc.be.rsms.common.constant.RedisConstant;
import mixc.be.rsms.common.enums.DropdownEnum;
import mixc.be.rsms.common.utils.JacksonUtil;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2025/03/31
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class TradeOrderLogServiceImpl extends ServiceImpl<TbBusinessTradeOrderLogMapper, TbBusinessTradeOrderLog>
        implements ITradeOrderLogService {

    @Value("${order.timeOut}")
    private Integer orderTimeOut;

    private final TbBusinessTradeOrderLogMapper tradeOrderLogMapper;
    private final IOrderService orderService;
    private final RedissonClient redisson;
    private final ThreadPoolConfig threadPoolConfig;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void insertTradeOrderLog(TbBusinessTradeOrderLog tradeOrderLog) {
        tradeOrderLogMapper.insert(tradeOrderLog);
    }

    @Override
    public void paySerialTimeOutTask() {
        LocalDateTime localDateTime = LocalDateTime.now().minusMinutes(orderTimeOut);
        List<TbBusinessTradeOrderLog> list = list(Wrappers.lambdaQuery(TbBusinessTradeOrderLog.class)
                .eq(TbBusinessTradeOrderLog::getStatus, DropdownEnum.TRADE_ORDER_LOG_STATUS_SUCCESS.getDictKey())
                .lt(TbBusinessTradeOrderLog::getCreateTime, localDateTime));
        log.info("本次定时任务查询到的超时流水号：{}", JacksonUtil.toJSON(list.stream().map(TbBusinessTradeOrderLog::getOrderCode).toList()));

        CompletableFuture.runAsync(() -> {
            RLock lock = redisson.getLock(RedisConstant.TRADE_ORDER_TIME_OUT_KEY);
            try {
                // 防止上一轮任务未完成，而本轮任务已开启......
                boolean b = lock.tryLock(10, -1, TimeUnit.SECONDS);
                if (b) {
                    try {
                        log.info("获取到锁，开始任务.......");
                        list.forEach(tradeOrderLog -> {
                            log.info("开始更新下单流水号：{} 状态.....", tradeOrderLog.getOrderSerialNumber());
                            orderService.queryTradeOrderInfo(tradeOrderLog.getOrderId(), tradeOrderLog.getSubOrderId());
                            log.info("下单流水号：{} 状态更新完成.....", tradeOrderLog.getOrderSerialNumber());
                        });
                        log.info("任务执行完成.........");
                    } catch (Exception e) {
                        log.error("同步订单支付状态异常....", e);
                    } finally {
                        log.info("释放锁.......");
                        lock.unlock();
                    }
                } else {
                    log.info("锁获取超时，表示：上一轮定时任务未执行完成，本轮任务不开启....");
                }
            } catch (InterruptedException e) {
                log.error("锁获取异常......", e);
            }
        }, threadPoolConfig.virtualThreadPerTaskExecutor()).join();

    }
}
