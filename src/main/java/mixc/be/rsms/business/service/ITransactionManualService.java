package mixc.be.rsms.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionManual;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 成交报告-业绩分配信息-手动分配 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface ITransactionManualService extends IService<TbBusinessTransactionManual> {

    /**
     * @description 查询手动分配数据
     * <AUTHOR>
     * @param[1] transactionIds
     * @throws
     * @return Map<List<TbBusinessTransactionManual>>
     * @time 2025/6/4 16:52
     */
    Map<Long, List<TbBusinessTransactionManual>> findManualByTransactionId(List<Long> transactionIds);

}
