package mixc.be.rsms.business.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.enums.BusinessEnum;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.feignclient.AuthorServerClient;
import mixc.be.rsms.business.common.feignclient.DataServerClient;
import mixc.be.rsms.business.convert.*;
import mixc.be.rsms.business.domain.dto.*;
import mixc.be.rsms.business.domain.pojo.TbBusinessDeposit;
import mixc.be.rsms.business.domain.pojo.TbBusinessOrder;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionInfo;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionReport;
import mixc.be.rsms.business.mapper.TbBusinessDepositMapper;
import mixc.be.rsms.business.mapper.TbBusinessTransactionInfoMapper;
import mixc.be.rsms.business.mapper.TbBusinessTransactionReportMapper;
import mixc.be.rsms.business.service.IDepositService;
import mixc.be.rsms.business.service.IOrderService;
import mixc.be.rsms.business.utils.PermissionUtil;
import mixc.be.rsms.business.vo.*;
import mixc.be.rsms.common.constant.RsmsConstant;
import mixc.be.rsms.common.enums.*;
import mixc.be.rsms.common.utils.*;
import mixc.be.rsms.pojo.author.UserDetailsMicroResp;
import mixc.be.rsms.pojo.business.DepositResp;
import mixc.be.rsms.pojo.business.OrderResp;
import mixc.be.rsms.pojo.data.CustomerSourceResp;
import mixc.be.rsms.pojo.data.FileInfoResp;
import mixc.be.rsms.pojo.data.ParkingSpaceResp;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 成交意向金/订金表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
@RefreshScope
public class DepositServiceImpl extends ServiceImpl<TbBusinessDepositMapper, TbBusinessDeposit> implements IDepositService {

    private final RedissonClient redisson;
    @Value("${sf.dj.item.id}")
    private String receiveCommunityId;

    @Value("${sf.dj.item.name}")
    private String receiveCommunityName;

    private final SnowFlake snowFlake;

    private final DataParkingSpaceConvert parkingSpaceConvert;

    private final DataFileInfoConvert fileInfoConvert;

    private final DataDepositConvert depositConvert;

    private final DataTransactionInfoConvert transactionInfoConvert;

    private final TbBusinessDepositMapper depositMapper;

    private final TbBusinessTransactionInfoMapper transactionInfoMapper;

    private final TbBusinessTransactionReportMapper reportMapper;

    private final AuthorServerClient authorServerClient;

    private final FlowNumberUtil flowNumberUtil;

    private final DataServerClient dataServerClient;

    private final IOrderService orderService;
    private final DataOrderConvert orderConvert;

    @Override
    public IPage<DepositVo> page(DepositQueryParams params) {
        if (ObjectUtils.isEmpty(params)) {
            throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
        }
        if (params.getPageNum() < 1 || params.getPageSize() < 1) {
            throw new BusinessException(CommonEnum.PAGE_PARAMS_ERROR);
        }
        if (!ObjectUtils.isEmpty(params.getBrokerNameKeyWord())) {
            List<Long> brokerIds = authorServerClient.getUserIdsByUserName(params.getBrokerNameKeyWord());
            if (ObjectUtils.isEmpty(brokerIds)) {
                Page<DepositVo> depositVoPage = new Page<>(params.getPageNum(), params.getPageSize(), 0);
                depositVoPage.setRecords(List.of());
                return depositVoPage;
            }
            params.setBrokerIds(brokerIds.toArray(new Long[0]));
        }
        CompletableFuture<Map<String, List<Long>>> supplyAsync = CompletableFuture.supplyAsync(() -> {
            return PermissionUtil.syncGetUserIdByPermissionCode(List.of(
                    PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_VIEW.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_EDIT.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_DELETE.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_AUDIT_OR_REJECT.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_REVERSE_AUDIT.getCode()
            ));
        });

        List<Long> permissionUserIdList = PermissionUtil.getUserIdListByPermissionCode(List.of(
                        PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_VIEW.getCode()
                ))
                .get(PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_VIEW.getCode());
        if (ObjectUtils.isEmpty(permissionUserIdList) || permissionUserIdList.contains(PermissionIsAllValueEnum.DISABLE.getValue())) {
            throw new BusinessException(CommonEnum.NOT_PERMISSION_ERROR);
        }
        List<Long> customerIdList = null;
        if (!ObjectUtils.isEmpty(params.getCustomerKeyWord())) {
            customerIdList = dataServerClient.innerQueryCustomerByName(params.getCustomerKeyWord())
                    .stream()
                    .map(CustomerSourceResp::getId).toList();
        }
        log.info("开始执行查询.....");
        Page<TbBusinessDeposit> page = new Page<>(params.getPageNum(), params.getPageSize());
        IPage<TbBusinessDeposit> tbDataDepositPage = depositMapper.selectPages(page, params, permissionUserIdList, customerIdList);
        if (tbDataDepositPage.getTotal() == 0) {
            Page<DepositVo> depositVoPage = new Page<>(tbDataDepositPage.getCurrent(), tbDataDepositPage.getSize(), tbDataDepositPage.getTotal());
            depositVoPage.setRecords(List.of());
            return depositVoPage;
        }

        List<Long> idList = tbDataDepositPage.getRecords().stream().map(TbBusinessDeposit::getId).toList();

        // 填充实收金额
        QueryWrapper<TbBusinessTransactionInfo> transactionInfoQueryWrapper = Wrappers.query(TbBusinessTransactionInfo.class)
                .select("deposit_id, sum(transaction_amount) as transaction_amount")
                .in("deposit_id", idList)
                .eq("transaction_type", DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey())
                .groupBy("deposit_id");
        List<TbBusinessTransactionInfo> transactionInfos = transactionInfoMapper.selectList(transactionInfoQueryWrapper);
        Map<Long, BigDecimal> collect = transactionInfos
                .stream()
                .collect(Collectors.toMap(TbBusinessTransactionInfo::getDepositId, TbBusinessTransactionInfo::getTransactionAmount, (existing, next) -> existing));

        // 填充所属经纪人姓名
        Map<Long, UserDetailsMicroResp> brokerNameMap = authorServerClient.getUserDetailsMicro(tbDataDepositPage.getRecords().stream().map(TbBusinessDeposit::getBrokerId).filter(Objects::nonNull).collect(Collectors.toList()))
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(UserDetailsMicroResp::getId, Function.identity(), (existing, next) -> existing));

        // 填充客户姓名
        Map<Long, String> customerNameMap = dataServerClient.innerQueryCustomerByIdList(tbDataDepositPage.getRecords().stream().map(TbBusinessDeposit::getCustomerId).toList())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(CustomerSourceResp::getId, CustomerSourceResp::getName, (existing, next) -> existing));
        List<Long> houseIdList = tbDataDepositPage.getRecords().stream()
                .filter(item -> !ObjectUtils.isEmpty(item.getHouseDesc()))
                .map(item -> Arrays.stream(item.getHouseDesc().split(RsmsConstant.SPLIT_COMMA)).toList())
                .flatMap(Collection::stream)
                .map(Long::valueOf)
                .distinct()
                .toList();
        Map<Long, HouseInfoVo> houseInfoVoMap = dataServerClient.listHouseInfoByIds(houseIdList)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(HouseInfoVo::getId, Function.identity(), (existing, next) -> existing));
        List<Long> parkingSpaceIdList = tbDataDepositPage.getRecords().stream()
                .filter(item -> !ObjectUtils.isEmpty(item.getParkingSpaceDesc()))
                .map(item -> Arrays.stream(item.getParkingSpaceDesc().split(RsmsConstant.SPLIT_COMMA)).toList())
                .flatMap(Collection::stream)
                .map(Long::valueOf)
                .distinct()
                .toList();
        Map<Long, ParkingSpaceVo> parkingInfoVoMap = dataServerClient.getParkingSpaceByIdList(parkingSpaceIdList)
                .stream()
                .filter(Objects::nonNull)
                .map(item -> {
                    ParkingSpaceVo parkingSpaceVo = parkingSpaceConvert.resp2Vo(item);
                    parkingSpaceVo.setPictureList(fileInfoConvert.resp2Vo(item.getPictureList()));
                    return parkingSpaceVo;
                })
                .collect(Collectors.toMap(ParkingSpaceVo::getId, Function.identity(), (existing, next) -> existing));
        Map<String, List<Long>> permissionCodeMap = supplyAsync.join();
        List<DepositVo> list = tbDataDepositPage.getRecords().stream().map(item -> {
            DepositVo depositVo = depositConvert.dataToVo(item);
            depositVo.setHouseDescList(getHouseArraysByIdStr(item, houseInfoVoMap));
            depositVo.setParkingSpaceList(getParkingSpaceByIdStr(item, parkingInfoVoMap));
            depositVo.setActualReceiveAmount(collect.getOrDefault(depositVo.getId(), new BigDecimal("0.00")));
            depositVo.setCustomerName(customerNameMap.get(item.getCustomerId()));
            depositVo.setCustomerMobile(PersonDataEncryptUtil.mobileEncrypt(item.getCustomerMobile()));
            depositVo.setCustomerMobileEncode(Base64Util.encodeData(item.getCustomerMobile()));
            if (!ObjectUtils.isEmpty(brokerNameMap.get(item.getBrokerId()))) {
                UserDetailsMicroResp brokerDetail = brokerNameMap.get(item.getBrokerId());
                depositVo.setBrokerName(brokerDetail.getEmpName());
                depositVo.setBrokerMobile(PersonDataEncryptUtil.mobileEncrypt(brokerDetail.getMobile()));
                depositVo.setBrokerMobileEncode(Base64Util.encodeData(brokerDetail.getMobile()));
            }

            depositVo.setPermissions(givePermissions(permissionCodeMap, item));
            return depositVo;
        }).toList();
        Page<DepositVo> depositVoPage = new Page<>(tbDataDepositPage.getCurrent(), tbDataDepositPage.getSize(), tbDataDepositPage.getTotal());
        depositVoPage.setRecords(list);
        return depositVoPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(DepositAddParams depositAddParams) {
        return insideSaveOrUpdateDeposit(depositAddParams);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteDeposit(Long id) {
        TbBusinessDeposit dataDeposit = getById(id);
        if (RsmsConstant.DEL_FLAG_YES.equals(dataDeposit.getIsDeleted())) {
            throw new BusinessException(CommonEnum.REQUEST_OBJ_NOT_EXIST);
        }
        if (!ObjectUtils.isEmpty(dataDeposit.getAuditTime())
                || !DropdownEnum.DEPOSIT_STATUS_UNEXAMINATION.getDictKey().equals(dataDeposit.getStatus())) {
            throw new BusinessException(BusinessEnum.DEPOSIT_DELETE_ERROR);
        }
        LambdaUpdateWrapper<TbBusinessDeposit> set = Wrappers.lambdaUpdate(TbBusinessDeposit.class)
                .eq(TbBusinessDeposit::getId, dataDeposit.getId())
                .set(TbBusinessDeposit::getUpdateUser, StpUtil.getLoginIdAsLong())
                .set(TbBusinessDeposit::getIsDeleted, RsmsConstant.DEL_FLAG_YES);
        int i = depositMapper.update(set);
        if (i > 0) {
            LambdaUpdateWrapper<TbBusinessTransactionInfo> updateWrapper = Wrappers.lambdaUpdate(TbBusinessTransactionInfo.class)
                    .eq(TbBusinessTransactionInfo::getDepositId, dataDeposit.getId())
                    .set(TbBusinessTransactionInfo::getUpdateUser, StpUtil.getLoginIdAsLong())
                    .set(TbBusinessTransactionInfo::getIsDeleted, RsmsConstant.DEL_FLAG_YES);
            transactionInfoMapper.update(updateWrapper);
        }
        return i;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitDeposit(DepositAuditParams params) {
        if (ObjectUtils.isEmpty(params)) {
            throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
        }
        List<TbBusinessTransactionInfo> transactionInfos = params.getDeposit().getTransactionInfoList().stream().map(transactionInfoConvert::addParamToData).toList();
        // 检查关键参数是否输入
        TbBusinessDeposit tbBusinessDeposit = depositConvert.addParamToData(params.getDeposit());
        if (!ObjectUtils.isEmpty(params.getDeposit().getHouseDescArrays())) {
            tbBusinessDeposit.setHouseDesc(String.join(RsmsConstant.SPLIT_COMMA, params.getDeposit().getHouseDescArrays()));
        }
        if (!ObjectUtils.isEmpty(params.getDeposit().getParkingSpaceDescArrays())) {
            tbBusinessDeposit.setParkingSpaceDesc(String.join(RsmsConstant.SPLIT_COMMA, params.getDeposit().getParkingSpaceDescArrays()));
        }
        validateParam(tbBusinessDeposit, transactionInfos);
        // 如果客户手机号为空，则设置手机号为客源表手机号
        if (ObjectUtils.isEmpty(params.getDeposit().getCustomerMobile())) {
            DepositAddParams deposit = params.getDeposit();
            Optional<CustomerSourceResp> first = dataServerClient.innerQueryCustomerByIdList(List.of(deposit.getCustomerId()))
                    .stream().findFirst();
            first.ifPresent(customerSourceResp -> deposit.setCustomerMobile(customerSourceResp.getMobile()));
            params.setDeposit(deposit);
        }
        List<TransactionInfoAddParams> infoAddParams = params.getDeposit().getTransactionInfoList()
                .stream()
                .filter(info -> {
                    return DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(info.getTransactionType());
                }).toList();
        if (ObjectUtils.isEmpty(infoAddParams) || infoAddParams.size() > 1) {
            throw new BusinessException("意向金提交时仅允许存在一条收款记录", BusinessEnum.DEPOSIT_INSERT_ERROR.getCode());
        }

        // 先新增/修改，后提交
        Long depositId = insideSaveOrUpdateDeposit(params.getDeposit());

        LambdaUpdateWrapper<TbBusinessDeposit> set = Wrappers.lambdaUpdate(TbBusinessDeposit.class)
                .eq(TbBusinessDeposit::getId, depositId)
                .eq(TbBusinessDeposit::getStatus, DropdownEnum.DEPOSIT_STATUS_UNEXAMINATION.getDictKey())
                .set(TbBusinessDeposit::getUpdateUser, StpUtil.getLoginIdAsLong())
                .set(TbBusinessDeposit::getStatus, DropdownEnum.DEPOSIT_STATUS_EXAMINATIONING.getDictKey());
        depositMapper.update(set);
        return depositId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer auditOrReject(DepositAuditParams params) {
        if (ObjectUtils.isEmpty(params)) {
            throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
        }
        if (ObjectUtils.isEmpty(params.getId())) {
            throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
        }
        TbBusinessDeposit dataDeposit = getById(params.getId());
        if (!DropdownEnum.DEPOSIT_STATUS_EXAMINATIONING.getDictKey().equals(dataDeposit.getStatus())) {
            throw new BusinessException(BusinessEnum.DEPOSIT_STATUS_ERROR);
        }
        // TODO 审核/驳回 权限检查

        LambdaUpdateWrapper<TbBusinessDeposit> set = Wrappers.lambdaUpdate(TbBusinessDeposit.class)
                .eq(TbBusinessDeposit::getId, dataDeposit.getId())
                .eq(TbBusinessDeposit::getStatus, DropdownEnum.DEPOSIT_STATUS_EXAMINATIONING.getDictKey())
                .set(TbBusinessDeposit::getUpdateUser, StpUtil.getLoginIdAsLong());

        if (ObjectUtils.isEmpty(params.getPass())) {
            throw new BusinessException(BusinessEnum.AUDIT_STATUS_ERROR);
        }
        if (params.getPass()) {
            set.set(TbBusinessDeposit::getStatus, DropdownEnum.DEPOSIT_STATUS_EXAMINATIONED.getDictKey());
            set.set(TbBusinessDeposit::getAuditTime, LocalDateTime.now());
        } else {
            set.set(TbBusinessDeposit::getStatus, DropdownEnum.DEPOSIT_STATUS_UNEXAMINATION.getDictKey());
        }
        int update = depositMapper.update(set);
        // TODO 财务一体化中，每次审核通过都需要给收费系统传一次数据，全额记实收
        if (update > 0 && params.getPass()) {

            LambdaUpdateWrapper<TbBusinessTransactionInfo> updateWrapper = Wrappers.lambdaUpdate(TbBusinessTransactionInfo.class)
                    .eq(TbBusinessTransactionInfo::getDepositId, dataDeposit.getId())
                    .set(TbBusinessTransactionInfo::getUpdateUser, StpUtil.getLoginIdAsLong())
                    .set(TbBusinessTransactionInfo::getStatus, DropdownEnum.TRANSACTION_STATUS_UNMODIFIED_PASS.getDictKey());
            transactionInfoMapper.update(updateWrapper);

            // 订单数据生成、业务数据绑定订单数据
            Long orderId = postProcessAudit(dataDeposit);
            // 发起大额转账订单推送收费系统
            orderService.bigAmountTransferPush(orderId);
        }
        return update;
    }

    private Long postProcessAudit(TbBusinessDeposit dataDeposit) {
        // 生成订单（意向金这边目前只有收款订单）
        Long orderId = dataDeposit.getOrderId();
        // 订单 ID 为空，则表示未生成过订单，故做新增订单操作；否则，则做针对交易行的新增记录做订单补全操作
        LambdaQueryWrapper<TbBusinessTransactionInfo> eq = Wrappers.lambdaQuery(TbBusinessTransactionInfo.class)
                .eq(TbBusinessTransactionInfo::getDepositId, dataDeposit.getId());
        List<TbBusinessTransactionInfo> transactionInfos = transactionInfoMapper.selectList(eq);
        // 收款子订单
        List<SubOrderReceiveAddParams> orderReceiveAddParams = transactionInfos.stream()
                .filter(item -> ObjectUtils.isEmpty(item.getSubOrderId()))
                .filter(item -> DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(item.getTransactionType()))
                .map(info -> generateReceiveAddParam(dataDeposit.getType(), info.getTransactionAmount(), info.getId()))
                .toList();
        if (!ObjectUtils.isEmpty(orderId)) {
            // 退款子订单  要退款必然先收款，意味着已经存在主订单
            List<SubOrderRefundAddParams> subOrderRefundAddParamsList = new LinkedList<>();
            // 查找唯一的收款行的子订单ID
            Optional<Long> first = transactionInfos.stream()
                    .filter(item -> !ObjectUtils.isEmpty(item.getSubOrderId()))
                    .filter(item -> DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(item.getTransactionType()))
                    .filter(item -> DropdownEnum.TRANSACTION_ORDER_STATUS_PAYED.getDictKey().equals(item.getOrderStatus()))
                    .map(TbBusinessTransactionInfo::getSubOrderId)
                    .findFirst();
            if (first.isPresent()) {
                subOrderRefundAddParamsList = transactionInfos.stream()
                        .filter(item -> ObjectUtils.isEmpty(item.getSubOrderId()))
                        // 可能会存在多条退款记录，其中只会有一条是 null、 未退款、退款中、退款完成的状态，其余均为 退款失败、退款驳回的状态
                        .filter(item -> ObjectUtils.isEmpty(item.getOrderStatus()) || !List.of(
                                        DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDED_FAIL.getDictKey(),
                                        DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDED_REJECTED.getDictKey()
                                ).contains(item.getOrderStatus())
                        )
                        .filter(item -> DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey().equals(item.getTransactionType()))
                        .map(info -> {
                            return generateRefundAddParam(info.getId(),
                                    first.get(),
                                    info.getTransactionAmount(),
                                    info.getBankName(),
                                    info.getBankNum(),
                                    info.getAccountName(),
                                    info.getCity(),
                                    info.getRefundChannel());
                        }).toList();
            }
            log.info("生成的退款子订单信息:{}", JacksonUtil.toJSON(subOrderRefundAddParamsList));
            orderService.addSubOrder(orderId, orderReceiveAddParams, subOrderRefundAddParamsList, List.of());
        }else {
            OrderAddParams orderAddParams = generateOrderAddParam(dataDeposit, orderReceiveAddParams);
            if (ObjectUtils.isEmpty(orderAddParams)) {
                return null;
            }
            TbBusinessOrder save = orderService.save(orderAddParams);
            orderId = save.getId();
            // 反填 意向金表中 订单信息，修改意向金状态
            LambdaUpdateWrapper<TbBusinessDeposit> wrapper = Wrappers.lambdaUpdate(TbBusinessDeposit.class)
                    .eq(TbBusinessDeposit::getId, dataDeposit.getId())
                    .set(TbBusinessDeposit::getOrderId, save.getId())
                    .set(TbBusinessDeposit::getOrderCode, save.getOrderCode());
            depositMapper.update(wrapper);
        }
        // 针对无订单信息的业务数据 填充交易信息记录中的子订单相关字段
        List<Long> infoIdList = transactionInfos.stream()
                .filter(tbBusinessTransactionInfo -> ObjectUtils.isEmpty(tbBusinessTransactionInfo.getSubOrderId()))
                .map(TbBusinessTransactionInfo::getId)
                .toList();
        OrderVo orderVo = orderService.detail(orderId);
        Map<Long, OrderReceiveVo> businessReceiveOrderStatusMap = orderVo.getReceiveOrder()
                .stream()
                .filter(orderReceiveVo -> infoIdList.contains(orderReceiveVo.getBusinessId()))
                .collect(Collectors.toMap(OrderReceiveVo::getBusinessId, Function.identity(), (existing, next) -> existing));
        Map<Long, OrderRefundVo> businessRefundOrderStatusMap = orderVo.getRefundOrder()
                .stream()
                .filter(orderRefundVo -> infoIdList.contains(orderRefundVo.getBusinessId()))
                .collect(Collectors.toMap(OrderRefundVo::getBusinessId, Function.identity(), (existing, next) -> existing));
        List<TbBusinessTransactionInfo> updateInfoList = new LinkedList<>(businessReceiveOrderStatusMap.keySet().stream()
                .map(businessId -> {
                    OrderReceiveVo orderReceiveVo = businessReceiveOrderStatusMap.get(businessId);
                    TbBusinessTransactionInfo info = new TbBusinessTransactionInfo();
                    info.setId(businessId);
                    info.setOrderStatus(subOrderStatus2TransactionInfoOrderStatus(orderReceiveVo.getOrderStatus()));
                    info.setSubOrderId(orderReceiveVo.getId());
                    info.setSubOrderCode(orderReceiveVo.getSubOrderCode());
                    return info;
                })
                .toList());
        updateInfoList.addAll(businessRefundOrderStatusMap.keySet().stream()
                .map(businessId -> {
                    OrderRefundVo orderRefundVo = businessRefundOrderStatusMap.get(businessId);
                    TbBusinessTransactionInfo info = new TbBusinessTransactionInfo();
                    info.setId(businessId);
                    info.setOrderStatus(subOrderStatus2TransactionInfoOrderStatus(orderRefundVo.getOrderStatus()));
                    info.setSubOrderId(orderRefundVo.getId());
                    info.setSubOrderCode(orderRefundVo.getSubOrderCode());
                    return info;
                }).toList());
        transactionInfoMapper.updateById(updateInfoList);
        return orderVo.getId();
    }

    /**
     * 子订单状态转 交易信息行订单状态
     * @param orderStatus
     * @return
     */
    @Override
    public String subOrderStatus2TransactionInfoOrderStatus(String orderStatus) {
        if (DropdownEnum.ORDER_STATUS_UNPAY.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSACTION_ORDER_STATUS_UNPAY.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_PAYING.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSACTION_ORDER_STATUS_PAYING.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_PAYED.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSACTION_ORDER_STATUS_PAYED.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_UNREFUND.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSACTION_ORDER_STATUS_UNREFUND.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_REFUNDING.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDING.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_REFUND_ALL.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDED.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_REFUND_FAIL.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDING.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_REFUND_REJECTED.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDING.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_UNCONVERT.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSACTION_ORDER_STATUS_UNCOMMISSION.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_CONVERTING.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSACTION_ORDER_STATUS_COMMISSIONING.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_CONVERT.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSACTION_ORDER_STATUS_COMMISSIONED.getDictKey();
        }
        return null;
    }

    /**
     * 生成主订单
     * @param dataDeposit
     * @param orderReceiveAddParams
     * @return
     */
    private OrderAddParams generateOrderAddParam(TbBusinessDeposit dataDeposit, List<SubOrderReceiveAddParams> orderReceiveAddParams) {
        if (ObjectUtils.isEmpty(orderReceiveAddParams)) {
            return null;
        }
        OrderAddParams orderAddParams = new OrderAddParams();
        orderAddParams.setContractNum(dataDeposit.getTransactionContractNum());
        orderAddParams.setHouseIds(dataDeposit.getHouseDesc());
        orderAddParams.setParkingIds(dataDeposit.getParkingSpaceDesc());
        if (!ObjectUtils.isEmpty(dataDeposit.getHouseDesc())) {
            List<String> houseCommunityIdList = dataServerClient.listHouseInfoByIds(Arrays.stream(dataDeposit.getHouseDesc().split(RsmsConstant.SPLIT_COMMA)).map(Long::valueOf).toList())
                    .stream()
                    .map(HouseInfoVo::getCommunityId)
                    .distinct()
                    .map(String::valueOf)
                    .toList();
            orderAddParams.setCommunityIds(String.join(RsmsConstant.SPLIT_COMMA,houseCommunityIdList));
        } else if (!ObjectUtils.isEmpty(dataDeposit.getParkingSpaceDesc())) {
            List<String> parkingSpaceCommunityIdList = dataServerClient.getParkingSpaceByIdList(Arrays.stream(dataDeposit.getParkingSpaceDesc().split(RsmsConstant.SPLIT_COMMA)).map(Long::valueOf).toList())
                    .stream()
                    .map(ParkingSpaceResp::getCommunityId)
                    .distinct()
                    .map(String::valueOf)
                    .toList();
            orderAddParams.setCommunityIds(String.join(RsmsConstant.SPLIT_COMMA,parkingSpaceCommunityIdList));
        }
        Map<Long, CustomerSourceResp> customerMap = dataServerClient.innerQueryCustomerByIdList(List.of(dataDeposit.getCustomerId()))
                .stream()
                .collect(Collectors.toMap(CustomerSourceResp::getId, Function.identity(), (existing, next) -> existing));
        orderAddParams.setCustomerName(customerMap.get(dataDeposit.getCustomerId()).getName());
        orderAddParams.setCustomerMobile(dataDeposit.getCustomerMobile());
        String type = DropdownEnum.DEPOSIT_TYPE_DEPOSIT.getDictKey().equals(dataDeposit.getType()) ? DropdownEnum.ORDER_SOURCE_TYPE_DJ.getDictKey() : DropdownEnum.ORDER_SOURCE_TYPE_YXJ.getDictKey();
        orderAddParams.setTransactionType(type);
        orderAddParams.setSubOrderReceiveAddParams(orderReceiveAddParams);
        return orderAddParams;
    }
    /**
     * 生成收款子订单
     * @param depositType   意向金类型
     * @param amount    金额
     * @param businessId   业务数据ID
     * @return
     */
    private SubOrderReceiveAddParams generateReceiveAddParam(String depositType, BigDecimal amount, Long businessId) {
        SubOrderReceiveAddParams subOrderReceiveAddParams = new SubOrderReceiveAddParams();
        String goodName;
        if (DropdownEnum.DEPOSIT_TYPE_INTENTION.getDictKey().equals(depositType)) {
            goodName = DropdownEnum.DEPOSIT_TYPE_INTENTION.getDictValue();
        }else {
            goodName = DropdownEnum.DEPOSIT_TYPE_DEPOSIT.getDictValue();
        }
        subOrderReceiveAddParams.setGoodName(goodName);
        subOrderReceiveAddParams.setPayMode(DropdownEnum.ORDER_PAY_MODEL_2.getDictKey());
        subOrderReceiveAddParams.setPayAmount(amount);
        subOrderReceiveAddParams.setDiscountAmount(new BigDecimal("0.00"));
        subOrderReceiveAddParams.setBusinessId(businessId);
        // 意向金收费项目固定
        subOrderReceiveAddParams.setReceiveCommunityId(receiveCommunityId); // 收费项目ID
        subOrderReceiveAddParams.setReceiveCommunityName(receiveCommunityName); // 收费项目名称
        return subOrderReceiveAddParams;
    }

    /**
     * 生成退款子订单
     * @param businessId
     * @param receiveSubOrderId
     * @param refundAmount
     * @param bankName
     * @param bankNum
     * @param accountName
     * @param city
     * @param refundChannel
     * @return
     */
    private SubOrderRefundAddParams generateRefundAddParam(Long businessId,
                                                           Long receiveSubOrderId,
                                                           BigDecimal refundAmount,
                                                           String bankName,
                                                           String bankNum,
                                                           String accountName,
                                                           String city,
                                                           String refundChannel) {
        SubOrderRefundAddParams refundAddParams = new SubOrderRefundAddParams();
        refundAddParams.setBusinessId(businessId);
        refundAddParams.setReceiveSubOrderId(receiveSubOrderId);
        refundAddParams.setAmount(refundAmount);
        // 退款仅能通过 银行卡退款
        refundAddParams.setBankName(bankName);
        refundAddParams.setBankNum(bankNum);
        refundAddParams.setAccountName(accountName);
        refundAddParams.setCity(city);
        refundAddParams.setRefundChannel(refundChannel);
        refundAddParams.setRemark("发起退款操作...");
        return refundAddParams;
    }

    @Override
    public Integer reverseAudit(DepositAuditParams params) {
        if (ObjectUtils.isEmpty(params)) {
            throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
        }
        if (ObjectUtils.isEmpty(params.getId())) {
            throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
        }
        TbBusinessDeposit dataDeposit = getById(params.getId());
        if (!DropdownEnum.DEPOSIT_STATUS_EXAMINATIONED.getDictKey().equals(dataDeposit.getStatus())
                && !DropdownEnum.DEPOSIT_STATUS_GENERATE_ORDER.getDictKey().equals(dataDeposit.getStatus())
                && !DropdownEnum.DEPOSIT_STATUS_RECEIVED.getDictKey().equals(dataDeposit.getStatus())
                && !DropdownEnum.DEPOSIT_STATUS_REFUND.getDictKey().equals(dataDeposit.getStatus())
                && !DropdownEnum.DEPOSIT_STATUS_COMMISSION.getDictKey().equals(dataDeposit.getStatus())) {
            throw new BusinessException(BusinessEnum.DEPOSIT_STATUS_ERROR);
        }
        // TODO 反审核权限检查


        LambdaUpdateWrapper<TbBusinessDeposit> set = Wrappers.lambdaUpdate(TbBusinessDeposit.class)
                .eq(TbBusinessDeposit::getId, dataDeposit.getId())
                .in(TbBusinessDeposit::getStatus, List.of(
                        DropdownEnum.DEPOSIT_STATUS_EXAMINATIONED.getDictKey(),
                        DropdownEnum.DEPOSIT_STATUS_GENERATE_ORDER.getDictKey(),
                        DropdownEnum.DEPOSIT_STATUS_RECEIVED.getDictKey(),
                        DropdownEnum.DEPOSIT_STATUS_REFUND.getDictKey(),
                        DropdownEnum.DEPOSIT_STATUS_COMMISSION.getDictKey()
                ))
                .set(TbBusinessDeposit::getUpdateUser, StpUtil.getLoginIdAsLong())
                .set(TbBusinessDeposit::getStatus, DropdownEnum.DEPOSIT_STATUS_UNEXAMINATION.getDictKey());
        int update = depositMapper.update(set);
        // TODO 财务一体化中，每次反审核通过 都需要给 收费系统传一次数据，冲销原账务
        return update;
    }

    @Override
    public DepositVo getDeposit(Long id) {
        log.info("查询意向金详情：{}", id);
        TbBusinessDeposit dataDeposit = getById(id);
        DepositVo depositVo = depositConvert.dataToVo(dataDeposit);
        CompletableFuture<Map<String, List<Long>>> supplyAsync = CompletableFuture.supplyAsync(() -> {
            return PermissionUtil.syncGetUserIdByPermissionCode(List.of(
                    PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_VIEW.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_EDIT.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_DELETE.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_AUDIT_OR_REJECT.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_REVERSE_AUDIT.getCode()
            ));
        });
        // 填充客户姓名查询
        Map<Long, String> customerNameMap = dataServerClient.innerQueryCustomerByIdList(List.of(depositVo.getCustomerId()))
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(CustomerSourceResp::getId, CustomerSourceResp::getName, (existing, next) -> existing));
        depositVo.setCustomerName(customerNameMap.get(depositVo.getCustomerId()));
        // 手机号加密
        depositVo.setCustomerMobile(PersonDataEncryptUtil.mobileEncrypt(dataDeposit.getCustomerMobile()));
        depositVo.setCustomerMobileEncode(Base64Util.encodeData(dataDeposit.getCustomerMobile()));

        // 填充房源、项目信息
        if (!ObjectUtils.isEmpty(dataDeposit.getHouseDesc()) || !ObjectUtils.isEmpty(dataDeposit.getParkingSpaceDesc())) {
            // 车位意向金
            if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_3.getDictKey().equals(dataDeposit.getCategory())
                    || DropdownEnum.DEPOSIT_CATEGORY_DPS_C_4.getDictKey().equals(dataDeposit.getCategory())
                    || DropdownEnum.DEPOSIT_CATEGORY_DPS_C_10.getDictKey().equals(dataDeposit.getCategory())) {
                List<Long> parkingSpaceIdList = Arrays.stream(dataDeposit.getParkingSpaceDesc().split(RsmsConstant.SPLIT_COMMA)).map(Long::valueOf).toList();
                Map<Long, ParkingSpaceVo> parkingInfoVoMap = dataServerClient.getParkingSpaceByIdList(parkingSpaceIdList)
                        .stream()
                        .filter(Objects::nonNull)
                        .map(item -> {
                            ParkingSpaceVo parkingSpaceVo = parkingSpaceConvert.resp2Vo(item);
                            parkingSpaceVo.setPictureList(fileInfoConvert.resp2Vo(item.getPictureList()));
                            return parkingSpaceVo;
                        })
                        .collect(Collectors.toMap(ParkingSpaceVo::getId, Function.identity(), (existing, next) -> existing));
                depositVo.setParkingSpaceList(getParkingSpaceByIdStr(dataDeposit, parkingInfoVoMap));
            }else { // 房源意向金
                List<Long> houseIdList = Arrays.stream(dataDeposit.getHouseDesc().split(RsmsConstant.SPLIT_COMMA)).map(Long::valueOf).toList();
                Map<Long, HouseInfoVo> houseInfoVoMap = dataServerClient.listHouseInfoByIds(houseIdList)
                        .stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(HouseInfoVo::getId, Function.identity(), (existing, next) -> existing));
                depositVo.setHouseDescList(getHouseArraysByIdStr(dataDeposit, houseInfoVoMap));
            }
        }else {
            depositVo.setHouseDescList(List.of());
            depositVo.setParkingSpaceList(List.of());
        }
        // 填充所属人信息
        Optional<UserDetailsMicroResp> first = authorServerClient.getUserDetailsMicro(List.of(dataDeposit.getBrokerId()))
                .stream()
                .filter(Objects::nonNull)
                .filter(item -> item.getId().equals(dataDeposit.getBrokerId()))
                .findFirst();
        if (first.isPresent()) {
            UserDetailsMicroResp userDetailsMicroResp = first.get();
            depositVo.setBrokerName(userDetailsMicroResp.getEmpName());
            depositVo.setBrokerStoreName(userDetailsMicroResp.getStoreName());
            depositVo.setBrokerMobile(PersonDataEncryptUtil.mobileEncrypt(userDetailsMicroResp.getMobile()));
            depositVo.setBrokerMobileEncode(Base64Util.encodeData(userDetailsMicroResp.getMobile()));
        }

        // 收款金额、退款金额、佣金金额 查询
        DepositVo statistics = getAmountStatistics(dataDeposit.getId());
        if (!ObjectUtils.isEmpty(statistics)) {
            depositVo.setReceiveAmount(statistics.getReceiveAmount());
            depositVo.setRefundAmount(statistics.getRefundAmount());
            depositVo.setCommissionAmount(statistics.getCommissionAmount());
            depositVo.setBalanceAmount(statistics.getReceiveAmount().subtract(statistics.getRefundAmount()).subtract(statistics.getCommissionAmount()));
        }

        // 填充交易信息
        LambdaQueryWrapper<TbBusinessTransactionInfo> infoLambdaQueryWrapper = Wrappers.lambdaQuery(TbBusinessTransactionInfo.class)
                .eq(TbBusinessTransactionInfo::getDepositId, dataDeposit.getId())
                .orderByDesc(TbBusinessTransactionInfo::getTransactionDateTime);
        List<TbBusinessTransactionInfo> transactionInfos = transactionInfoMapper.selectList(infoLambdaQueryWrapper);
        if (!ObjectUtils.isEmpty(transactionInfos)) {
            // 填充合同附件信息
            Map<Long, DataFileInfoVo> fileInfoMap = dataServerClient.findByBusinessIdList(transactionInfos.stream().map(TbBusinessTransactionInfo::getId).toList())
                    .getData()
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(DataFileInfoVo::getBusinessId, Function.identity(), (existing, next) -> existing));

            List<TransactionInfoVo> transactionInfoVoList = transactionInfos.stream()
                    .map(item -> {
                        TransactionInfoVo transactionInfoVo = transactionInfoConvert.dataToVo(item);
                        if (!ObjectUtils.isEmpty(fileInfoMap.get(transactionInfoVo.getId()))) {
                            transactionInfoVo.setEnclosureFileId(fileInfoMap.get(transactionInfoVo.getId()).getFileId());
                            transactionInfoVo.setEnclosureFileName(fileInfoMap.get(transactionInfoVo.getId()).getFileName());
                        }
                        return transactionInfoVo;
                    }).toList();
            editCheck(depositVo, transactionInfoVoList);
            depositVo.setTransactionInfoList(transactionInfoVoList);
        }else {
            depositVo.setTransactionInfoList(List.of());
        }


        Map<String, List<Long>> permissionCodeMap = supplyAsync.join();
        depositVo.setPermissions(givePermissions(permissionCodeMap, dataDeposit));
        return depositVo;
    }

    /**
     * 返回前端是否允许编辑交易行
     * @param depositVo
     * @param transactionInfoVoList
     */
    private void editCheck(DepositVo depositVo, List<TransactionInfoVo> transactionInfoVoList) {
        for (TransactionInfoVo transactionInfoVo : transactionInfoVoList) {
            // 所有订单状态都不允许编辑交易行
            if (!ObjectUtils.isEmpty(transactionInfoVo.getOrderStatus())) {
                // 收款中、已收款的订单，关联的交易明细行不可编辑，无论意向金是什么状态
                transactionInfoVo.setEdit(false);
                continue;
            }
            // 无订单id信息的交易明细行反审核后可编辑
            if (!ObjectUtils.isEmpty(depositVo.getAuditTime()) && !ObjectUtils.isEmpty(transactionInfoVo.getSubOrderId())) {
                transactionInfoVo.setEdit(false);
                continue;
            }
            transactionInfoVo.setEdit(true);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refund(DepositRefundParam param) {
        log.info("意向金发起退款:{}", JacksonUtil.toJSON(param));
        TbBusinessDeposit deposit = depositMapper.selectById(param.getId());
        BigDecimal availableAmount = getAvailableAmount(param.getId());
        TbBusinessTransactionInfo refundTransactionInfo = generateRefundTransactionInfo(param, availableAmount);
        log.info("新增退款行交易信息：{}", JacksonUtil.toJSON(refundTransactionInfo));
        transactionInfoMapper.insert(refundTransactionInfo);

        TbBusinessTransactionInfo transactionInfo = transactionInfoMapper.selectList(Wrappers.lambdaQuery(TbBusinessTransactionInfo.class)
                        .eq(TbBusinessTransactionInfo::getDepositId, param.getId())
                        .eq(TbBusinessTransactionInfo::getTransactionType, DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey())
                        .eq(TbBusinessTransactionInfo::getOrderStatus, DropdownEnum.TRANSACTION_ORDER_STATUS_PAYED.getDictKey()))
                .getFirst();
        SubOrderRefundAddParams refundAddParams = generateRefundAddParam(refundTransactionInfo.getId(),
                transactionInfo.getSubOrderId(),
                refundTransactionInfo.getTransactionAmount(),
                param.getBankName(),
                param.getBankNum(),
                param.getAccountName(),
                param.getCity(),
                param.getRefundChannel());
        log.info("生成退款子订单信息:{}", JacksonUtil.toJSON(refundAddParams));
        orderService.addSubOrder(deposit.getOrderId(), List.of(), List.of(refundAddParams), List.of());
    }

    /**
     * 获取可用余额
     * @param depositId
     * @return
     */
    private BigDecimal getAvailableAmount(Long depositId) {
        DepositVo amountStatistics = getAmountStatistics(depositId);

        BigDecimal available = amountStatistics.getReceiveAmount().subtract(amountStatistics.getCommissionAmount()).subtract(amountStatistics.getRefundAmount());
        if (available.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(BusinessEnum.DEPOSIT_NOT_REFUNDABLE_BALANCE_ERROR);
        }
        return available;
    }

    /**
     * 生成退款交易行
     * @param param
     * @param refundAmount
     * @return
     */
    private TbBusinessTransactionInfo generateRefundTransactionInfo(DepositRefundParam param, BigDecimal refundAmount) {
        TbBusinessTransactionInfo info = new TbBusinessTransactionInfo();
        info.setId(snowFlake.nextId());
        info.setDepositId(param.getId());
        info.setTransactionAmount(refundAmount);
        info.setTransactionType(DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey());
        info.setTransactionDateTime(LocalDateTime.now());
        return info;
    }

    @Override
    public List<DepositVo> queryUnBindingTransactionsReportList(String depositCode, Long customerId, Boolean bindingTransactionReport) {
        // 只有绑定了意向金，且成交报告本身记录为 非草稿状态(非未审核) 时,才认为是绑定
        LambdaQueryWrapper<TbBusinessTransactionReport> reportLambdaQueryWrapper = Wrappers.lambdaQuery(TbBusinessTransactionReport.class)
                .isNotNull(TbBusinessTransactionReport::getDepositId);
        List<Long> bindingReportDeposit = reportMapper.selectList(reportLambdaQueryWrapper)
                .stream()
                .filter(report -> !DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey().equals(report.getStatus()))
                .map(TbBusinessTransactionReport::getDepositId)
                .toList();
        LambdaQueryWrapper<TbBusinessDeposit> depositLambdaQueryWrapper = Wrappers.lambdaQuery(TbBusinessDeposit.class)
                .like(!ObjectUtils.isEmpty(depositCode), TbBusinessDeposit::getDepositCode, depositCode)
                .in(TbBusinessDeposit::getStatus, List.of(
                        DropdownEnum.DEPOSIT_STATUS_EXAMINATIONED.getDictKey(),
                        DropdownEnum.DEPOSIT_STATUS_GENERATE_ORDER.getDictKey(),
                        DropdownEnum.DEPOSIT_STATUS_RECEIVED.getDictKey(),
                        DropdownEnum.DEPOSIT_STATUS_REFUND.getDictKey(),
                        DropdownEnum.DEPOSIT_STATUS_COMMISSION.getDictKey()
                ))
                .eq(TbBusinessDeposit::getCustomerId, customerId);
        if (bindingTransactionReport) {
            depositLambdaQueryWrapper.in(!ObjectUtils.isEmpty(bindingReportDeposit), TbBusinessDeposit::getId, bindingReportDeposit);
        }else {
            depositLambdaQueryWrapper.notIn(!ObjectUtils.isEmpty(bindingReportDeposit), TbBusinessDeposit::getId, bindingReportDeposit);
        }
        List<DepositVo> result = depositMapper.selectList(depositLambdaQueryWrapper).stream()
                .map(depositConvert::dataToVo)
                .toList();
        if (ObjectUtils.isEmpty(result)) {
            return List.of();
        }
        // 根据交易信息行的订单状态判断，意向金数据本身不更新订单状态  方便走反审核流程
        Map<Long, List<TbBusinessTransactionInfo>> infoMap = transactionInfoMapper.selectList(Wrappers.lambdaQuery(TbBusinessTransactionInfo.class)
                        .in(TbBusinessTransactionInfo::getDepositId, result.stream().map(DepositVo::getId).collect(Collectors.toList()))
                        .eq(TbBusinessTransactionInfo::getOrderStatus, DropdownEnum.TRANSACTION_ORDER_STATUS_PAYED.getDictKey())
                )
                .stream()
                .collect(Collectors.groupingBy(TbBusinessTransactionInfo::getDepositId));
        return result.stream()
                .filter(deposit -> {
                    return !ObjectUtils.isEmpty(infoMap.get(deposit.getId()));
                })
                .filter(deposit -> {
                    Optional<BigDecimal> reduce = infoMap.get(deposit.getId()).stream()
                            .map(info -> {
                                if (DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(info.getTransactionType())) {
                                    return info.getTransactionAmount();
                                } else {
                                    return info.getTransactionAmount().multiply(new BigDecimal("-1"));
                                }
                            }).reduce(BigDecimal::add);
                    return reduce.filter(decimal -> decimal.compareTo(BigDecimal.ZERO) > 0).isPresent();
                })
                .peek(depositVo -> {
                    DepositVo statistics = getAmountStatistics(depositVo.getId());
                    if (!ObjectUtils.isEmpty(statistics)) {
                        depositVo.setReceiveAmount(statistics.getReceiveAmount());
                        depositVo.setRefundAmount(statistics.getRefundAmount());
                        depositVo.setCommissionAmount(statistics.getCommissionAmount());
                        depositVo.setBalanceAmount(statistics.getReceiveAmount().subtract(statistics.getRefundAmount()).subtract(statistics.getCommissionAmount()));
                    }
                })
                .toList();
    }

    /**
     * 内部保存意向金、订金 供 保存\更新、提交调用
     * @param depositAddParams
     * @return
     */
    public Long insideSaveOrUpdateDeposit(DepositAddParams depositAddParams){
        if (ObjectUtils.isEmpty(depositAddParams)) {
            throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
        }

        TbBusinessDeposit tbBusinessDeposit = depositConvert.addParamToData(depositAddParams);
        if (!ObjectUtils.isEmpty(depositAddParams.getHouseDescArrays())) {
            tbBusinessDeposit.setHouseDesc(String.join(RsmsConstant.SPLIT_COMMA, depositAddParams.getHouseDescArrays()));
        }
        if (!ObjectUtils.isEmpty(depositAddParams.getParkingSpaceDescArrays())) {
            tbBusinessDeposit.setParkingSpaceDesc(String.join(RsmsConstant.SPLIT_COMMA, depositAddParams.getParkingSpaceDescArrays()));
        }
        if (ObjectUtils.isEmpty(depositAddParams.getId())) {
            tbBusinessDeposit.setId(snowFlake.nextId());
            tbBusinessDeposit.setDepositCode(flowNumberUtil.getFlowNumber(BusinessPrefixEnum.DEPOSIT_KEY_PREFIX.getCode(), 6));
            tbBusinessDeposit.setStatus(DropdownEnum.DEPOSIT_STATUS_UNEXAMINATION.getDictKey());
            tbBusinessDeposit.setCreateUser(StpUtil.getLoginIdAsLong());
            depositMapper.insert(tbBusinessDeposit);

            for (TransactionInfoAddParams addTransactionInfo : depositAddParams.getTransactionInfoList()) {
                TbBusinessTransactionInfo tbBusinessTransactionInfo = transactionInfoConvert.addParamToData(addTransactionInfo);
                tbBusinessTransactionInfo.setId(snowFlake.nextId());
                tbBusinessTransactionInfo.setDepositId(tbBusinessDeposit.getId());
                tbBusinessTransactionInfo.setCreateUser(StpUtil.getLoginIdAsLong());
                transactionInfoMapper.insert(tbBusinessTransactionInfo);
                if (!ObjectUtils.isEmpty(addTransactionInfo.getEnclosureFileId())) {
                    insertFileInfo(tbBusinessTransactionInfo.getId(), addTransactionInfo.getEnclosureFileId(), false);
                }
            }
            return tbBusinessDeposit.getId();
        } else {
            // 只有未审核状态才允许修改
            DepositVo deposit = getDeposit(depositAddParams.getId());
            if (!DropdownEnum.DEPOSIT_STATUS_UNEXAMINATION.getDictKey().equals(deposit.getStatus())) {
                throw new BusinessException(BusinessEnum.DEPOSIT_STATUS_ERROR);
            }
            // 前端每次给的都是全量数据，如果前端未传某个 交易信息 ，则表示该交易信息被前端做删除操作了
            // 当前数据库中的交易信息
            List<Long> existingInfoIds = new ArrayList<>(transactionInfoMapper.selectList(Wrappers.lambdaQuery(TbBusinessTransactionInfo.class).eq(TbBusinessTransactionInfo::getDepositId, deposit.getId()))
                    .stream()
                    .map(TbBusinessTransactionInfo::getId)
                    .filter(Objects::nonNull)
                    .toList());
            // 由于存在草稿状态，所以，数据库中的交易信息可能为空；不为空时，才考虑删除数据库中的对应数据
            if (!ObjectUtils.isEmpty(existingInfoIds)) {
                // 前端传送过来的交易信息ID
                List<Long> receiveInfoIds = depositAddParams.getTransactionInfoList()
                        .stream()
                        .map(TransactionInfoAddParams::getId)
                        .filter(Objects::nonNull)
                        .toList();
                // 不为空，表示只有传送过来的交易信息保留，其余数据库中的记录删除； 为空，表示删除所有数据库中的交易信息数据
                if (!ObjectUtils.isEmpty(receiveInfoIds)) {
                    existingInfoIds.removeAll(receiveInfoIds);
                }
                // 过滤完后，有未传过来的 Id 则删除，否则表示前端未作删除，把所有存在的交易信息都传过来了
                if (!ObjectUtils.isEmpty(existingInfoIds)) {
                    LambdaUpdateWrapper<TbBusinessTransactionInfo> deleteTransactionInfoWrappers = Wrappers.lambdaUpdate(TbBusinessTransactionInfo.class)
                            .eq(TbBusinessTransactionInfo::getDepositId, deposit.getId())
                            .in(TbBusinessTransactionInfo::getId,existingInfoIds)
                            .set(TbBusinessTransactionInfo::getUpdateUser, StpUtil.getLoginIdAsLong())
                            .set(TbBusinessTransactionInfo::getIsDeleted, RsmsConstant.DEL_FLAG_YES);
                    transactionInfoMapper.update(deleteTransactionInfoWrappers);
                }
            }
            Map<Long, TbBusinessTransactionInfo> existingInfoMap = new LinkedHashMap<>();
            List<Long> existingInfoIdList = depositAddParams.getTransactionInfoList()
                    .stream()
                    .map(TransactionInfoAddParams::getId)
                    .filter(Objects::nonNull)
                    .toList();
            if (!ObjectUtils.isEmpty(existingInfoIdList)) {
                existingInfoMap.putAll(transactionInfoMapper.selectBatchIds(existingInfoIdList)
                        .stream()
                        .collect(Collectors.toMap(TbBusinessTransactionInfo::getId, Function.identity(), (existing, next) -> existing)));
            }
            // 如果意向金交易信息ID 不为空，则做修改操作，否则，做新增操作
            for (TransactionInfoAddParams addTransactionInfo : depositAddParams.getTransactionInfoList()) {
                if (!ObjectUtils.isEmpty(addTransactionInfo.getId()) && !ObjectUtils.isEmpty(existingInfoMap.get(addTransactionInfo.getId()))) {
                    TbBusinessTransactionInfo tbBusinessTransactionInfo = existingInfoMap.get(addTransactionInfo.getId());
                    // 所有生成订单的交易行都不允许编辑， 无论意向金是什么状态
                    if (!ObjectUtils.isEmpty(tbBusinessTransactionInfo.getOrderStatus())) {
                        continue;
                    }
                    // 无订单id信息的交易明细行反审核后可编辑
                    if (!ObjectUtils.isEmpty(deposit.getAuditTime()) && !ObjectUtils.isEmpty(tbBusinessTransactionInfo.getSubOrderId())) {
                        continue;
                    }
                }

                TbBusinessTransactionInfo tbBusinessTransactionInfo = transactionInfoConvert.addParamToData(addTransactionInfo);
                if (!ObjectUtils.isEmpty(addTransactionInfo.getId())) {
                    LambdaUpdateWrapper<TbBusinessTransactionInfo> updateWrapper = Wrappers.lambdaUpdate(TbBusinessTransactionInfo.class)
                            .eq(TbBusinessTransactionInfo::getId, tbBusinessTransactionInfo.getId())
                            .set(TbBusinessTransactionInfo::getUpdateUser, StpUtil.getLoginIdAsLong())
                            .set(TbBusinessTransactionInfo::getTransactionDateTime, tbBusinessTransactionInfo.getTransactionDateTime())
                            .set(TbBusinessTransactionInfo::getTransactionType, tbBusinessTransactionInfo.getTransactionType())
                            .set(TbBusinessTransactionInfo::getTransactionAmount, tbBusinessTransactionInfo.getTransactionAmount())
                            .set(TbBusinessTransactionInfo::getContractNum, tbBusinessTransactionInfo.getContractNum())
                            .set(TbBusinessTransactionInfo::getRemark, tbBusinessTransactionInfo.getRemark())
                            .set(TbBusinessTransactionInfo::getStatus, addTransactionInfo.getStatus());
                    transactionInfoMapper.update(updateWrapper);
                    if (!ObjectUtils.isEmpty(addTransactionInfo.getEnclosureFileId())) {
                        insertFileInfo(tbBusinessTransactionInfo.getId(), addTransactionInfo.getEnclosureFileId(), true);
                    }
                }else {
                    tbBusinessTransactionInfo.setId(snowFlake.nextId());
                    tbBusinessTransactionInfo.setDepositId(tbBusinessDeposit.getId());
                    tbBusinessTransactionInfo.setCreateUser(StpUtil.getLoginIdAsLong());
                    transactionInfoMapper.insert(tbBusinessTransactionInfo);
                    if (!ObjectUtils.isEmpty(addTransactionInfo.getEnclosureFileId())) {
                        insertFileInfo(tbBusinessTransactionInfo.getId(), addTransactionInfo.getEnclosureFileId(), false);
                    }
                }
            }
            depositMapper.updateById(tbBusinessDeposit);
            return tbBusinessDeposit.getId();
        }
    }

    /**
     * 获取 收款、退款、佣金 统计数据
     *
     * @param id 意向金主表Id
     * @return
     */
    @Override
    public DepositVo getAmountStatistics(Long id) {
        QueryWrapper<TbBusinessTransactionInfo> transactionInfoQueryWrapper = Wrappers.query(TbBusinessTransactionInfo.class)
                .select("deposit_id",
                        "sum(case transaction_type when 't_type_01' then ifnull(transaction_amount,0) else 0 end) as receiveAmount",
                        "sum(case transaction_type when 't_type_02' then ifnull(transaction_amount,0) else 0 end) as refundAmount",
                        "sum(case transaction_type when 't_type_01' then 0 when 't_type_02' then 0 else ifnull(transaction_amount,0) end) as commissionAmount")
                .eq("deposit_id", id)
                .in("order_status", List.of(
                        DropdownEnum.TRANSACTION_ORDER_STATUS_PAYED.getDictKey(),
                        DropdownEnum.TRANSACTION_ORDER_STATUS_UNREFUND.getDictKey(),
                        DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDING.getDictKey(),
                        DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDED.getDictKey(),
                        DropdownEnum.TRANSACTION_ORDER_STATUS_UNCOMMISSION.getDictKey(),
                        DropdownEnum.TRANSACTION_ORDER_STATUS_COMMISSIONING.getDictKey(),
                        DropdownEnum.TRANSACTION_ORDER_STATUS_COMMISSIONED.getDictKey()
                ))
                .groupBy("deposit_id");
        Map<Long, TbBusinessTransactionInfo> transactionInfoMap = transactionInfoMapper.selectList(transactionInfoQueryWrapper)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(TbBusinessTransactionInfo::getDepositId, Function.identity(), (existing, next) -> existing));
        if (ObjectUtils.isEmpty(transactionInfoMap)) {
            return null;
        }
        BigDecimal receiveAmount = transactionInfoMap.getOrDefault(id, new TbBusinessTransactionInfo()).getReceiveAmount();
        BigDecimal refundAmount = transactionInfoMap.getOrDefault(id, new TbBusinessTransactionInfo()).getRefundAmount();
        BigDecimal commissionAmount = transactionInfoMap.getOrDefault(id, new TbBusinessTransactionInfo()).getCommissionAmount();
        return DepositVo.builder()
                .receiveAmount(receiveAmount)
                .refundAmount(refundAmount)
                .commissionAmount(commissionAmount)
                .build();
    }

    /**
     * 表中存储的房源id 转换为前端展示的信息
     *
     * @param dataDeposit
     * @param houseInfoVoMap
     */
    private List<HouseInfoVo> getHouseArraysByIdStr(TbBusinessDeposit dataDeposit, Map<Long, HouseInfoVo> houseInfoVoMap) {
        if (ObjectUtils.isEmpty(houseInfoVoMap) || ObjectUtils.isEmpty(dataDeposit.getHouseDesc())) {
            return null;
        }
        return Arrays.stream(dataDeposit.getHouseDesc().split(RsmsConstant.SPLIT_COMMA))
                .map(Long::valueOf)
                .map(houseInfoVoMap::get)
                .filter(Objects::nonNull)
                .toList();
    }

    private List<ParkingSpaceVo> getParkingSpaceByIdStr(TbBusinessDeposit deposit, Map<Long, ParkingSpaceVo> parkingSpaceRespMap) {
        if (ObjectUtils.isEmpty(parkingSpaceRespMap) || ObjectUtils.isEmpty(deposit.getParkingSpaceDesc())) {
            return null;
        }
        return Arrays.stream(deposit.getParkingSpaceDesc().split(RsmsConstant.SPLIT_COMMA))
                .map(Long::valueOf)
                .map(parkingSpaceRespMap::get)
                .filter(Objects::nonNull)
                .toList();
    }

    private TbBusinessDeposit getById(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
        }
        TbBusinessDeposit tbBusinessDeposit = depositMapper.selectById(id);
        if (ObjectUtils.isEmpty(tbBusinessDeposit)) {
            throw new BusinessException(CommonEnum.REQUEST_OBJ_NOT_EXIST);
        }
        return tbBusinessDeposit;
    }

    /**
     * 提交时验证参数有效性
     * @param deposit
     * @param info
     */
    private void validateParam(TbBusinessDeposit deposit, List<TbBusinessTransactionInfo> info) {
        if (ObjectUtils.isEmpty(deposit.getType())) {
            throw new BusinessException(BusinessEnum.DEPOSIT_TYPE_PARAMS_ERROR);
        }
        if (ObjectUtils.isEmpty(deposit.getCategory())) {
            throw new BusinessException(BusinessEnum.DEPOSIT_CATEGORY_PARAMS_NOT_NULL_ERROR);
        }
        if (ObjectUtils.isEmpty(deposit.getCustomerId())) {
            throw new BusinessException(BusinessEnum.DEPOSIT_CUSTOMER_PARAMS_NOT_NULL_ERROR);
        }
        if (ObjectUtils.isEmpty(info)) {
            throw new BusinessException(BusinessEnum.DEPOSIT_TRANSACTION_INFO_PARAMS_NOT_NULL_ERROR);
        }
        // 剩余金额统计
        BigDecimal balanceAmount = new BigDecimal("0.00");
        for (TbBusinessTransactionInfo i : info) {
            if (ObjectUtils.isEmpty(i.getStatus())) {
                throw new BusinessException(BusinessEnum.DEPOSIT_PARAMS_TRANSACTION_STATUS_NOT_NULL_ERROR);
            }
            if (ObjectUtils.isEmpty(i.getTransactionAmount())) {
                throw new BusinessException(BusinessEnum.DEPOSIT_PARAMS_TRANSACTION_AMOUNT_NOT_NULL_ERROR);
            }
            if (ObjectUtils.isEmpty(i.getTransactionType())) {
                throw new BusinessException(BusinessEnum.DEPOSIT_PARAMS_TRANSACTION_TYPE_NOT_NULL_ERROR);
            }
            if (ObjectUtils.isEmpty(i.getContractNum())) {
                throw new BusinessException(BusinessEnum.DEPOSIT_PARAMS_TRANSACTION_CONTRACTNUM_NOT_NULL_ERROR);
            }
            // 除收款外，其余皆是扣款
            if (DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(i.getTransactionType())) {
                balanceAmount = balanceAmount.add(i.getTransactionAmount());
            } else {
                // 退款失败、退款驳回的记录为废数据，不计入统计
                if (!ObjectUtils.isEmpty(i.getOrderStatus())
                        && List.of(
                            DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDED_FAIL.getDictKey(),
                            DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDED_REJECTED.getDictKey()
                        ).contains(i.getOrderStatus())
                ) {
                    continue;
                }
                balanceAmount = balanceAmount.subtract(i.getTransactionAmount());
            }

        }
        // 剩余金额不能小于零
        if (balanceAmount.compareTo(new BigDecimal("0.00")) < 0) {
            throw new BusinessException(BusinessEnum.DEPOSIT_PARAMS_TRANSACTION_BALANCEAMOUNT_ERROR);
        }
        String[] houseIdArrays = null;
        if (!ObjectUtils.isEmpty(deposit.getHouseDesc())) {
            houseIdArrays = deposit.getHouseDesc().split(RsmsConstant.SPLIT_COMMA);
        }
        String[] parkingSpaceIdArrays = null;
        if (!ObjectUtils.isEmpty(deposit.getParkingSpaceDesc())) {
            parkingSpaceIdArrays = deposit.getParkingSpaceDesc().split(RsmsConstant.SPLIT_COMMA);
        }
        if (!transactionInfoValidate(deposit.getCategory(), houseIdArrays, parkingSpaceIdArrays, info)) {
            throw new BusinessException(BusinessEnum.DEPOSIT_DATA_NOT_AGREEMENT_ERROR);
        }
    }

    /**
     * 交易类目与交易类型、关联房源、车位信息字段验证
     *
     * @param category         交易类目
     * @param transactionInfos 交易信息
     * @return
     */
    private Boolean transactionInfoValidate(String category, String[] houseIdArrays, String[] parkingSpaceIdArrays, List<TbBusinessTransactionInfo> transactionInfos) {
        // 如果所有的交易记录都是 收款或者退款的 则跳过验证
        boolean allReceiveOrAllRefund = transactionInfos.stream().allMatch(item -> {
            return DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(item.getTransactionType())
                    || DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey().equals(item.getTransactionType())
                    || DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
        });
        if (allReceiveOrAllRefund) {
            return true;
        }

        // 类目为 住宅二手房买卖 时，交易类型只能为 收款、退款、住宅二手房买卖居间服务费、金融机构居间服务费
        if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_1.getDictKey().equals(category)) {
            boolean transactionTypeMatch = transactionInfos.stream().noneMatch(item -> {
                return !DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_01.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
            });
            // 存在 住宅二手房买卖居间服务费、金融机构居间服务费 的交易类型，则关联房源不能为空
            List<TbBusinessTransactionInfo> list = transactionInfos.stream()
                    .filter(item -> {
                        return DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_01.getDictKey().equals(item.getTransactionType())
                                || DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
                    }).toList();
            return transactionTypeMatch && (list.isEmpty() || !ObjectUtils.isEmpty(houseIdArrays));
        }
        // 类目为 住宅二手房租赁 时，交易类型只能为 收款、退款、住宅二手房租赁居间服务费、金融机构居间服务费
        if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_2.getDictKey().equals(category)) {
            boolean transactionTypeMatch = transactionInfos.stream().noneMatch(item -> {
                return !DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_02.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
            });
            // 存在 住宅二手房租赁居间服务费、金融机构居间服务费 的交易类型，则关联房源不能为空
            List<TbBusinessTransactionInfo> list = transactionInfos.stream()
                    .filter(item -> {
                        return DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_02.getDictKey().equals(item.getTransactionType())
                                || DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
                    }).toList();
            return transactionTypeMatch && (list.isEmpty() || !ObjectUtils.isEmpty(houseIdArrays));
        }
        // 类目为 车位二手房买卖 时，交易类型只能为 收款、退款、车位二手房买卖居间服务费、金融机构居间服务费
        if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_3.getDictKey().equals(category)) {
            boolean transactionTypeMatch = transactionInfos.stream().noneMatch(item -> {
                return !DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_03.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
            });
            // 存在 车位二手房买卖居间服务费、金融机构居间服务费 的交易类型，则关联车位不能为空
            List<TbBusinessTransactionInfo> list = transactionInfos.stream()
                    .filter(item -> {
                        return DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_03.getDictKey().equals(item.getTransactionType())
                                || DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
                    }).toList();
            return transactionTypeMatch && (list.isEmpty() || !ObjectUtils.isEmpty(parkingSpaceIdArrays));
        }
        // 类目为 车位二手房租赁 时，交易类型只能为 收款、退款、车位二手房租赁居间服务费、金融机构居间服务费
        if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_4.getDictKey().equals(category)) {
            boolean transactionTypeMatch = transactionInfos.stream().noneMatch(item -> {
                return !DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_04.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
            });
            // 存在 车位二手房租赁居间服务费、金融机构居间服务费 的交易类型，则关联车位不能为空
            List<TbBusinessTransactionInfo> list = transactionInfos.stream()
                    .filter(item -> {
                        return DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_04.getDictKey().equals(item.getTransactionType())
                                || DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
                    }).toList();
            return transactionTypeMatch && (list.isEmpty() || !ObjectUtils.isEmpty(parkingSpaceIdArrays));
        }
        // 类目为 商铺二手房租赁 时，交易类型只能为 收款、退款、商铺二手房租赁居间服务费、金融机构居间服务费
        if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_5.getDictKey().equals(category)) {
            boolean transactionTypeMatch = transactionInfos.stream().noneMatch(item -> {
                return !DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_06.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
            });
            // 存在 商铺二手房租赁居间服务费、金融机构居间服务费 的交易类型，则关联房源不能为空
            List<TbBusinessTransactionInfo> list = transactionInfos.stream()
                    .filter(item -> {
                        return DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_06.getDictKey().equals(item.getTransactionType())
                                || DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
                    }).toList();
            return transactionTypeMatch && (list.isEmpty() || !ObjectUtils.isEmpty(houseIdArrays));
        }
        // 类目为 商铺二手房买卖 时，交易类型只能为 收款、退款、商铺二手房买卖居间服务费、金融机构居间服务费
        if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_6.getDictKey().equals(category)) {
            boolean transactionTypeMatch = transactionInfos.stream().noneMatch(item -> {
                return !DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_07.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
            });
            // 存在 商铺二手房买卖居间服务费、金融机构居间服务费 的交易类型，则关联房源不能为空
            List<TbBusinessTransactionInfo> list = transactionInfos.stream()
                    .filter(item -> {
                        return DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_07.getDictKey().equals(item.getTransactionType())
                                || DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
                    }).toList();
            return transactionTypeMatch && (list.isEmpty() || !ObjectUtils.isEmpty(houseIdArrays));
        }
        // 类目为 写字楼二手房租赁 时，交易类型只能为 收款、退款、写字楼二手房租赁居间服务费、金融机构居间服务费
        if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_7.getDictKey().equals(category)) {
            boolean transactionTypeMatch = transactionInfos.stream().noneMatch(item -> {
                return !DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_08.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
            });
            // 存在 写字楼二手房租赁居间服务费、金融机构居间服务费 的交易类型，则关联房源不能为空
            List<TbBusinessTransactionInfo> list = transactionInfos.stream()
                    .filter(item -> {
                        return DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_08.getDictKey().equals(item.getTransactionType())
                                || DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
                    }).toList();
            return transactionTypeMatch && (list.isEmpty() || !ObjectUtils.isEmpty(houseIdArrays));
        }
        // 类目为 写字楼二手房买卖 时，交易类型只能为 收款、退款、写字楼二手房买卖居间服务费、金融机构居间服务费
        if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_8.getDictKey().equals(category)) {
            boolean transactionTypeMatch = transactionInfos.stream().noneMatch(item -> {
                return !DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_09.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
            });
            // 存在 写字楼二手房买卖居间服务费、金融机构居间服务费 的交易类型，则关联房源不能为空
            List<TbBusinessTransactionInfo> list = transactionInfos.stream()
                    .filter(item -> {
                        return DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_09.getDictKey().equals(item.getTransactionType())
                                || DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
                    }).toList();
            return transactionTypeMatch && (list.isEmpty() || !ObjectUtils.isEmpty(houseIdArrays));
        }
        // 类目为 车位新盘买卖 时，交易类型只能为 收款、退款、车位新盘分销居间服务费、金融机构居间服务费
        if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_10.getDictKey().equals(category)) {
            return transactionInfos.stream().noneMatch(item -> {
                return !DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_10.getDictKey().equals(item.getTransactionType())
                        && !DropdownEnum.TRANSACTION_TYPE_T_TYPE_03_05.getDictKey().equals(item.getTransactionType());
            });
        }
        return false;
    }

    /**
     * 新增文件业务表记录
     *
     * @param businessId        业务主键
     * @param fileId            文件Id
     * @param removeHistoryInfo 是否删除业务主键关联的其他记录
     */
    private void insertFileInfo(Long businessId, Long fileId, Boolean removeHistoryInfo) {
        if (ObjectUtils.isEmpty(fileId)) {
            return;
        }
        if (removeHistoryInfo) {
            dataServerClient.deleteByBusinessIdList(List.of(businessId));
        }
        FileInfoResp fileInfo = new FileInfoResp();

        fileInfo.setBusinessId(businessId);
        fileInfo.setFileId(fileId);
        fileInfo.setBusinessType(DropdownEnum.FILE_BUSINESS_TYPE_DEPOSIT.getDictKey());
        fileInfo.setId(snowFlake.nextId());
        dataServerClient.innerBatchSave(List.of(fileInfo));
    }

    /**
     * 填充权限码
     * @param permissionCodeMap
     * @param deposit
     * @return
     */
    private List<String> givePermissions(Map<String, List<Long>> permissionCodeMap, TbBusinessDeposit deposit) {
        Set<String> permissions = new HashSet<>();
        if (ObjectUtils.isEmpty(deposit.getHouseDesc()) && ObjectUtils.isEmpty(deposit.getParkingSpaceDesc())) {
            return new ArrayList<>(permissions);
        }
        permissionCodeMap.keySet()
                .forEach(key -> {
                    List<Long> permissionUserIdList = permissionCodeMap.getOrDefault(key, List.of());
                    Stream.of(
                                    PermissionIsAllValueEnum.ALL.getValue(),
                                    deposit.getBrokerId()
                            )
                            .filter(permissionUserIdList::contains)
                            .findAny()
                            .ifPresent(permission -> {
                                        permissions.add(key);
                                    }
                            );
                });

        // 根据状态不同，删除对应权限
        // 已审核的意向金，列表和详情都需要控制按钮-通过、驳回不展示。列表按钮为详情、反审核；详情只有反审核按钮
        //未审核的意向金，列表只有详情、编辑、删除操作。详情里只有编辑和删除。
        //审核中的意向金，列表只有详情、通过、驳回操作，详情里只有通过和驳回按钮
        if (DropdownEnum.DEPOSIT_STATUS_UNEXAMINATION.getDictKey().equals(deposit.getStatus())) {
            // 未审核状态，删除审核、驳回权限  删除 反审核权限
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_AUDIT_OR_REJECT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_REVERSE_AUDIT.getCode());
        }else if (DropdownEnum.DEPOSIT_STATUS_EXAMINATIONING.getDictKey().equals(deposit.getStatus())) {
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_REVERSE_AUDIT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_EDIT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_DELETE.getCode());
        } else if (DropdownEnum.DEPOSIT_STATUS_EXAMINATIONED.getDictKey().equals(deposit.getStatus())
                || DropdownEnum.DEPOSIT_STATUS_GENERATE_ORDER.getDictKey().equals(deposit.getStatus())
                || DropdownEnum.DEPOSIT_STATUS_RECEIVED.getDictKey().equals(deposit.getStatus())
                || DropdownEnum.DEPOSIT_STATUS_REFUND.getDictKey().equals(deposit.getStatus())
                || DropdownEnum.DEPOSIT_STATUS_COMMISSION.getDictKey().equals(deposit.getStatus())) {
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_EDIT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_AUDIT_OR_REJECT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_DEPOSIT_DELETE.getCode());
        }
        return new ArrayList<>(permissions);
    }

    @Override
    public List<DepositResp> queryDepositByCustomerId(List<Long> customerIds) {
        if (!ObjectUtils.isEmpty(customerIds)) {
            List<TbBusinessDeposit> tbBusinessDeposits = depositMapper.selectList(Wrappers.lambdaQuery(TbBusinessDeposit.class)
                    .in(TbBusinessDeposit::getCustomerId, customerIds));
            return depositConvert.data2RespList(tbBusinessDeposits);
        }
        return List.of();
    }

    @Override
    public List<DepositResp> queryDepositById(List<Long> ids) {
        if (!ObjectUtils.isEmpty(ids)) {
            List<TbBusinessDeposit> tbBusinessDeposits = depositMapper.selectList(Wrappers.lambdaQuery(TbBusinessDeposit.class)
                    .in(TbBusinessDeposit::getId, ids));
            return depositConvert.data2RespList(tbBusinessDeposits);
        }
        return List.of();
    }

    @Override
    public List<DepositResp> queryDepositByAssetsId(List<Long> assetsIds) {
        if (!ObjectUtils.isEmpty(assetsIds)) {
            LambdaQueryWrapper<TbBusinessDeposit> query = Wrappers.lambdaQuery(TbBusinessDeposit.class);
            query.nested(i -> {
                        for (Long houseId : assetsIds) {
                            i.like(TbBusinessDeposit::getHouseDesc, houseId)
                                    .or();
                        }
                        i.eq(TbBusinessDeposit::getId, 0);
                    })
                    .or()
                    .nested(i -> {
                        for (Long parkingSpaceId : assetsIds) {
                            i.like(TbBusinessDeposit::getParkingSpaceDesc, parkingSpaceId)
                                    .or();
                        }
                        i.eq(TbBusinessDeposit::getId, 0);
                    });
            List<TbBusinessDeposit> tbBusinessDeposits = depositMapper.selectList(query);
            return depositConvert.data2RespList(tbBusinessDeposits);
        }
        return List.of();
    }

    @Override
    public List<DepositResp> queryDepositByAssetsIdAndCustomerMobile(Long assetsId, String customerMobile) {
        if (ObjectUtils.isEmpty(assetsId) && ObjectUtils.isEmpty(customerMobile)) {
            return List.of();
        }
        LambdaQueryWrapper<TbBusinessDeposit> like = Wrappers.lambdaQuery(TbBusinessDeposit.class)
                .nested(i -> {
                    i.like(!ObjectUtils.isEmpty(assetsId), TbBusinessDeposit::getHouseDesc, assetsId)
                            .or()
                            .like(!ObjectUtils.isEmpty(assetsId), TbBusinessDeposit::getParkingSpaceDesc, assetsId);
                })
                .like(!ObjectUtils.isEmpty(customerMobile), TbBusinessDeposit::getCustomerMobile, customerMobile)
                .orderByDesc(TbBusinessDeposit::getCreateTime);
        List<TbBusinessDeposit> tbBusinessDeposits = depositMapper.selectList(like);
        Map<Long, OrderResp> orderIdMap = tbBusinessDeposits.stream()
                .map(TbBusinessDeposit::getOrderId)
                .filter(Objects::nonNull)
                .distinct()
                .map(orderService::detail)
                .map(orderConvert::vo2Resp)
                .collect(Collectors.toMap(OrderResp::getId, Function.identity(), (existing, next) -> existing));

        return tbBusinessDeposits.stream()
                .filter(deposit -> !ObjectUtils.isEmpty(deposit.getOrderId()))
                .map(item -> {
                    DepositResp depositResp = depositConvert.data2Resp(item);
                    depositResp.setOrder(orderIdMap.get(item.getOrderId()));
                    return depositResp;
                })
                .toList();
    }

}
