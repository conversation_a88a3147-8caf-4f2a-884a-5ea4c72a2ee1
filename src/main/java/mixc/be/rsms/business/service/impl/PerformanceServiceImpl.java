package mixc.be.rsms.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.domain.dto.CommissionPlanDto;
import mixc.be.rsms.business.domain.pojo.*;
import mixc.be.rsms.business.mapper.TbBusinessPerformanceMapper;
import mixc.be.rsms.business.service.*;
import mixc.be.rsms.common.enums.DropdownEnum;
import mixc.be.rsms.common.utils.FlowNumberUtil;
import mixc.be.rsms.common.utils.SnowFlake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 业绩主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class PerformanceServiceImpl extends ServiceImpl<TbBusinessPerformanceMapper, TbBusinessPerformance> implements IPerformanceService {

    private final ITransactionReportService transactionReportService;

    private final IReceiveCommissionPlanService receiveCommissionPlanService;

    private final ITransactionAutoService transactionAutoService;

    private final ITransactionManualService transactionManualService;

    private final SnowFlake snowFlake;

    private final IPerformanceItemService performanceItemService;

    private final FlowNumberUtil flowNumberUtil;

    /**
     * 生成业务业绩
     */
    @Override
    public void generatePerformance() {
        //查询成交报告数据
        List<TbBusinessTransactionReport> tbBusinessTransactionReports = transactionReportService.queryTransactionReportBySettleStatus();
        if (CollectionUtils.isEmpty(tbBusinessTransactionReports)) {
            return;
        }
        log.info("Generate Performance tbBusinessTransactionReports size={}", tbBusinessTransactionReports.size());
        generatePerformanceByTransactionReport(tbBusinessTransactionReports);
    }

    @Override
    public void generatePerformanceByTransactionReport(List<TbBusinessTransactionReport> transactionReports) {
        List<Long> transactionIds = transactionReports.stream().map(TbBusinessTransactionReport::getId).toList();
        //查询收佣计划数据
        Map<Long, CommissionPlanDto> receivedCommissionByReportId = receiveCommissionPlanService.getReceivedCommissionByReportId(transactionIds);
        //查询自动分配数据
        Map<Long, List<TbBusinessTransactionAuto>> autoByTransactionId = transactionAutoService.findAutoByTransactionId(transactionIds);
        //查询手动分配数据
        Map<Long, List<TbBusinessTransactionManual>> manualByTransactionId = transactionManualService.findManualByTransactionId(transactionIds);
        //遍历成交报告生成业务业绩-签约业绩
        generateSignPerformance(transactionReports, receivedCommissionByReportId, autoByTransactionId, manualByTransactionId);
    }

    //生成签约业绩
    private void generateSignPerformance(List<TbBusinessTransactionReport> transactionReports,
                                         Map<Long, CommissionPlanDto> receivedCommissionByReportId,
                                         Map<Long, List<TbBusinessTransactionAuto>> autoByTransactionId,
                                         Map<Long, List<TbBusinessTransactionManual>> manualByTransactionId) {
        //构建业绩数据
        List<TbBusinessPerformance> performanceList = new ArrayList<>();
        List<TbBusinessPerformanceItem> performanceItemList = new ArrayList<>();
        transactionReports.forEach(transactionReport -> {
            //判断是手动还是自动
            if (DropdownEnum.ALLOCATION_FLAG_AUTO.getDictKey().equals(transactionReport.getAllocationFlag())) {
                //自动
                autoByTransactionId.get(transactionReport.getId()).forEach(auto -> {
                    
                });
            }
            if (DropdownEnum.ALLOCATION_FLAG_MANUAL.getDictKey().equals(transactionReport.getAllocationFlag())) {
                //手动
            }
            //构建业绩主表数据
            TbBusinessPerformance performance = new TbBusinessPerformance();
            performance.setId(snowFlake.nextId());
            performance.setTransactionId(transactionReport.getId());
            performance.setPerformanceCode(flowNumberUtil.getFlowNumber("YJ", 4));
            performance.setUserId(transactionReport.getDealMakerId());
            performance.setStoreId();
            performance.setPerformancePeriod();
            performance.setPerformanceType(DropdownEnum.PERFORMANCE_TYPE_SIGN.getDictKey());
            performance.setPerformanceStage(DropdownEnum.PERFORMANCE_STAGE_SIGN.getDictKey());
            performance.setPerformanceTime();
            performanceList.add(performance);
            //构建业绩明细数据
            buildPerformanceItem(performance, transactionReport, receivedCommissionByReportId, autoByTransactionId, manualByTransactionId, performanceItemList);
        });
        //保存业绩数据
        if (!CollectionUtils.isEmpty(performanceList)) {
            saveBatch(performanceList);
        }
        if (!CollectionUtils.isEmpty(performanceItemList)) {
            performanceItemService.saveBatch(performanceItemList);
        }

    }

    private void buildPerformanceItem(TbBusinessPerformance performance, TbBusinessTransactionReport transactionReport, Map<Long, CommissionPlanDto> receivedCommissionByReportId, Map<Long, List<TbBusinessTransactionAuto>> autoByTransactionId, Map<Long, List<TbBusinessTransactionManual>> manualByTransactionId, List<TbBusinessPerformanceItem> performanceItemList) {

    }



    //生成实收业绩
    private void generateReceivedPerformance() {

    }

    //生成核算业绩
    private void generateCalculatePerformance() {

    }








}
