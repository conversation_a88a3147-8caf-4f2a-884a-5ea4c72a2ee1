package mixc.be.rsms.business.service;

import mixc.be.rsms.business.domain.dto.ContractSignAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessContractResidentRent;

/**
 * @ClassName IBizContractResidentRentService
 * @Description 房屋租赁居间服务合同（住宅）业务接口
 * <AUTHOR>
 * @date 2024-12-16
 */
public interface IBizContractResidentRentService {

	/**
	 * 保存房屋租赁居间服务合同（住宅）扩展信息
	 * @param addParams 合同参数信息
	 * @param contractId 合同ID
	 * @return
	 */
	void addContractSignExtendInfo(ContractSignAddParams addParams, Long contractId);
	
	/**
	 * 更新合同扩展信息
	 * @param updateParams 更新参数
	 */
	void updateContract(ContractSignAddParams updateParams);
	
	/**
	 * 房屋租赁居间合同详情扩展信息查询
	 * @param contractId 合同ID
	 * @return
	 */
	TbBusinessContractResidentRent residentRentDetail(Long contractId);
	
	/**
	 * 合同删除
	 * @param contractId 合同ID
	 */
	void deleteContract(Long contractId); 
}
