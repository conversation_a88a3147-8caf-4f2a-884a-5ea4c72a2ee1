package mixc.be.rsms.business.service.impl;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.service.IBizContractSignParamService;
import mixc.be.rsms.business.vo.ContractDetailVo;
import mixc.be.rsms.business.vo.DataFileInfoVo;
import mixc.be.rsms.common.enums.DropdownEnum;
import mixc.be.rsms.common.utils.Base64Util;
import mixc.be.rsms.pojo.third.JoyCompanyParam;
import mixc.be.rsms.pojo.third.JoyContract;
import mixc.be.rsms.pojo.third.JoyContractSaveTemplate;
import mixc.be.rsms.pojo.third.JoyCreateContractParam;
import mixc.be.rsms.pojo.third.JoyCustomerParam;
import mixc.be.rsms.pojo.third.JoySignObjectParam;

/**
 * @ClassName BizContractSignParamServiceImpl
 * @Desription 合同签署参数业务实现类
 * <AUTHOR>
 * @date 2025-02-18
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class BizContractSignParamServiceImpl implements IBizContractSignParamService {
	
	@Override
	public List<JoySignObjectParam> assembleContractSignParam(ContractDetailVo contract, JoyCreateContractParam joyContractParam) {
		List<JoySignObjectParam> joySignObjectParam = new ArrayList<>();
		if (DropdownEnum.CONTRACT_TYPE_HOUSE_RENT_RESIDENT.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RENT_COMMERCIAL.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_OWNERSHIP.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_RENT.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_DELEGATE_HANDLER_REAL_ESTATE.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENTIAL_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_COMMERCIAL_RENTIAL_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_TRIPARTITE_TERMINATION_AGREEMENT_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENTIAL_SZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_COMMERCIAL_RENTIAL_SZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_RENT_SZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_SZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_WH.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())) { //甲方、乙方、丙方
			//甲方
			JoySignObjectParam signAParam = new JoySignObjectParam();
			signAParam.setSignParty("firstParty");
			signAParam.setType("PERSONAL");
			JoyCustomerParam customerAParam = new JoyCustomerParam();
			if (!ObjectUtils.isEmpty(contract.getContractPartAVo().getLeaserProxyUserName())) {	//优先传甲方代理人
				String leaserProxyUserMobile = Base64Util.decodeData(contract.getContractPartAVo().getLeaserProxyUserMobileEncode());
				customerAParam.setId(leaserProxyUserMobile);
				customerAParam.setMobile(leaserProxyUserMobile);
				customerAParam.setName(contract.getContractPartAVo().getLeaserProxyUserName());
				customerAParam.setCertificateNumber(contract.getContractPartAVo().getLeaserProxyUserIdNumber());
			} else {
				String leaserUserMobile = Base64Util.decodeData(contract.getContractPartAVo().getLeaserMobileEncode());
				customerAParam.setId(leaserUserMobile);
				customerAParam.setMobile(leaserUserMobile);
				customerAParam.setName(contract.getContractPartAVo().getLeaserUserName());
				customerAParam.setCertificateNumber(contract.getContractPartAVo().getIdentityNumber());
			}
			signAParam.setCustomerList(List.of(customerAParam));
			joySignObjectParam.add(signAParam);
			//乙方
			JoySignObjectParam signBParam = new JoySignObjectParam();
			signBParam.setSignParty("secondParty");
			signBParam.setType("PERSONAL");
			JoyCustomerParam customerBParam = new JoyCustomerParam();
			if (!ObjectUtils.isEmpty(contract.getContractPartBVo().getCustomerProxyUserName())) {	//优先传乙方代理人
				String customerMobile = Base64Util.decodeData(contract.getContractPartBVo().getCustomerProxyUserMobileEncode());
				customerBParam.setId(customerMobile);
				customerBParam.setMobile(customerMobile);
				customerBParam.setName(contract.getContractPartBVo().getCustomerProxyUserName());
				customerBParam.setCertificateNumber(contract.getContractPartBVo().getCustomerProxyUserIdNumber());
			} else {
				String customerMobile = Base64Util.decodeData(contract.getContractPartBVo().getCustomerMobileEncode());
				customerBParam.setId(customerMobile);
				customerBParam.setMobile(customerMobile);
				customerBParam.setName(contract.getContractPartBVo().getCustomerName());
				customerBParam.setCertificateNumber(contract.getContractPartBVo().getIdentityNumber());
			}
			signBParam.setCustomerList(List.of(customerBParam));
			joySignObjectParam.add(signBParam);
			//公司
			JoySignObjectParam signCompanyParam = new JoySignObjectParam();
			signCompanyParam.setActionOrder(1);
			signCompanyParam.setSignParty("thirdParty");
			signCompanyParam.setType("CORPORATE");
			JoyCompanyParam company = new JoyCompanyParam();
			company.setCompanyName(contract.getContractBaseInfoVo().getIntermediaryCompanyName());
			company.setAutoSign(true);
			signCompanyParam.setCompany(company);
			joySignObjectParam.add(signCompanyParam);
		} else if (DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_SERVICE_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_SERVICE_SZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENTIAL_GZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_COMMERCIAL_RENTIAL_GZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_SERVICE_GZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_LEASE_CONTRACT_NEW_SZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())) {	//甲方、乙方
			//甲乙方合同设置发起方
			joyContractParam.setTenantName(
					contract.getContractBaseInfoVo() == null ? null : contract.getContractBaseInfoVo().getIntermediaryCompanyName());
			//甲方
			JoySignObjectParam signAParam = new JoySignObjectParam();
			signAParam.setSignParty("firstParty");
			signAParam.setType("PERSONAL");
			JoyCustomerParam customerAParam = new JoyCustomerParam();
			customerAParam.setId(Base64Util.decodeData(contract.getContractPartAVo().getLeaserMobileEncode()));
			customerAParam.setMobile(Base64Util.decodeData(contract.getContractPartAVo().getLeaserMobileEncode()));
			customerAParam.setName(contract.getContractPartAVo().getLeaserUserName());
			customerAParam.setCertificateNumber(contract.getContractPartAVo().getIdentityNumber());
			signAParam.setCustomerList(List.of(customerAParam));
			joySignObjectParam.add(signAParam);
			//乙方
			JoySignObjectParam signBParam = new JoySignObjectParam();
			signBParam.setSignParty("secondParty");
			signBParam.setType("PERSONAL");
			JoyCustomerParam customerBParam = new JoyCustomerParam();
			customerBParam.setId(Base64Util.decodeData(contract.getContractPartBVo().getCustomerMobileEncode()));
			customerBParam.setMobile(Base64Util.decodeData(contract.getContractPartBVo().getCustomerMobileEncode()));
			customerBParam.setName(contract.getContractPartBVo().getCustomerName());
			customerBParam.setCertificateNumber(contract.getContractPartBVo().getIdentityNumber());
			signBParam.setCustomerList(List.of(customerBParam));
			joySignObjectParam.add(signBParam);
		} else if (DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENT_SELL_REAL_ESTATE_AGENCY.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_DELEGATE_HANDLER_BANK_LOAN.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_INTERMEDIARY_SERVICE_FEE_CONFIRM_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_EARNEST_MONEY_TO_INTERMEDIARY_SERVICE_FEE_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_LOOK_BUY_RENT_REAL_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENT_SELL_REAL_ESTATE_AGENCY_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())) { //甲方、居间方
			//甲方
			JoySignObjectParam signAParam = new JoySignObjectParam();
			signAParam.setSignParty("firstParty");
			signAParam.setType("PERSONAL");
			JoyCustomerParam customerAParam = new JoyCustomerParam();
			customerAParam.setId(Base64Util.decodeData(contract.getContractPartAVo().getLeaserMobileEncode()));
			customerAParam.setMobile(Base64Util.decodeData(contract.getContractPartAVo().getLeaserMobileEncode()));
			customerAParam.setName(contract.getContractPartAVo().getLeaserUserName());
			customerAParam.setCertificateNumber(contract.getContractPartAVo().getIdentityNumber());
			signAParam.setCustomerList(List.of(customerAParam));
			joySignObjectParam.add(signAParam);
			//公司
			JoySignObjectParam signCompanyParam = new JoySignObjectParam();
			signCompanyParam.setActionOrder(1);
			signCompanyParam.setSignParty("secondParty");
			signCompanyParam.setType("CORPORATE");
			JoyCompanyParam company = new JoyCompanyParam();
			company.setCompanyName(contract.getContractBaseInfoVo().getIntermediaryCompanyName());
			company.setAutoSign(true);
			signCompanyParam.setCompany(company);
			joySignObjectParam.add(signCompanyParam);
		} else if (DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_LOOK_BUY_RENT_SELL_REAL_ESTATE_AGENCY.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())) { //乙方、居间方
			//乙方
			JoySignObjectParam signBParam = new JoySignObjectParam();
			signBParam.setSignParty("firstParty");
			signBParam.setType("PERSONAL");
			JoyCustomerParam customerBParam = new JoyCustomerParam();
			customerBParam.setId(Base64Util.decodeData(contract.getContractPartBVo().getCustomerMobileEncode()));
			customerBParam.setMobile(Base64Util.decodeData(contract.getContractPartBVo().getCustomerMobileEncode()));
			customerBParam.setName(contract.getContractPartBVo().getCustomerName());
			customerBParam.setCertificateNumber(contract.getContractPartBVo().getIdentityNumber());
			signBParam.setCustomerList(List.of(customerBParam));
			joySignObjectParam.add(signBParam);
			//公司
			JoySignObjectParam signCompanyParam = new JoySignObjectParam();
			signCompanyParam.setActionOrder(1);
			signCompanyParam.setSignParty("secondParty");
			signCompanyParam.setType("CORPORATE");
			JoyCompanyParam company = new JoyCompanyParam();
			company.setCompanyName(contract.getContractBaseInfoVo().getIntermediaryCompanyName());
			company.setAutoSign(true);
			signCompanyParam.setCompany(company);
			joySignObjectParam.add(signCompanyParam);
		}
		return joySignObjectParam;
	}

	@Override
	public List<JoyContract> assembleTemplateParam(ContractDetailVo contract) {
		List<JoyContract> contractTemplateParamList = new ArrayList<>();
		JoyContract jc = new JoyContract();
		List<JoyContractSaveTemplate> templateParamList = new ArrayList<>();
		//设置模版基础信息参数
		addTemplateBaseInfoParam(contract, templateParamList);
		//设置居间方公司信息参数
		if (DropdownEnum.CONTRACT_TYPE_HOUSE_RENT_RESIDENT.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RENT_COMMERCIAL.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_OWNERSHIP.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_RENT.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_DELEGATE_HANDLER_REAL_ESTATE.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENTIAL_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_COMMERCIAL_RENTIAL_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_TRIPARTITE_TERMINATION_AGREEMENT_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENTIAL_SZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_COMMERCIAL_RENTIAL_SZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_RENT_SZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_SZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_WH.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENT_SELL_REAL_ESTATE_AGENCY.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_DELEGATE_HANDLER_BANK_LOAN.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_INTERMEDIARY_SERVICE_FEE_CONFIRM_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_EARNEST_MONEY_TO_INTERMEDIARY_SERVICE_FEE_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_LOOK_BUY_RENT_REAL_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENT_SELL_REAL_ESTATE_AGENCY_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_LOOK_BUY_RENT_REAL_CD.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())) {
			JoyContractSaveTemplate middleCompanyParam = new JoyContractSaveTemplate();
			middleCompanyParam.setRequired(1);
			middleCompanyParam.setKey("customerIntermediaryCompany");
			middleCompanyParam.setValue(contract.getContractBaseInfoVo().getIntermediaryCompanyName());
			templateParamList.add(middleCompanyParam);
		}
		//设置合同模版甲方参数信息
		if (!ObjectUtils.isEmpty(contract.getContractPartAVo()) 
				&& !ObjectUtils.isEmpty(contract.getContractPartAVo().getLeaserUserName())) {
			addTemplatePartAParam(contract, templateParamList);
		}
		//设置合同模版乙方参数信息
		if (!ObjectUtils.isEmpty(contract.getContractPartBVo()) 
				&& !ObjectUtils.isEmpty(contract.getContractPartBVo().getCustomerName())) {
			addTemplatePartBParam(contract, templateParamList);
		}
		//营业执照编号-营业执照编号，如果甲乙方都是企业的话，营业执照怎么传
		JoyContractSaveTemplate externalBusinessLicenseNumberParam = new JoyContractSaveTemplate();
		externalBusinessLicenseNumberParam.setRequired(1);
		externalBusinessLicenseNumberParam.setKey("externalBusinessLicenseNumber");
		externalBusinessLicenseNumberParam.setValue("");
		templateParamList.add(externalBusinessLicenseNumberParam);
		//设置模版支付信息参数
		addTemplatePayParam(contract, templateParamList);
		//设置模版附件信息参数
		addTemplateAttachmentParam(contract, templateParamList);
		
		jc.setExtendParamList(templateParamList);
		contractTemplateParamList.add(jc);
		return contractTemplateParamList;
	}

	/**
	 * 设置模版基础信息参数
	 * @param contract
	 * @param templateParamList
	 */
	private void addTemplateBaseInfoParam(ContractDetailVo contract, List<JoyContractSaveTemplate> templateParamList) {
		//合同编号
		JoyContractSaveTemplate templateParam = new JoyContractSaveTemplate();
		templateParam.setKey("customerContractNumber");
		templateParam.setValue(contract.getContractBaseInfoVo().getContractNumber());
		templateParam.setRequired(1);
		templateParamList.add(templateParam);
		if (DropdownEnum.CONTRACT_TYPE_PARK_SPACE_OWNERSHIP.getDictKey().equals(contract.getContractBaseInfoVo().getContractType()) 
				|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_RENT.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())
				|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_RENT_SZ.getDictKey().equals(contract.getContractBaseInfoVo().getContractType())) {
			//车位号
			JoyContractSaveTemplate parkLocationParam = new JoyContractSaveTemplate();
			parkLocationParam.setKey("customerParkingSpaceNumber");
			parkLocationParam.setValue(contract.getContractBaseInfoVo().getContractParkSpaceInfo());
			parkLocationParam.setRequired(1);
			templateParamList.add(parkLocationParam);
		} else {
			//房屋位置
			JoyContractSaveTemplate houseInfoLocationParam = new JoyContractSaveTemplate();
			houseInfoLocationParam.setKey("houseInfoLocation");
			houseInfoLocationParam.setValue(contract.getContractBaseInfoVo().getContractHouseInfo());
			houseInfoLocationParam.setRequired(1);
			templateParamList.add(houseInfoLocationParam);
			//建筑面积
			JoyContractSaveTemplate houseInfoConstructionAreaParam = new JoyContractSaveTemplate();
			houseInfoConstructionAreaParam.setKey("houseInfoConstructionArea");
			houseInfoConstructionAreaParam.setValue(contract.getContractBaseInfoVo().getArea() + "");
			houseInfoConstructionAreaParam.setRequired(1);
			templateParamList.add(houseInfoConstructionAreaParam);
		}
	}

	/**
	 * 设置模版支付信息参数
	 * @param contract
	 * @param templateParamList
	 */
	private void addTemplatePayParam(ContractDetailVo contract, List<JoyContractSaveTemplate> templateParamList) {
		//甲方支付费用金额
		if (!ObjectUtils.isEmpty(contract.getContractSignedVo().getLeaserCommission())) {
			JoyContractSaveTemplate orderFirstPartyPayAmountParam = new JoyContractSaveTemplate();
			orderFirstPartyPayAmountParam.setRequired(1);
			orderFirstPartyPayAmountParam.setKey("orderFirstPartyPayAmount");
			orderFirstPartyPayAmountParam.setValue(contract.getContractSignedVo().getLeaserCommission() + "");
			templateParamList.add(orderFirstPartyPayAmountParam);
		}
		//乙方支付费用金额
		if (!ObjectUtils.isEmpty(contract.getContractSignedVo().getCustomerCommission())) {
			JoyContractSaveTemplate orderSecondPartyPayAmountParam = new JoyContractSaveTemplate();
			orderSecondPartyPayAmountParam.setRequired(1);
			orderSecondPartyPayAmountParam.setKey("orderSecondPartyPayAmount");
			orderSecondPartyPayAmountParam.setValue(contract.getContractSignedVo().getCustomerCommission() + "");
			templateParamList.add(orderSecondPartyPayAmountParam);
		}
	}

	/**
	 * 设置模版附件信息参数
	 * @param contract
	 * @param templateParamList
	 */
	private void addTemplateAttachmentParam(ContractDetailVo contract,
			List<JoyContractSaveTemplate> templateParamList) {
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			//房屋交接清单
			List<DataFileInfoVo> handoverList = contract.getContractAttachementVo().getHouseHandoverAttachment();
			if (!CollectionUtils.isEmpty(handoverList)) {
				JoyContractSaveTemplate houseInfoHouseHandoverChecklistParam = new JoyContractSaveTemplate();
				houseInfoHouseHandoverChecklistParam.setRequired(1);
				houseInfoHouseHandoverChecklistParam.setKey("houseInfoHouseHandoverChecklist");
				List<URL> handoverUrlList = handoverList.stream().map(DataFileInfoVo::getPresignedUrl).collect(Collectors.toList());
				houseInfoHouseHandoverChecklistParam.setValue(objectMapper.writeValueAsString(handoverUrlList));
				templateParamList.add(houseInfoHouseHandoverChecklistParam);
			}
			//甲方房屋产权资料复印件-房屋产权资料
			List<DataFileInfoVo> ownershipList = contract.getContractAttachementVo().getLeaserOwnershipAttachment();
			if (!CollectionUtils.isEmpty(ownershipList)) {
				JoyContractSaveTemplate customerPropertyOwnershipInformationParam = new JoyContractSaveTemplate();
				customerPropertyOwnershipInformationParam.setRequired(1);
				customerPropertyOwnershipInformationParam.setKey("customerPropertyOwnershipInformation");
				List<URL> ownershipUrlList = ownershipList.stream().map(DataFileInfoVo::getPresignedUrl).collect(Collectors.toList());
				customerPropertyOwnershipInformationParam.setValue(objectMapper.writeValueAsString(ownershipUrlList));
				templateParamList.add(customerPropertyOwnershipInformationParam);
			} 
			//甲乙方双方的身份证明证件-身份证复印件
			List<DataFileInfoVo> customerIdCopyList = contract.getContractAttachementVo().getIdentityAttachment();
			if (!CollectionUtils.isEmpty(customerIdCopyList)) {
				JoyContractSaveTemplate customerIdCopyParam = new JoyContractSaveTemplate();
				customerIdCopyParam.setRequired(1);
				customerIdCopyParam.setKey("customerIdCopy");
				List<URL> customerIdUrlList = customerIdCopyList.stream().map(DataFileInfoVo::getPresignedUrl).collect(Collectors.toList());
				customerIdCopyParam.setValue(objectMapper.writeValueAsString(customerIdUrlList));
				templateParamList.add(customerIdCopyParam);
			} 
			//乙方身份证明证件
			List<DataFileInfoVo> customerIdentityList = contract.getContractAttachementVo().getCustomerIdentityAttachment();
			if (!CollectionUtils.isEmpty(customerIdentityList)) {
				JoyContractSaveTemplate customerIdentityAttachmentParam = new JoyContractSaveTemplate();
				customerIdentityAttachmentParam.setRequired(1);
				customerIdentityAttachmentParam.setKey("SecondPartyIDImage");
				List<URL> customerIdentityUrlList = customerIdentityList.stream().map(DataFileInfoVo::getPresignedUrl).collect(Collectors.toList());
				customerIdentityAttachmentParam.setValue(objectMapper.writeValueAsString(customerIdentityUrlList));
				templateParamList.add(customerIdentityAttachmentParam);
			} 
			//甲方车位产权资料复印件
			List<DataFileInfoVo> leaserCarOwnershipList = contract.getContractAttachementVo().getLeaserCarOwnershipAttachment();
			if (!CollectionUtils.isEmpty(leaserCarOwnershipList)) {
				JoyContractSaveTemplate leaserCarOwnershipParam = new JoyContractSaveTemplate();
				leaserCarOwnershipParam.setRequired(1);
				leaserCarOwnershipParam.setKey("FirstPartyParkingRightsImage");
				List<URL> leaserCarOwnershipUrlList = leaserCarOwnershipList.stream().map(DataFileInfoVo::getPresignedUrl).collect(Collectors.toList());
				leaserCarOwnershipParam.setValue(objectMapper.writeValueAsString(leaserCarOwnershipUrlList));
				templateParamList.add(leaserCarOwnershipParam);
			} 
			//乙方车辆资料复印件
			List<DataFileInfoVo> customerCarInfoList = contract.getContractAttachementVo().getCustomerCarInfoAttachment();
			if (!CollectionUtils.isEmpty(customerCarInfoList)) {
				JoyContractSaveTemplate customerCarInfoParam = new JoyContractSaveTemplate();
				customerCarInfoParam.setRequired(1);
				customerCarInfoParam.setKey("SecondParthVehicleImage");
				List<URL> customerCarInfoUrlList = customerCarInfoList.stream().map(DataFileInfoVo::getPresignedUrl).collect(Collectors.toList());
				customerCarInfoParam.setValue(objectMapper.writeValueAsString(customerCarInfoUrlList));
				templateParamList.add(customerCarInfoParam);
			} 
			//补充条款
			List<DataFileInfoVo> supplementTermsList = contract.getContractAttachementVo().getSupplementaryTermsAttachment();
			if (!CollectionUtils.isEmpty(supplementTermsList)) {
				JoyContractSaveTemplate supplementTermsParam = new JoyContractSaveTemplate();
				supplementTermsParam.setRequired(1);
				supplementTermsParam.setKey("HouseFloorPlan");
				List<URL> supplementTermsUrlList = supplementTermsList.stream().map(DataFileInfoVo::getPresignedUrl).collect(Collectors.toList());
				supplementTermsParam.setValue(objectMapper.writeValueAsString(supplementTermsUrlList));
				templateParamList.add(supplementTermsParam);
			}
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		} 
	}

	/**
	 * 设置模版乙方信息参数
	 * @param contract
	 * @param templateParamList
	 */
	private void addTemplatePartBParam(ContractDetailVo contract, List<JoyContractSaveTemplate> templateParamList) {
		//乙方（承租方）-乙方名称
		JoyContractSaveTemplate secondPartyNameParam = new JoyContractSaveTemplate();
		secondPartyNameParam.setRequired(1);
		secondPartyNameParam.setKey("secondPartyName");
		secondPartyNameParam.setValue(contract.getContractPartBVo().getCustomerName());
		templateParamList.add(secondPartyNameParam);
		//乙方证件编号-乙方证件编号
		JoyContractSaveTemplate externalSecondPartyIDNumberParam = new JoyContractSaveTemplate();
		externalSecondPartyIDNumberParam.setRequired(1);
		externalSecondPartyIDNumberParam.setKey("externalSecondPartyIDNumber");
		externalSecondPartyIDNumberParam.setValue(contract.getContractPartBVo().getIdentityNumber());
		templateParamList.add(externalSecondPartyIDNumberParam);
		//乙方联系地址-通信地址（乙）
		JoyContractSaveTemplate econdPartyMailAddressParam = new JoyContractSaveTemplate();
		econdPartyMailAddressParam.setRequired(1);
		econdPartyMailAddressParam.setKey("secondPartyMailAddress");
		econdPartyMailAddressParam.setValue(contract.getContractPartBVo().getCustomerAddress());
		templateParamList.add(econdPartyMailAddressParam);
		//乙方联系电话-联系电话（乙）
		JoyContractSaveTemplate secondPartyMobileParam = new JoyContractSaveTemplate();
		secondPartyMobileParam.setRequired(1);
		secondPartyMobileParam.setKey("secondPartyMobile");
		secondPartyMobileParam.setValue(Base64Util.decodeData(contract.getContractPartBVo().getCustomerMobileEncode()));
		templateParamList.add(secondPartyMobileParam);
		//乙方代理人-乙方代理人
		JoyContractSaveTemplate externalSecondPartyAgentParam = new JoyContractSaveTemplate();
		externalSecondPartyAgentParam.setRequired(1);
		externalSecondPartyAgentParam.setKey("externalSecondPartyAgent");
		externalSecondPartyAgentParam.setValue(contract.getContractPartBVo().getCustomerProxyUserName() == null ? "" : contract.getContractPartBVo().getCustomerProxyUserName());
		templateParamList.add(externalSecondPartyAgentParam);
		//乙方代理人编号-乙方代理人编号
		JoyContractSaveTemplate externalSecondPartyAgentNumberParam = new JoyContractSaveTemplate();
		externalSecondPartyAgentNumberParam.setRequired(1);
		externalSecondPartyAgentNumberParam.setKey("externalSecondPartyAgentNumber");
		externalSecondPartyAgentNumberParam.setValue(contract.getContractPartBVo().getCustomerProxyUserIdNumber() == null ? "" : contract.getContractPartBVo().getCustomerProxyUserIdNumber());
		templateParamList.add(externalSecondPartyAgentNumberParam);
		//乙方代理人联系电话-乙方代理人联系电话
		JoyContractSaveTemplate externalSecondPartyAgentMobileParam = new JoyContractSaveTemplate();
		externalSecondPartyAgentMobileParam.setRequired(1);
		externalSecondPartyAgentMobileParam.setKey("externalSecondPartyAgentMobile");
		if (Base64Util.decodeData(contract.getContractPartBVo().getCustomerProxyUserMobileEncode()) == null) {
			externalSecondPartyAgentMobileParam.setValue("");
		} else {
			externalSecondPartyAgentMobileParam.setValue(Base64Util.decodeData(contract.getContractPartBVo().getCustomerProxyUserMobileEncode())); 
		}
		templateParamList.add(externalSecondPartyAgentMobileParam);
	}

	/**
	 * 设置模版甲方信息参数
	 * @param contract
	 * @param templateParamList
	 */
	private void addTemplatePartAParam(ContractDetailVo contract, List<JoyContractSaveTemplate> templateParamList) {
		//甲方（产权人）-客户名称
		JoyContractSaveTemplate customerNameParam = new JoyContractSaveTemplate();
		customerNameParam.setRequired(1);
		customerNameParam.setKey("customerName");
		customerNameParam.setValue(contract.getContractPartAVo().getLeaserUserName());
		templateParamList.add(customerNameParam);
		//甲方证件编号-客户证件号
		JoyContractSaveTemplate customerIdCardParam = new JoyContractSaveTemplate();
		customerIdCardParam.setRequired(1);
		customerIdCardParam.setKey("customerIdCard");
		customerIdCardParam.setValue(contract.getContractPartAVo().getIdentityNumber());
		templateParamList.add(customerIdCardParam);
		//甲方联系地址-常住地址
		JoyContractSaveTemplate customerResidentialAddressParam = new JoyContractSaveTemplate();
		customerResidentialAddressParam.setRequired(1);
		customerResidentialAddressParam.setKey("customerResidentialAddress");
		customerResidentialAddressParam.setValue(contract.getContractPartAVo().getLeaserAddress());
		templateParamList.add(customerResidentialAddressParam);
		//甲方联系电话-客户联系方式
		JoyContractSaveTemplate customerTelephoneParam = new JoyContractSaveTemplate();
		customerTelephoneParam.setRequired(1);
		customerTelephoneParam.setKey("customerTelephone");
		customerTelephoneParam.setValue(Base64Util.decodeData(contract.getContractPartAVo().getLeaserMobileEncode()));
		templateParamList.add(customerTelephoneParam);
		//甲方代理人-甲方代理人
		JoyContractSaveTemplate customerFirstPartyAgentParam = new JoyContractSaveTemplate();
		customerFirstPartyAgentParam.setRequired(1);
		customerFirstPartyAgentParam.setKey("customerFirstPartyAgent");
		customerFirstPartyAgentParam.setValue(contract.getContractPartAVo().getLeaserProxyUserName() == null ? "" : contract.getContractPartAVo().getLeaserProxyUserName());
		templateParamList.add(customerFirstPartyAgentParam);
		//甲方代理人证件编号-甲方代理人证件编号
		JoyContractSaveTemplate customerFirstPartyAgentIDCardParam = new JoyContractSaveTemplate();
		customerFirstPartyAgentIDCardParam.setRequired(1);
		customerFirstPartyAgentIDCardParam.setKey("customerFirstPartyAgentIDCard");
		customerFirstPartyAgentIDCardParam.setValue(contract.getContractPartAVo().getLeaserProxyUserIdNumber() == null ? "" : contract.getContractPartAVo().getLeaserProxyUserIdNumber());
		templateParamList.add(customerFirstPartyAgentIDCardParam);
		//甲方代理人联系电话-甲方代理人联系电话
		JoyContractSaveTemplate customerFirstPartyAgentMobileParam = new JoyContractSaveTemplate();
		customerFirstPartyAgentMobileParam.setRequired(1);
		customerFirstPartyAgentMobileParam.setKey("customerFirstPartyAgentMobile");
		if (Base64Util.decodeData(contract.getContractPartAVo().getLeaserProxyUserMobileEncode()) == null) {
			customerFirstPartyAgentMobileParam.setValue("");
		} else {
			customerFirstPartyAgentMobileParam.setValue(Base64Util.decodeData(contract.getContractPartAVo().getLeaserProxyUserMobileEncode()));
		}
		templateParamList.add(customerFirstPartyAgentMobileParam);
	}
	
}
