package mixc.be.rsms.business.service.impl;

import java.net.URI;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.enums.BusinessEnum;
import mixc.be.rsms.business.common.enums.ResultEnum;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.exception.UnAuthException;
import mixc.be.rsms.business.common.feignclient.AuthorServerClient;
import mixc.be.rsms.business.common.feignclient.DataServerClient;
import mixc.be.rsms.business.common.feignclient.ThirdServiceClient;
import mixc.be.rsms.business.common.mq.SendMsg;
import mixc.be.rsms.business.convert.BizContractConvert;
import mixc.be.rsms.business.domain.dto.ContractFileInfoParam;
import mixc.be.rsms.business.domain.dto.ContractNumberParam;
import mixc.be.rsms.business.domain.dto.ContractSignAddParams;
import mixc.be.rsms.business.domain.dto.FileInfoParam;
import mixc.be.rsms.business.domain.dto.JoyContractCallbackParam;
import mixc.be.rsms.business.domain.pojo.TbBusinessContract;
import mixc.be.rsms.business.domain.pojo.TbBusinessContractSignMapping;
import mixc.be.rsms.business.domain.pojo.TbBusinessJoyContractStatusCallback;
import mixc.be.rsms.business.mapper.TbBusinessContractMapper;
import mixc.be.rsms.business.mapper.TbBusinessContractSignMappingMapper;
import mixc.be.rsms.business.mapper.TbBusinessJoyContractStatusCallbackMapper;
import mixc.be.rsms.business.service.IBizContractService;
import mixc.be.rsms.business.service.IBizContractSignParamService;
import mixc.be.rsms.business.service.IContractService;
import mixc.be.rsms.business.utils.PermissionUtil;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.ContractAttachmentVo;
import mixc.be.rsms.business.vo.ContractBaseInfoVo;
import mixc.be.rsms.business.vo.ContractDetailVo;
import mixc.be.rsms.business.vo.ContractPartAVo;
import mixc.be.rsms.business.vo.ContractPartBVo;
import mixc.be.rsms.business.vo.ContractSignedVo;
import mixc.be.rsms.business.vo.DataFileInfoVo;
import mixc.be.rsms.business.vo.EnterpriseVo;
import mixc.be.rsms.business.vo.HouseInfoVo;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.common.constant.RsmsConstant;
import mixc.be.rsms.common.enums.CommonEnum;
import mixc.be.rsms.common.enums.DropdownEnum;
import mixc.be.rsms.common.enums.PermissionEnum;
import mixc.be.rsms.common.utils.AESUtil;
import mixc.be.rsms.common.utils.AuthUtil;
import mixc.be.rsms.common.utils.Base64Util;
import mixc.be.rsms.common.utils.HmacSHA256Util;
import mixc.be.rsms.common.utils.PersonDataEncryptUtil;
import mixc.be.rsms.common.utils.SnowFlake;
import mixc.be.rsms.common.utils.StpEmployeeUtil;
import mixc.be.rsms.pojo.author.UserDetailsMicroResp;
import mixc.be.rsms.pojo.business.MessageContent;
import mixc.be.rsms.pojo.data.ContractNumberResp;
import mixc.be.rsms.pojo.data.CustomerSourceResp;
import mixc.be.rsms.pojo.data.ParkingSpaceResp;
import mixc.be.rsms.pojo.third.JoyContract;
import mixc.be.rsms.pojo.third.JoyContractAttachementResp;
import mixc.be.rsms.pojo.third.JoyContractAttachementResp.JoyContractAttachement;
import mixc.be.rsms.pojo.third.JoyContractAttachementResp.Result;
import mixc.be.rsms.pojo.third.JoyContractPreview;
import mixc.be.rsms.pojo.third.JoyCreateContractParam;
import mixc.be.rsms.pojo.third.JoyResultResp;
import mixc.be.rsms.pojo.third.JoySignObjectParam;

/**
 * @ClassName ContractServiceImpl
 * @Desription 合同业务实现类
 * <AUTHOR>
 * @date 2024-12-13
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class ContractServiceImpl implements IContractService {
	
	private final IBizContractService bizContractService;
	private final DataServerClient dataClient;
	private final AuthorServerClient authClient;
	private final BizContractConvert bizContractConvert;
	private final ThirdServiceClient thirdServiceClient;
	private final StringRedisTemplate stringRedisTemplate;
	private final IBizContractSignParamService signParamService;
	private final TbBusinessContractSignMappingMapper contractSignMappingMapper;
	private final TbBusinessJoyContractStatusCallbackMapper joyContractStatusCallbackMapper;
	private final TbBusinessContractMapper contractMapper;
	private final SendMsg sendMsg;
	private final SnowFlake snowFlake;
	
	@Value("${joy.contract_status.signature_key}")
	private String signatureSecretKey;
	
	@Value("${joy.contract_status.aeskey}")
	private String aesKey;
	
	@Override
	@GlobalTransactional(name = "addContract", rollbackFor = Exception.class)
	public ResultVo<JoyContractPreview> addContractSign(ContractSignAddParams addParams) {
		String operateType = addParams.getOperateType();
		//保存合同基本信息
		Long contractId = bizContractService.addContractSignBaseInfo(addParams);
		//保存附件
		saveResidentRentAttachment(addParams, contractId);
		JoyCreateContractParam joyContractParam = assembleJoyContractParam(addParams, contractId);
		//调用朝昔合同接口
		JoyContractPreview jcp = new JoyContractPreview();
		if (!RsmsConstant.OPERATE_TYPE_SAVE.equals(operateType)) {
			String accessToken = getAccessToken();
			if (ObjectUtils.isEmpty(accessToken)) {
				throw new UnAuthException(ResultEnum.AUTH_DENY);
			}
			log.info("add contract accessToken is {}", accessToken);
			joyContractParam.setAccessToken(accessToken);
			//调用第三方接口创建合同
			log.info("创建朝昔合同参数：{}", joyContractParam);
			ResultVo<List<String>> resultVo = thirdServiceClient.createJoyContract(joyContractParam);
			log.info("创建朝昔合同返回结果：{}", resultVo);
			if ("0".equals(resultVo.getCode())) {
				List<String> contractList = resultVo.getData();
				if (!CollectionUtils.isEmpty(contractList)) {
					String zxContractId = contractList.get(0);
					jcp.setContractId(zxContractId);
					jcp.setAccessToken(accessToken);
					//更新合同
					bizContractService.updateJoyContractId(contractId, zxContractId);
				}
			} else if (RsmsConstant.JOY_ACCESS_TOKEN_EXPIRED_CODE.equals(resultVo.getCode())) {
				authClient.empLogout();
				throw new UnAuthException(ResultEnum.AUTH_DENY);
			} else {
				return ResultVoUtil.error(resultVo.getCode(), resultVo.getMsg());
			}
		}
		//更新合同号状态
		String contractNumber = addParams.getContractBaseInfo().getContractNumber();
		ContractNumberResp contractNumberResp = dataClient.getByContractNumber(
				contractNumber, addParams.getContractBaseInfo().getIntermediaryCompanyId());
		ContractNumberParam updateParam = new ContractNumberParam();
		updateParam.setCompanyId(addParams.getContractBaseInfo().getIntermediaryCompanyId());
		updateParam.setContractNumber(contractNumber);
		if (!ObjectUtils.isEmpty(contractNumberResp)) {
			Integer flag = dataClient.updateContractNumber(updateParam);
			if (!ObjectUtils.isEmpty(flag) && flag.intValue() != 1) {
				throw new BusinessException(BusinessEnum.CONTRACT_NUMBER_HAS_USED);
			}
		} else {
			Integer flag = dataClient.updateContractNumberRule(updateParam);
			if (!ObjectUtils.isEmpty(flag) && flag.intValue() != 1) {
				throw new BusinessException(BusinessEnum.CONTRACT_NUMBER_HAS_USED);
			}
		}
		return ResultVoUtil.success(jcp);
	}

	@Override
	public ContractDetailVo contractDetail(Long contractId, String sourceType) {
		if (ObjectUtils.isEmpty(contractId)) {
			throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
		}
		TbBusinessContract bizContract = bizContractService.bizContractDetail(contractId);
		String contractType = bizContract.getContractType();
		List<TbBusinessContractSignMapping> queryContractMappingList = contractSignMappingMapper.selectList(Wrappers.lambdaQuery(
				TbBusinessContractSignMapping.class).eq(TbBusinessContractSignMapping::getContractType, contractType));
		ContractDetailVo contractDetailVo = new ContractDetailVo();
		ContractBaseInfoVo baseInfo = bizContractConvert.convertBaseInfoVo(bizContract);
		ContractPartAVo partAVo = bizContractConvert.convertPartAVo(bizContract);
		ContractPartBVo partBVo = bizContractConvert.convertPartBVo(bizContract);
		ContractSignedVo signedVo = bizContractConvert.convertContractSignedVo(bizContract);
		ContractAttachmentVo attachmentVo = bizContractConvert.convertContractAttachmentVo(bizContract);
		baseInfo.setContractId(contractId);
		//居间方公司
		if (!ObjectUtils.isEmpty(bizContract.getIntermediaryCompanyId())) {
			EnterpriseVo enterprise = dataClient.queryEnterprise(bizContract.getIntermediaryCompanyId());
			if (!ObjectUtils.isEmpty(enterprise)) {
				baseInfo.setIntermediaryCompanyName(enterprise.getEnterpriseName());
			} else {
				log.info("居间方公司ID：{} 不存在，请检查配置表", bizContract.getIntermediaryCompanyId());
			}
		}
		//权限
		List<String> permissionList = List.of(
				PermissionEnum.ESIGN_CONTRACT_MANAGE_EDIT.getCode(),
				PermissionEnum.ESIGN_CONTRACT_MANAGE_SUBMIT.getCode(),
				PermissionEnum.ESIGN_CONTRACT_MANAGE_APPROVAL_PASS.getCode(),
				PermissionEnum.ESIGN_CONTRACT_MANAGE_REJECT.getCode(),
				PermissionEnum.ESIGN_CONTRACT_MANAGE_GENERATE_REPORT.getCode(),
				PermissionEnum.ESIGN_CONTRACT_MANAGE_RESIGN.getCode(),
				PermissionEnum.ESIGN_CONTRACT_MANAGE_ARCHIVE.getCode());
		//区分客户端和员工端
        log.info("sourceType is {}", sourceType);
        if (ObjectUtils.isEmpty(sourceType) || (!ObjectUtils.isEmpty(sourceType) && !RsmsConstant.DICT_TYPE_CUSTOMER.equals(sourceType))) {
        	log.info("requst is employee or pc......");
        	List<String> permissions = PermissionUtil.getUserPermissionList(permissionList, AuthUtil.getLoginIdAsLong());
        	contractDetailVo.setPermissions(permissions);
        }
		//联系电话加密
		encrpyMobile(bizContract, partAVo, partBVo);
		//房屋信息
		if (!ObjectUtils.isEmpty(bizContract.getHouseId())) {
			List<HouseInfoVo> houseList = dataClient.listHouseInfoByIds(List.of(bizContract.getHouseId()));
			if (!CollectionUtils.isEmpty(houseList)) {
				HouseInfoVo house = houseList.get(0);
				baseInfo.setArea(house.getArea());
				baseInfo.setContractHouseInfo(house.getLocation() + RsmsConstant.SPLIT_VERTICAL_LINE 
						+ house.getHouseNumber() + RsmsConstant.SPLIT_VERTICAL_LINE + house.getCommunityName());
			}
			if (!CollectionUtils.isEmpty(queryContractMappingList) 
					&& DropdownEnum.HOUSE_QUERY_FOR_RENT.getDictKey().equals(queryContractMappingList.get(0).getBusinessType())) {
				signedVo.setRentStatus(DropdownEnum.HOUSE_QUERY_FOR_RENT.getDictKey());
			} else {
				signedVo.setRentStatus(DropdownEnum.HOUSE_QUERY_FOR_SELL.getDictKey());
			}
		}
		//车位信息
		if (!ObjectUtils.isEmpty(bizContract.getParkingSpaceId())) {
			List<ParkingSpaceResp> parkSpaceRespList = dataClient.getParkingSpaceByIdList(List.of(bizContract.getParkingSpaceId()));
			if (!CollectionUtils.isEmpty(parkSpaceRespList)) {
				ParkingSpaceResp parkSpaceResp = parkSpaceRespList.get(0);
				baseInfo.setArea(parkSpaceResp.getArea());
				baseInfo.setContractParkSpaceInfo(DropdownEnum.getDictValue("parking_space_type", parkSpaceResp.getType()) + RsmsConstant.SPLIT_VERTICAL_LINE 
						+ parkSpaceResp.getNumber() + RsmsConstant.SPLIT_VERTICAL_LINE + parkSpaceResp.getCommunityName());
				signedVo.setParkRentStatus(parkSpaceResp.getSaleStatus());
				if (!CollectionUtils.isEmpty(queryContractMappingList) 
						&& DropdownEnum.HOUSE_QUERY_FOR_RENT.getDictKey().equals(queryContractMappingList.get(0).getBusinessType())) {
					signedVo.setParkRentStatus(DropdownEnum.USED_PARKING_SPACE_RENTAL.getDictKey());
				} else {
					signedVo.setParkRentStatus(DropdownEnum.NEW_PARKING_SPACE_SALE.getDictKey());
				}
			}
		}
		//客户名称
		if (!ObjectUtils.isEmpty(bizContract.getCustomerId())) {
			List<CustomerSourceResp> customerList = dataClient.innerQueryCustomerByIdList(List.of(bizContract.getCustomerId()));
			if (!CollectionUtils.isEmpty(customerList)) {
				partBVo.setCustomerName(customerList.get(0).getName());
			}
		}
		//门店
		List<UserDetailsMicroResp> userDetailRespList = authClient.getUserDetailsMicro(List.of(baseInfo.getCreateUser()));
		if (!CollectionUtils.isEmpty(userDetailRespList)) {
			baseInfo.setCreator(userDetailRespList.getFirst().getEmpName());
			baseInfo.setStoreName(userDetailRespList.get(0).getStoreName());
		}
		//附件信息
		ResultVo<List<DataFileInfoVo>> resultVo = dataClient.listFileInfo(contractId, null, true);
		List<DataFileInfoVo> contractFileList = resultVo.getData();
		if (!CollectionUtils.isEmpty(contractFileList)) {
			Map<String, List<DataFileInfoVo>> groupFileMap = contractFileList
					.stream().collect(Collectors.groupingBy(DataFileInfoVo::getBusinessType));
			groupFileMap.forEach((k, v) -> {
				if (RsmsConstant.LEASER_OWNERSHIP.equals(k)) {
					attachmentVo.setLeaserOwnershipAttachment(v);
				} else if (RsmsConstant.IDENTITY.equals(k)) {
					attachmentVo.setIdentityAttachment(v);
				} else if (RsmsConstant.HOUSE_HANDOVER.equals(k)) {
					attachmentVo.setHouseHandoverAttachment(v);
				} else if (RsmsConstant.CUSTOMER_ID_IDENTITY.equals(k)) {
					attachmentVo.setCustomerIdentityAttachment(v);
				} else if (RsmsConstant.LEASER_CAR_OWNERSHIP.equals(k)) {
					attachmentVo.setLeaserCarOwnershipAttachment(v);
				} else if (RsmsConstant.CUSTOMER_CAR_INFO.equals(k)) {
					attachmentVo.setCustomerCarInfoAttachment(v);
				} else if (RsmsConstant.SUPPLEMENTARY_TERMS.equals(k)) {
					attachmentVo.setSupplementaryTermsAttachment(v);
				} else if (RsmsConstant.HOUSE_DESIGN_AND_DECORATION.equals(k)) {
					attachmentVo.setHouseDesignAndDecorationAttachment(v);
				}
			});
		}
		//获取合同附件
		getContractAttachement(bizContract, attachmentVo);
		contractDetailVo.setContractBaseInfoVo(baseInfo);
		contractDetailVo.setContractPartAVo(partAVo);
		contractDetailVo.setContractPartBVo(partBVo);
		contractDetailVo.setContractSignedVo(signedVo);
		contractDetailVo.setContractAttachementVo(attachmentVo);
		return contractDetailVo;
	}

	/**
	 * 获取朝昔合同附件
	 * @param bizContract 合同对象
	 * @param attachmentVo 附件返回对象
	 */
	private void getContractAttachement(TbBusinessContract bizContract, ContractAttachmentVo attachmentVo) {
		//获取合同附件
		if (!ObjectUtils.isEmpty(bizContract.getJoyContractId())) {
			JoyContractAttachementResp joyContractAttResp = thirdServiceClient.getJoyContractAttachementUrl(bizContract.getJoyContractId());
			Result result = joyContractAttResp.getData();
			if (!ObjectUtils.isEmpty(result)) {
				List<JoyContractAttachement> attList = result.getFiles();
				if (!CollectionUtils.isEmpty(attList)) {
					List<DataFileInfoVo> joyContractAttachmentVo = attList.stream().map(oldObj -> {
						DataFileInfoVo vo = new DataFileInfoVo();
						vo.setFileName(oldObj.getFileName());
						URI uri;
						try {
							uri = new URI(oldObj.getFileOssURL());
							URL url = uri.toURL();
							vo.setPresignedUrl(url);
							return vo;
						} catch (Exception e) {
							return null;
						}
					}).collect(Collectors.toList());
					attachmentVo.setContractAttachment(joyContractAttachmentVo);
				}
			}
		}
	}

	/**
	 * 手机号码加密
	 * @param contractDetailVo
	 */
	private void encrpyMobile(TbBusinessContract bizContract, ContractPartAVo partAVo, ContractPartBVo partBVo) {
		partBVo.setCustomerMobileEncode(Base64Util.encodeData(bizContract.getCustomerMobile()));
		partBVo.setCustomerMobile(PersonDataEncryptUtil.mobileEncrypt(bizContract.getCustomerMobile()));
		partBVo.setCustomerProxyUserMobileEncode(Base64Util.encodeData(bizContract.getCustomerProxyUserMobile()));
		partBVo.setCustomerProxyUserMobile(PersonDataEncryptUtil.mobileEncrypt(bizContract.getCustomerProxyUserMobile()));
		partAVo.setLeaserMobileEncode(Base64Util.encodeData(bizContract.getLeaserMobile()));
		partAVo.setLeaserMobile(PersonDataEncryptUtil.mobileEncrypt(bizContract.getLeaserMobile()));
		partAVo.setLeaserProxyUserMobileEncode(Base64Util.encodeData(bizContract.getLeaserProxyUserMobile()));
		partAVo.setLeaserProxyUserMobile(PersonDataEncryptUtil.mobileEncrypt(bizContract.getLeaserProxyUserMobile()));
	}
	
	/**
	 * 设置房屋租赁居间合同扩展信息
	 * @param contractDetailVo 合同基本信息详情
	 * @param residentRentContract 扩展信息
	 */
//	private void setExtendsInfo(
//			ContractDetailVo contractDetailVo, TbBusinessContractResidentRent residentRentContract) {
//		if (!ObjectUtils.isEmpty(residentRentContract)) {
//			contractDetailVo.setLeaserProxyUserName(residentRentContract.getLeaserProxyUserName());
//			contractDetailVo.setLeaserProxyUserMobile(residentRentContract.getLeaserProxyUserMobile());
//			contractDetailVo.setLeaserProxyUserIdNumber(residentRentContract.getLeaserProxyUserIdNumber());
//			contractDetailVo.setCustomerProxyUserName(residentRentContract.getCustomerProxyUserName());
//			contractDetailVo.setCustomerProxyUserMobile(residentRentContract.getCustomerProxyUserMobile());
//			contractDetailVo.setCustomerProxyUserIdNumber(residentRentContract.getCustomerProxyUserIdNumber());
//		}
//	}

	/**
	 * 保存合同附件
	 * @param addParams 添加参数
	 * @param contractId 合同ID
	 */
	private void saveResidentRentAttachment(ContractSignAddParams addParams, Long contractId) {
		if (ObjectUtils.isEmpty(addParams.getAttachmentParams())) {
			return;
		}
		List<FileInfoParam> attachmentList = new ArrayList<>();
		//甲方房屋产权资料复印件
		List<FileInfoParam> leaserOwnershipAttachmentParamList = addParams.getAttachmentParams().getLeaserOwnershipAttachment();
		if (!CollectionUtils.isEmpty(leaserOwnershipAttachmentParamList)) {
			leaserOwnershipAttachmentParamList.stream().forEach(item -> {
				item.setBusinessType(RsmsConstant.LEASER_OWNERSHIP);
				item.setBusinessId(contractId);
			});
			attachmentList.addAll(leaserOwnershipAttachmentParamList);
		}
		//甲乙双方身份证明复印件
		List<FileInfoParam> identityAttachmentParamList = addParams.getAttachmentParams().getIdentityAttachment();
		if (!CollectionUtils.isEmpty(identityAttachmentParamList)) {
			identityAttachmentParamList.stream().forEach(item -> {
				item.setBusinessType(RsmsConstant.IDENTITY);
				item.setBusinessId(contractId);
			});
			attachmentList.addAll(identityAttachmentParamList);
		}
		//房屋交接清单
		List<FileInfoParam> houseHandoverAttachmentParamList = addParams.getAttachmentParams().getHouseHandoverAttachment();
		if (!CollectionUtils.isEmpty(houseHandoverAttachmentParamList)) {
			houseHandoverAttachmentParamList.stream().forEach(item -> {
				item.setBusinessType(RsmsConstant.HOUSE_HANDOVER);
				item.setBusinessId(contractId);
			});
			attachmentList.addAll(houseHandoverAttachmentParamList);
		}
		//乙方身份证明证件
		List<FileInfoParam> customerIdentityAttachmentParamList = addParams.getAttachmentParams().getCustomerIdentityAttachment();
		if (!CollectionUtils.isEmpty(customerIdentityAttachmentParamList)) {
			customerIdentityAttachmentParamList.stream().forEach(item -> {
				item.setBusinessType(RsmsConstant.CUSTOMER_ID_IDENTITY);
				item.setBusinessId(contractId);
			});
			attachmentList.addAll(customerIdentityAttachmentParamList);
		}
		//甲方车位产权资料复印件
		List<FileInfoParam> leaserCarOwnershipAttachmentParamList = addParams.getAttachmentParams().getLeaserCarOwnershipAttachment();
		if (!CollectionUtils.isEmpty(leaserCarOwnershipAttachmentParamList)) {
			leaserCarOwnershipAttachmentParamList.stream().forEach(item -> {
				item.setBusinessType(RsmsConstant.LEASER_CAR_OWNERSHIP);
				item.setBusinessId(contractId);
			});
			attachmentList.addAll(leaserCarOwnershipAttachmentParamList);
		}
		//乙方车辆资料复印件
		List<FileInfoParam> customerCarInfoAttachmentParamList = addParams.getAttachmentParams().getCustomerCarInfoAttachment();
		if (!CollectionUtils.isEmpty(customerCarInfoAttachmentParamList)) {
			customerCarInfoAttachmentParamList.stream().forEach(item -> {
				item.setBusinessType(RsmsConstant.CUSTOMER_CAR_INFO);
				item.setBusinessId(contractId);
			});
			attachmentList.addAll(customerCarInfoAttachmentParamList);
		}
		//补充条款
		List<FileInfoParam> supplementaryTermsAttachmentParamList = addParams.getAttachmentParams().getSupplementaryTermsAttachment();
		if (!CollectionUtils.isEmpty(supplementaryTermsAttachmentParamList)) {
			supplementaryTermsAttachmentParamList.stream().forEach(item -> {
				item.setBusinessType(RsmsConstant.SUPPLEMENTARY_TERMS);
				item.setBusinessId(contractId);
			});
			attachmentList.addAll(supplementaryTermsAttachmentParamList);
		}
		//补充条款
		List<FileInfoParam> houseDesignAndDecorationAttachmentParamList = addParams.getAttachmentParams().getHouseDesignAndDecorationAttachment();
		if (!CollectionUtils.isEmpty(houseDesignAndDecorationAttachmentParamList)) {
			houseDesignAndDecorationAttachmentParamList.stream().forEach(item -> {
				item.setBusinessType(RsmsConstant.HOUSE_DESIGN_AND_DECORATION);
				item.setBusinessId(contractId);
			});
			attachmentList.addAll(houseDesignAndDecorationAttachmentParamList);
		}
		ContractFileInfoParam fileInfoParam = new ContractFileInfoParam();
		fileInfoParam.setContractId(contractId);
		fileInfoParam.setHouseFileList(attachmentList);
		//保存附件
		dataClient.innerSaveContractFile(fileInfoParam);
	}

	@Override
	@GlobalTransactional(name = "updateContract", rollbackFor = Exception.class)
	public ResultVo<JoyContractPreview> updateContract(ContractSignAddParams updateParams) {
		String operateType = updateParams.getOperateType();
		//更新合同基础信息
		bizContractService.updateContract(updateParams);
		Long contractId = updateParams.getId();
		//更新附件信息
		saveResidentRentAttachment(updateParams, contractId);
		JoyContractPreview jcp = new JoyContractPreview();
		if (!RsmsConstant.OPERATE_TYPE_SAVE.equals(operateType)) {
			String accessToken = getAccessToken();
			log.info("更新朝昔合同签署模版参数获取到的token： {}", accessToken);
			if (ObjectUtils.isEmpty(accessToken)) {
				throw new UnAuthException(ResultEnum.AUTH_DENY);
			}
			//合同详细信息
			ContractDetailVo contractDetailVo = contractDetail(contractId, RsmsConstant.DICT_TYPE_CUSTOMER);
			//调用朝昔接口保存合同
			JoyCreateContractParam joyContractParam = new JoyCreateContractParam();
			String joyContractId = contractDetailVo.getContractBaseInfoVo().getJoyContractId();
			List<JoyContract> contractTemplateParamList = new ArrayList<>();
			log.info("更新获取到的朝昔合同ID:{}", joyContractId);
			if (ObjectUtils.isEmpty(joyContractId)) {
				//朝昔合同ID为空
				log.info("=================轻合同对接走创建合同对接");
				joyContractParam = assembleJoyContractParam(updateParams, contractId);
				joyContractParam.setAccessToken(accessToken);
				ResultVo<List<String>> resultVo = thirdServiceClient.createJoyContract(joyContractParam);
				log.info("创建朝昔合同返回结果：{}", resultVo);
				if ("0".equals(resultVo.getCode())) {
					List<String> contractList = resultVo.getData();
					if (!CollectionUtils.isEmpty(contractList)) {
						String zxContractId = contractList.get(0);
						jcp.setContractId(zxContractId);
						jcp.setAccessToken(accessToken);
						//更新合同
						bizContractService.updateJoyContractId(contractId, zxContractId);
					}
				} else if (RsmsConstant.JOY_ACCESS_TOKEN_EXPIRED_CODE.equals(resultVo.getCode())) {
					authClient.empLogout();
					throw new UnAuthException(ResultEnum.AUTH_DENY); 
				} else {
					return ResultVoUtil.error(resultVo.getCode(), resultVo.getMsg());
				}
			} else {
				log.info("=================轻合同对接走更新对接");
				//朝昔合同ID不为空
				contractTemplateParamList = signParamService.assembleTemplateParam(contractDetailVo);
				if (!CollectionUtils.isEmpty(contractTemplateParamList)) {
					contractTemplateParamList.get(0).setContractId(contractDetailVo.getContractBaseInfoVo().getJoyContractId());
				}
				joyContractParam.setJoyContractTemplateParamList(contractTemplateParamList);
				joyContractParam.setAccessToken(accessToken);
				//调用第三方接口创建合同
				log.info("更新朝昔合同签署模版参数：{}", joyContractParam);
				JoyResultResp<String> resultVo = thirdServiceClient.updateJoyContract(joyContractParam);
				log.info("更新朝昔合同签署参数返回结果：{}", resultVo);
				if (ResultEnum.OK.getCode().equals(resultVo.getCode())) {
					jcp.setAccessToken(accessToken);
					jcp.setContractId(contractDetailVo.getContractBaseInfoVo().getJoyContractId());
					return ResultVoUtil.success(jcp);
				} else if (RsmsConstant.JOY_ACCESS_TOKEN_EXPIRED_CODE.equals(resultVo.getCode())) {
					authClient.empLogout();
					throw new UnAuthException(ResultEnum.AUTH_DENY); 
				} else {
					return ResultVoUtil.error(resultVo.getCode(), resultVo.getMessage());
				}
			}
			//更新合同号状态
//			String contractNumber = contractDetailVo.getContractBaseInfoVo().getContractNumber();
//			ContractNumberResp contractNumberResp = dataClient.getByContractNumber(
//					contractNumber, updateParams.getContractBaseInfo().getIntermediaryCompanyId());
//			ContractNumberParam updateParam = new ContractNumberParam();
//			updateParam.setCompanyId(updateParams.getContractBaseInfo().getIntermediaryCompanyId());
//			updateParam.setContractNumber(contractNumber);
//			if (!ObjectUtils.isEmpty(contractNumberResp)) {
//				Integer flag = dataClient.updateContractNumber(updateParam);
//				if (!ObjectUtils.isEmpty(flag) && flag.intValue() != 1) {
//					throw new BusinessException(BusinessEnum.CONTRACT_NUMBER_HAS_USED);
//				}
//			} else {
//				Integer flag = dataClient.updateContractNumberRule(updateParam);
//				if (!ObjectUtils.isEmpty(flag) && flag.intValue() != 1) {
//					throw new BusinessException(BusinessEnum.CONTRACT_NUMBER_HAS_USED);
//				}
//			}
		}
		return ResultVoUtil.success(jcp);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteContract(Long contractId) {
		//删除合同基础信息
		bizContractService.deleteContract(contractId);
	}

	/**
	 * 组装合同新增参数
	 * @param addParams
	 * @param contractId
	 * @return
	 */
	private JoyCreateContractParam assembleJoyContractParam(ContractSignAddParams addParams, Long contractId) {
		Long userId = AuthUtil.getLoginIdAsLong();
		//组织ID
		List<UserDetailsMicroResp> userDetailRespList = authClient.getUserDetailsMicro(List.of(userId));
		String orgId = null;
		if (!CollectionUtils.isEmpty(userDetailRespList)) {
			orgId = userDetailRespList.get(0).getOrgId();
		}
		//合同详细信息
		ContractDetailVo contractDetailVo = contractDetail(contractId, RsmsConstant.DICT_TYPE_CUSTOMER);
		//模版ID
		String templateCode = null;
		List<TbBusinessContractSignMapping> contractSignMappingList = contractSignMappingMapper.selectList(
				new LambdaQueryWrapper<TbBusinessContractSignMapping>().eq(TbBusinessContractSignMapping::getContractType, contractDetailVo.getContractBaseInfoVo().getContractType()));
		if (!CollectionUtils.isEmpty(contractSignMappingList)) {
			templateCode = contractSignMappingList.get(0).getTemplateCode();
		}
		//调用朝昔接口保存合同
		JoyCreateContractParam joyContractParam = new JoyCreateContractParam();
		//签署参数列表
		List<JoySignObjectParam> signObjectParamList = signParamService.assembleContractSignParam(contractDetailVo, joyContractParam);
		joyContractParam.setOrgId(orgId);
		joyContractParam.setSubmitFlag(0);
		joyContractParam.setTemplateCode(templateCode);
		joyContractParam.setSignObjectList(signObjectParamList);
		//模版参数
		List<JoyContract> contractTemplateParamList = signParamService.assembleTemplateParam(contractDetailVo);
		joyContractParam.setJoyContractTemplateParamList(contractTemplateParamList);
		return joyContractParam;
	}
	
	
	/**
	 * 获取access-token
	 * @return
	 */
	private String getAccessToken() {
		String tokenValue = StpEmployeeUtil.getTokenValue();
		log.info("员工端获取到的tokenValue=============={}", tokenValue);
		if (!stringRedisTemplate.hasKey(tokenValue)) {
			return null;
		} 
		String accessToken = stringRedisTemplate.opsForValue().get(StpEmployeeUtil.getTokenValue());
		log.info("朝昔接口调用accessToken=========={}", accessToken);
		return accessToken;
	}

	@Override
	public ResultVo<String> contractStatusCallBack(
			String joyContractSignature, 
			String joyContractTimestamp, 
			String joyContractNonce, 
			String joyContractCallbackParamJson) {
		ResultVo<String> result = new ResultVo<String>(ResultEnum.OK.getCode(), "success", null);
		log.info("朝昔回调租售更新合同状态获取到的参数为,签名：{}, 时间戳：{}, 随机数：{}, 请求体参数：{}", 
				joyContractSignature, joyContractTimestamp, joyContractNonce, joyContractCallbackParamJson);
		//校验参数
		if (ObjectUtils.isEmpty(joyContractCallbackParamJson)) {
			result = new ResultVo<String>(ResultEnum.ERROR.getCode(), "请求体参数不能为空", null);
			return result;
		}
		try {
			//验签
			ObjectMapper objectMapper = new ObjectMapper();
			Map<String, String> signParamMap = new HashMap<>();
			signParamMap.put("ciphertext", joyContractCallbackParamJson);
			String signParam = objectMapper.writeValueAsString(signParamMap);
			boolean verifyFlag = verifySignature(joyContractSignature, joyContractTimestamp, joyContractNonce, signParam);
			if (!verifyFlag) {
				result = new ResultVo<String>(ResultEnum.ERROR.getCode(), "合同验签不通过", null);
				return result;
			}
			// AES解密
			String joyContractParamDecrypt = AESUtil.decrypt(joyContractCallbackParamJson, aesKey);
//			String joyContractParamDecrypt = "{\"eventType\":\"CONTRACT_STATUS_CHANGE\",\"resource\":{\"contractId\":\"5584124\",\"contractNotifyState\":2,\"contractStatusUpdateTime\":1736774059532,\"contractSignObjectList\":[{\"signParty\":\"firstParty\",\"signObjectType\":\"CORPORATE\",\"signObjectName\":\"华润万象生活有限公司\",\"signStatus\":1,\"signTime\":1736774059532},{\"signParty\":\"secondParty\",\"signObjectType\":\"PERSONAL\",\"signObjectMobile\":\"18825468458\",\"signObjectName\":\"张三\",\"signStatus\":0,\"signTime\":1736774059532},{\"signParty\":\"thirdParty\",\"signObjectType\":\"CORPORATE\",\"signObjectName\":\"爱创科技有限公司\",\"signStatus\":0,\"signTime\":1736774059532}]}}";
			log.info("解密后的回调请求参数为：{}", joyContractParamDecrypt);
			//解析json
			JoyContractCallbackParam joyContractCallbackParam = objectMapper.readValue(joyContractParamDecrypt, JoyContractCallbackParam.class);
			if (!ObjectUtils.isEmpty(joyContractCallbackParam)) {
				String contractId = joyContractCallbackParam.getResource().getContractId();
				//保存请求参数
				TbBusinessJoyContractStatusCallback joyContractStatusCallback = new TbBusinessJoyContractStatusCallback();
				joyContractStatusCallback.setJoyContractId(contractId);
				joyContractStatusCallback.setBody(joyContractCallbackParamJson);
				joyContractStatusCallback.setId(snowFlake.nextId());
				joyContractStatusCallback.setStatus("SUCCESS");
				joyContractStatusCallbackMapper.insert(joyContractStatusCallback);
				//发送到消息队列
	            MessageContent<String> messageContent = new MessageContent<>();
	            messageContent.setMsgId(UUID.randomUUID().toString());
	            messageContent.setData(joyContractParamDecrypt);
	            messageContent.setCreateTime(new Date());
	            sendMsg.sendJoyContractStatusMsg(objectMapper.writeValueAsString(messageContent));
			}
		} catch (Exception e) {
			log.error("朝昔合同回调出错，原因：{}", e);
			result = new ResultVo<String>(ResultEnum.ERROR.getCode(), e.getMessage(), null);
			return result;
		}
		return result;
	}

	/**
	 * 验签
	 * @param joyContractSignature
	 * @param joyContractTimestamp
	 * @param joyContractNonce
	 * @param joyContractCallbackParamJson
	 * @return
	 */
	private boolean verifySignature(
			String joyContractSignature, 
			String joyContractTimestamp, 
			String joyContractNonce,
			String joyContractCallbackParamJson) {
		String signatureData = joyContractTimestamp + "\n" + joyContractNonce + "\n" + joyContractCallbackParamJson + "\n";
		String sha256Message = null;
		try {
			sha256Message = HmacSHA256Util.calculateHmacSHA256(signatureData, signatureSecretKey);
		} catch (Exception e) {
			log.error("验签失败，原因：{}", e);
			throw new RuntimeException(e);
		}
		log.info("验签时获取到的加密数据为：{}", sha256Message);
		if (!ObjectUtils.isEmpty(sha256Message) && !sha256Message.equals(joyContractSignature)) {
			return false;
	    }
		return true;
	}

	@Override
	public List<String> listContractByNumbers(List<String> contractNumbers) {
		if (CollectionUtils.isEmpty(contractNumbers)) {
			return null;
		}
		List<TbBusinessContract> contractList = contractMapper.selectList(
				Wrappers.lambdaQuery(TbBusinessContract.class).in(TbBusinessContract::getContractNumber, contractNumbers));
		if (!CollectionUtils.isEmpty(contractList)) {
			return contractList.stream().map(TbBusinessContract::getContractNumber).toList();
		}
		return null;
	}
	
}
