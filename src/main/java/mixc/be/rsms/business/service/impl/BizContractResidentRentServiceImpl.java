package mixc.be.rsms.business.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.convert.BizContractResidentRentConvert;
import mixc.be.rsms.business.domain.dto.ContractSignAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessContractResidentRent;
import mixc.be.rsms.business.mapper.TbBusinessContractResidentRentMapper;
import mixc.be.rsms.business.service.IBizContractResidentRentService;
import mixc.be.rsms.common.enums.CommonEnum;
import mixc.be.rsms.common.utils.AuthUtil;
import mixc.be.rsms.common.utils.SnowFlake;

/**
 * @ClassName BizContractResidentRentServiceImpl
 * @Desription 房屋租赁居间服务合同（住宅）业务实现类
 * <AUTHOR>
 * @date 2024-12-16
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class BizContractResidentRentServiceImpl implements IBizContractResidentRentService {
	
	private final TbBusinessContractResidentRentMapper residentRentMapper;
	private final BizContractResidentRentConvert residentRentCovert;
	private final SnowFlake snowFlake;
	
	
	@Override
	public void addContractSignExtendInfo(ContractSignAddParams addParams, Long contractId) {
		if (ObjectUtils.isEmpty(addParams)) {
			throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
		}
		TbBusinessContractResidentRent residentRent = residentRentCovert.convert(addParams);
		residentRent.setId(snowFlake.nextId());
		residentRent.setContractId(contractId);
		residentRent.setCreateUser(AuthUtil.getLoginIdAsLong());
		residentRentMapper.insert(residentRent);
	}


	@Override
	public TbBusinessContractResidentRent residentRentDetail(Long contractId) {
		return residentRentMapper.selectOne(
				new LambdaQueryWrapper<TbBusinessContractResidentRent>().eq(TbBusinessContractResidentRent::getContractId, contractId).last("limit 1"));
	}


	@Override
	public void updateContract(ContractSignAddParams updateParams) {
		if (ObjectUtils.isEmpty(updateParams)) {
			throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
		}
		if (ObjectUtils.isEmpty(updateParams.getId())) {
			throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
		}
		TbBusinessContractResidentRent residentRentContract = residentRentCovert.convert(updateParams);
		residentRentContract.setUpdateUser(AuthUtil.getLoginIdAsLong());
		residentRentMapper.update(residentRentContract, 
				new LambdaUpdateWrapper<TbBusinessContractResidentRent>()
				.eq(TbBusinessContractResidentRent::getContractId, updateParams.getId()));
	}


	@Override
	public void deleteContract(Long contractId) {
		residentRentMapper.deleteById(contractId);
	}

}
