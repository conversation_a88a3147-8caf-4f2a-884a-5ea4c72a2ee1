package mixc.be.rsms.business.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.domain.pojo.TbBusinessContractSignMapping;
import mixc.be.rsms.business.mapper.TbBusinessContractSignMappingMapper;
import mixc.be.rsms.business.service.IContractSignTypeService;
import mixc.be.rsms.business.vo.ContractTypeDropdownVo;
import mixc.be.rsms.common.constant.RsmsConstant;
import mixc.be.rsms.pojo.business.ContractSignTypeResp;
import mixc.be.rsms.pojo.business.SignContractType;

/**
 * @ClassName ContractSignTypeServiceImpl
 * @Desription 合同签约类型实现类
 * <AUTHOR>
 * @date 2024-12-13
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class ContractSignTypeServiceImpl implements IContractSignTypeService {
	
	private final TbBusinessContractSignMappingMapper contractSignMappingMapper;
	
	@Override
	public List<ContractSignTypeResp> listContractSignType(String cityCode, String contractType) {
		LambdaQueryWrapper<TbBusinessContractSignMapping> queryWrapper = new LambdaQueryWrapper<TbBusinessContractSignMapping>();
		if (!ObjectUtils.isEmpty(cityCode)) {
			queryWrapper.eq(TbBusinessContractSignMapping::getCityCode, cityCode);
			List<TbBusinessContractSignMapping> citySignMappingList = contractSignMappingMapper.selectList(queryWrapper);
			queryWrapper.clear();
			if (CollectionUtils.isEmpty(citySignMappingList)) {
				queryWrapper.eq(TbBusinessContractSignMapping::getCityCode, "100000");
			} else {
				queryWrapper.eq(TbBusinessContractSignMapping::getCityCode, cityCode);
			}
		} 
		if (!ObjectUtils.isEmpty(contractType)) {
			queryWrapper.eq(TbBusinessContractSignMapping::getContractType, contractType);
		} 
		List<ContractSignTypeResp> contractSignTypeRespList = new ArrayList<>();
		List<TbBusinessContractSignMapping> signMappingList = contractSignMappingMapper.selectList(queryWrapper);
		Set<String> businessTypeSet = new HashSet<>();
		if (!CollectionUtils.isEmpty(signMappingList)) {
			signMappingList.stream().forEach(item -> {
				businessTypeSet.add(item.getBusinessType());
			});
			for(String businessType : businessTypeSet) {
				ContractSignTypeResp contractSignTypeResp = new ContractSignTypeResp();
				contractSignTypeResp.setSignType(businessType);
				List<SignContractType> signContractTypeList = new ArrayList<>();
				for (TbBusinessContractSignMapping contractSignMapping: signMappingList) {
					if (businessType.equals(contractSignMapping.getBusinessType())) {
						contractSignTypeResp.setSignTypeName(contractSignMapping.getBusinessTypeName());
						SignContractType signContractType = new SignContractType();
						signContractType.setSignContractTemplateId(contractSignMapping.getTemplateId());
						signContractType.setSignContractTemplateCode(contractSignMapping.getTemplateCode());
						signContractType.setSignContractType(contractSignMapping.getContractType());
						signContractType.setSignContractTypeName(contractSignMapping.getContractTypeName());
						signContractTypeList.add(signContractType);
					}
				}
				contractSignTypeResp.setSignContractTypeList(signContractTypeList);
				contractSignTypeRespList.add(contractSignTypeResp);
			}
		}
		return contractSignTypeRespList;
	}

	@Override
	public List<ContractTypeDropdownVo> listContractTypeDropdown(String cityCode, boolean isConcat) {
		List<ContractTypeDropdownVo> resultVo = new ArrayList<>();
		LambdaQueryWrapper<TbBusinessContractSignMapping> queryWrapper = new LambdaQueryWrapper<TbBusinessContractSignMapping>();
		List<TbBusinessContractSignMapping> list = contractSignMappingMapper.selectList(queryWrapper);
		if (!CollectionUtils.isEmpty(list)) {
			resultVo = list.stream().map(item -> {
				ContractTypeDropdownVo vo = new ContractTypeDropdownVo();
				vo.setContractType(item.getContractType());
				vo.setContractCategory(item.getBusinessType());
				if (isConcat) {
					vo.setContractTypeName(String.join(RsmsConstant.SPLIT_CROSS, item.getContractTypeName(), item.getCityName()));
				} else {
					vo.setContractTypeName(item.getContractTypeName());
				}
				return vo;
			}).collect(Collectors.toList());
		}
		return resultVo;
	}

}
