package mixc.be.rsms.business.service;

import java.util.List;

import mixc.be.rsms.business.vo.ContractDetailVo;
import mixc.be.rsms.pojo.third.JoyContract;
import mixc.be.rsms.pojo.third.JoyCreateContractParam;
import mixc.be.rsms.pojo.third.JoySignObjectParam;

/**
 * @ClassName IBizContractService
 * @Description 合同基本信息业务接口
 * <AUTHOR>
 * @date 2025-02-18
 */
public interface IBizContractSignParamService {

	/**
	 * 组装合同签署参数
	 * @param contract 合同对象
	 * @param joyContractParam 朝昔合同参数对象
	 * @return
	 */
	List<JoySignObjectParam> assembleContractSignParam(ContractDetailVo contract, JoyCreateContractParam joyContractParam);
	
	/**
	 * 组装合同模版参数
	 * @param contract 合同对象
	 * @return
	 */
	List<JoyContract> assembleTemplateParam(ContractDetailVo contract);
	
}
