package mixc.be.rsms.business.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.enums.BusinessEnum;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.feignclient.AuthorServerClient;
import mixc.be.rsms.business.common.feignclient.DataServerClient;
import mixc.be.rsms.business.convert.OwnershipTransferConvert;
import mixc.be.rsms.business.domain.dto.OwnershipTransferQueryParams;
import mixc.be.rsms.business.domain.dto.OwnershipTransferUpdateParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessContractInfo;
import mixc.be.rsms.business.domain.pojo.TbBusinessOwnershipTransfer;
import mixc.be.rsms.business.mapper.TbBusinessContractInfoMapper;
import mixc.be.rsms.business.mapper.TbBusinessOwnershipTransferMapper;
import mixc.be.rsms.business.service.IOwnershipTransferService;
import mixc.be.rsms.business.vo.HouseInfoVo;
import mixc.be.rsms.business.vo.OwnershipTransferVo;
import mixc.be.rsms.common.constant.RsmsConstant;
import mixc.be.rsms.common.enums.CommonEnum;
import mixc.be.rsms.common.enums.DropdownEnum;
import mixc.be.rsms.common.enums.PermissionEnum;
import mixc.be.rsms.common.utils.PersonDataEncryptUtil;
import mixc.be.rsms.common.utils.SnowFlake;
import mixc.be.rsms.pojo.business.OwnershipTransferResp;
import mixc.be.rsms.pojo.data.CustomerSourceResp;
import mixc.be.rsms.pojo.data.ParkingSpaceResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 权证过户业务实现类
 * <AUTHOR>
 * @date 2024-10-20
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class OwnershipTransferServiceImpl implements IOwnershipTransferService {

	private final TbBusinessOwnershipTransferMapper transferMapper;
	private final OwnershipTransferConvert convert;
	private final SnowFlake snowFlake;
	private final AuthorServerClient authorServerClient;
	private final DataServerClient dataServerClient;
	private final TbBusinessContractInfoMapper contractInfoMapper;
	
	@Override
	public void saveOrUpdate(OwnershipTransferUpdateParams params) {
		if (ObjectUtils.isEmpty(params)) {
			throw new BusinessException(CommonEnum.REQUEST_OBJ_NOT_EXIST);
		}
		TbBusinessOwnershipTransfer ownershipTransfer = convert.convert(params);
		if (ObjectUtils.isEmpty(ownershipTransfer.getId())) {
			ownershipTransfer.setId(snowFlake.nextId());
			ownershipTransfer.setCreateUser(StpUtil.getLoginIdAsLong());
			//新增
			transferMapper.insert(ownershipTransfer);
		} else {
			TbBusinessOwnershipTransfer queryTransfer = transferMapper.selectById(ownershipTransfer.getId());
			if (ObjectUtils.isEmpty(queryTransfer)) {
				throw new BusinessException(CommonEnum.REQUEST_OBJ_NOT_EXIST);
			}
			String currentTransferStage = params.getCurrentTransferStage();
			String nextTransferStage = params.getNextTransferStage();
			if (DropdownEnum.TRANSFER_STAGE_TS16.getDictKey().equals(currentTransferStage) && !ObjectUtils.isEmpty(nextTransferStage)) {
				throw new BusinessException(BusinessEnum.TRANSFER_STATUS_S16_ERROR);
			}
			queryTransfer.setExpectTransferDate(params.getExpectTransferDate());
			queryTransfer.setCurrentTransferStage(params.getCurrentTransferStage());
			queryTransfer.setCurrentStageFinishDate(params.getCurrentStageFinishDate());
			queryTransfer.setNextTransferStage(params.getNextTransferStage());
			queryTransfer.setRemarks(params.getRemarks());
			queryTransfer.setUpdateUser(StpUtil.getLoginIdAsLong());
			transferMapper.updateById(queryTransfer);
		}
	}

	@Override
	public IPage<OwnershipTransferVo> pageOwnershipTransfer(OwnershipTransferQueryParams queryParams) {
		if (ObjectUtils.isEmpty(queryParams)) {
			throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
		}
		if (queryParams.getPageNum() < 1 || queryParams.getPageSize() < 1) {
            throw new BusinessException(CommonEnum.PAGE_PARAMS_ERROR);
        }
		List<Long> permissionViewUserIdList = authorServerClient.getUserPermissionByCode(
				PermissionEnum.TRANSACTION_MANAGE_OWNERSHIP_TRANSFER_VIEW.getCode());
		String permissionEditCode = PermissionEnum.TRANSACTION_MANAGE_OWNERSHIP_TRANSFER_EDIT.getCode();
		List<Long> permissionEditUserIdList = authorServerClient.getUserPermissionByCode(permissionEditCode);
		Long loginUserId = StpUtil.getLoginIdAsLong();
		if (!CollectionUtils.isEmpty(permissionViewUserIdList)) {
			if (permissionViewUserIdList.get(0).longValue() == RsmsConstant.PERMISSION_FOR_FORBIDDEN) {
				log.info("当前用户：{} 查看权证过户列表权限为：{} 禁止，直接返回", loginUserId, permissionViewUserIdList);
				return new Page<OwnershipTransferVo>();
			}
			if (permissionViewUserIdList.get(0).longValue() == RsmsConstant.PERMISSION_FOR_ALL) {
				log.info("当前用户：{} 查看权证过户列表权限为：{} 所有，置空查询索引字段条件", loginUserId, permissionViewUserIdList);
				permissionViewUserIdList = new ArrayList<>();
			}
		}
		List<Long> keyWordHouseIds = null;
		if (!ObjectUtils.isEmpty(queryParams.getHouseName())) {
			keyWordHouseIds = dataServerClient.getHouseIdByCommunityKeyWord(queryParams.getHouseName());
			Map<Long, HouseInfoVo> keyWordHouseDetailMap = dataServerClient.listHouseInfoByIds(keyWordHouseIds).stream()
					.collect(Collectors.toMap(HouseInfoVo::getId, Function.identity(), (existing, next) -> existing));
		}
		List<Long> customerIds = null;
		if (!ObjectUtils.isEmpty(queryParams.getCustomerName())) {
			customerIds = dataServerClient.innerQueryCustomerByName(queryParams.getCustomerName())
					.stream()
					.map(CustomerSourceResp::getId).toList();
		}
		IPage<TbBusinessOwnershipTransfer> ownershipTransferIPage = transferMapper.pageOwnershipTransfer(
				new Page<OwnershipTransferQueryParams>(queryParams.getPageNum(), queryParams.getPageSize()), queryParams, permissionViewUserIdList, keyWordHouseIds, customerIds);
		// 查询房源、客源相关信息
		Map<Long, HouseInfoVo> houseInfoVoMap = null;
		Map<Long, ParkingSpaceResp> parkingSpaceRespMap = null;
		Map<Long, CustomerSourceResp> customerSourceRespMap = null;
		if (!ObjectUtils.isEmpty(ownershipTransferIPage.getRecords())) {
			List<Long> list = ownershipTransferIPage.getRecords().stream()
					.map(TbBusinessOwnershipTransfer::getHouseId)
					.toList();
			houseInfoVoMap = dataServerClient.listHouseInfoByIds(list).stream()
					.collect(Collectors.toMap(HouseInfoVo::getId, Function.identity(), (existing, next) -> existing));
			List<Long> parkingSpaceIdList = ownershipTransferIPage.getRecords().stream()
					.map(TbBusinessOwnershipTransfer::getParkingSpaceId)
					.toList();
			parkingSpaceRespMap = dataServerClient.getParkingSpaceByIdList(parkingSpaceIdList)
					.stream()
					.collect(Collectors.toMap(ParkingSpaceResp::getId, Function.identity(), (existing, next) -> existing));
			List<Long> customerList = ownershipTransferIPage.getRecords().stream()
					.map(TbBusinessOwnershipTransfer::getCustomerId)
					.toList();
			customerSourceRespMap = dataServerClient.innerQueryCustomerByIdList(customerList).stream()
					.collect(Collectors.toMap(CustomerSourceResp::getId, Function.identity(), (existing, next) -> existing));
		}
		List<OwnershipTransferVo> response = new LinkedList<>();
		for (TbBusinessOwnershipTransfer item : ownershipTransferIPage.getRecords()) {
			OwnershipTransferVo ownershipTransferVo = setHouseCustomerMsg(item, houseInfoVoMap, parkingSpaceRespMap, customerSourceRespMap);
			if (!CollectionUtils.isEmpty(permissionEditUserIdList)) {
				if (permissionEditUserIdList.getFirst() == RsmsConstant.PERMISSION_FOR_ALL) {
					ownershipTransferVo.setPermissions(List.of(permissionEditCode));
				} else if (permissionEditUserIdList.contains(ownershipTransferVo.getCreateUser())) {
					ownershipTransferVo.setPermissions(List.of(permissionEditCode));
				}
			}
			response.add(ownershipTransferVo);
		}
		IPage<OwnershipTransferVo> page = new Page<>(ownershipTransferIPage.getCurrent(), ownershipTransferIPage.getSize(), ownershipTransferIPage.getTotal());
		page.setRecords(response);
		return page;
	}

	/**
	 * 填充房源、客源相关信息
	 * @param ownershipTransferIPage
	 * @param houseInfoVoMap
	 * @param customerSourceRespMap
	 * @return
	 */
	private OwnershipTransferVo setHouseCustomerMsg(TbBusinessOwnershipTransfer ownershipTransferIPage,
													Map<Long, HouseInfoVo> houseInfoVoMap,
													Map<Long, ParkingSpaceResp> parkingSpaceRespMap,
													Map<Long, CustomerSourceResp> customerSourceRespMap) {
		OwnershipTransferVo ownershipTransferVo = convert.dataToVo(ownershipTransferIPage);
		if (!ObjectUtils.isEmpty(ownershipTransferIPage.getHouseId())
				&& !ObjectUtils.isEmpty(houseInfoVoMap)
				&& !ObjectUtils.isEmpty(houseInfoVoMap.get(ownershipTransferIPage.getHouseId()))) {
			StringBuilder houseName = new StringBuilder();
			HouseInfoVo houseInfoVo = houseInfoVoMap.get(ownershipTransferIPage.getHouseId());
			houseName.append(houseInfoVo.getLocation())
					.append("|")
					.append(houseInfoVo.getHouseNumber())
					.append("|")
					.append(houseInfoVo.getCommunityName());
			ownershipTransferVo.setHouseName(houseName.toString());
		}
		if (!ObjectUtils.isEmpty(ownershipTransferIPage.getParkingSpaceId())
				&& !ObjectUtils.isEmpty(parkingSpaceRespMap)
				&& !ObjectUtils.isEmpty(parkingSpaceRespMap.get(ownershipTransferIPage.getParkingSpaceId()))) {
			ParkingSpaceResp parkingSpaceResp = parkingSpaceRespMap.get(ownershipTransferIPage.getParkingSpaceId());
			StringBuilder parkingSpaceName = new StringBuilder();
			parkingSpaceName.append(parkingSpaceResp.getName())
					.append("|")
					.append(parkingSpaceResp.getNumber())
					.append("|")
					.append(parkingSpaceResp.getCode());
			ownershipTransferVo.setParkingSpaceName(parkingSpaceName.toString());
		}
		StringBuilder customerName = new StringBuilder();
		if (!ObjectUtils.isEmpty(customerSourceRespMap.get(ownershipTransferIPage.getCustomerId()))) {
			CustomerSourceResp customerSourceResp = customerSourceRespMap.get(ownershipTransferIPage.getCustomerId());
			customerName.append(customerSourceResp.getName())
					.append("|")
					.append(PersonDataEncryptUtil.mobileEncrypt(customerSourceResp.getMobile()));
		}
		ownershipTransferVo.setCustomerName(customerName.toString());
		return ownershipTransferVo;

	}

	@Override
	public OwnershipTransferVo queryOwnershipTransferDetail(Long id) {
		if (ObjectUtils.isEmpty(id)) {
			throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
		}
		TbBusinessOwnershipTransfer tbBusinessOwnershipTransfer = transferMapper.selectById(id);
		Map<Long, HouseInfoVo> houseInfoVoMap = null;
		if (!ObjectUtils.isEmpty(tbBusinessOwnershipTransfer.getHouseId())) {
			houseInfoVoMap = dataServerClient.listHouseInfoByIds(List.of(tbBusinessOwnershipTransfer.getHouseId())).stream()
					.collect(Collectors.toMap(HouseInfoVo::getId, Function.identity(), (existing, next) -> existing));
		}
		Map<Long, ParkingSpaceResp> parkingSpaceRespMap = null;
		if (!ObjectUtils.isEmpty(tbBusinessOwnershipTransfer.getParkingSpaceId())) {
			parkingSpaceRespMap = dataServerClient.getParkingSpaceByIdList(List.of(tbBusinessOwnershipTransfer.getParkingSpaceId()))
					.stream()
					.collect(Collectors.toMap(ParkingSpaceResp::getId, Function.identity(), (existing, next) -> existing));
		}
		Map<Long, CustomerSourceResp> customerSourceRespMap = dataServerClient.innerQueryCustomerByIdList(List.of(tbBusinessOwnershipTransfer.getCustomerId())).stream()
				.collect(Collectors.toMap(CustomerSourceResp::getId, Function.identity(), (existing, next) -> existing));
		return setHouseCustomerMsg(tbBusinessOwnershipTransfer, houseInfoVoMap, parkingSpaceRespMap, customerSourceRespMap);
	}

	@Override
	public List<OwnershipTransferResp> mobileGetDetail(List<String> transactionContractNums) {
		if (ObjectUtils.isEmpty(transactionContractNums)) {
			return List.of();
		}
		LambdaQueryWrapper<TbBusinessOwnershipTransfer> wrapper = Wrappers.lambdaQuery(TbBusinessOwnershipTransfer.class);
		wrapper.nested(i -> {
			transactionContractNums
					.forEach(s -> {
						i.like(TbBusinessOwnershipTransfer::getTransferContractNumber, s)
								.or();
					});
			i.eq(TbBusinessOwnershipTransfer::getId, 0);
		});
		return transferMapper.selectList(wrapper)
				.stream()
				.map(convert::data2Resp)
				.toList();
	}

	@Override
	public IPage<OwnershipTransferVo> pageOwnershipByMobile(String mobile, Integer pageNum, Integer pageSize) {
		if (ObjectUtils.isEmpty(mobile)) {
			throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
		}
		if (pageNum < 1 || pageSize < 1) {
			throw new BusinessException(CommonEnum.PAGE_PARAMS_ERROR);
		}

		// 先查询到成交报告中 该手机号作为 客户或者业主的成交报告ID
		LambdaQueryWrapper<TbBusinessContractInfo> like = Wrappers.lambdaQuery(TbBusinessContractInfo.class)
				.like(TbBusinessContractInfo::getCustomerMobile, mobile)
				.or()
				.like(TbBusinessContractInfo::getOwnerMobile, mobile);
		List<Long> reportIdList = contractInfoMapper.selectList(like).stream()
				.map(TbBusinessContractInfo::getReportId).toList();

		if (ObjectUtils.isEmpty(reportIdList)) {
			Page<OwnershipTransferVo> transferVoPage = new Page<>(pageNum, pageSize, 0);
			transferVoPage.setRecords(List.of());
			return transferVoPage;
		}
		// 拿着成交报告Id 去权证过户表查询记录
		IPage<TbBusinessOwnershipTransfer> page = new Page<>(pageNum, pageSize);
		LambdaQueryWrapper<TbBusinessOwnershipTransfer> in = Wrappers.lambdaQuery(TbBusinessOwnershipTransfer.class)
				.in(TbBusinessOwnershipTransfer::getTransactionReportId, reportIdList)
				.orderByDesc(TbBusinessOwnershipTransfer::getCreateTime);
		IPage<TbBusinessOwnershipTransfer> selectPage = transferMapper.selectPage(page, in);
		List<OwnershipTransferVo> list = selectPage.getRecords().stream().map(convert::dataToVo).toList();

		IPage<OwnershipTransferVo> resultPage = new Page<>(selectPage.getCurrent(), selectPage.getSize(), selectPage.getTotal());
		resultPage.setRecords(list);
		return resultPage;
	}
}
