package mixc.be.rsms.business.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionInfo;
import mixc.be.rsms.business.mapper.TbBusinessTransactionInfoMapper;
import mixc.be.rsms.business.service.ITransactionInfoService;
import mixc.be.rsms.common.constant.RsmsConstant;
import mixc.be.rsms.common.enums.CommonEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <p>
 * 交易信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class TransactionInfoServiceImpl extends ServiceImpl<TbBusinessTransactionInfoMapper, TbBusinessTransactionInfo> implements ITransactionInfoService {

    private final TbBusinessTransactionInfoMapper transactionInfoMapper;

    @Override
    public Integer deleteById(Long id) {
        TbBusinessTransactionInfo transactionInfo = getById(id);
        transactionInfo.setUpdateUser(StpUtil.getLoginIdAsLong());
        LambdaUpdateWrapper<TbBusinessTransactionInfo> updateWrapper = Wrappers.lambdaUpdate(TbBusinessTransactionInfo.class)
                .eq(TbBusinessTransactionInfo::getId, transactionInfo.getId())
                .set(TbBusinessTransactionInfo::getIsDeleted, RsmsConstant.DEL_FLAG_YES)
                .set(TbBusinessTransactionInfo::getUpdateUser, StpUtil.getLoginIdAsLong());
        return transactionInfoMapper.update(updateWrapper);
    }

    private TbBusinessTransactionInfo getById(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
        }
        TbBusinessTransactionInfo tbBusinessTransactionInfo = transactionInfoMapper.selectById(id);
        if (ObjectUtils.isEmpty(tbBusinessTransactionInfo)) {
            throw new BusinessException(CommonEnum.REQUEST_OBJ_NOT_EXIST);
        }
        return tbBusinessTransactionInfo;
    }
}
