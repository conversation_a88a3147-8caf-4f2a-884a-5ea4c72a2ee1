package mixc.be.rsms.business.service;

import java.util.List;

import mixc.be.rsms.business.vo.ContractTypeDropdownVo;
import mixc.be.rsms.pojo.business.ContractSignTypeResp;

/**
 * @ClassName IDataHouseService
 * @Description 房源业务接口
 * <AUTHOR>
 * @date 2024-09-13
 */
public interface IContractSignTypeService {

	/**
	 * 根据城市编码获取合同签约类型列表
	 * @param cityCode 城市编码
	 * @param contractType 合同类型
	 * @return
	 */
	List<ContractSignTypeResp> listContractSignType(String cityCode, String contractType);
	
	/**
	 * @param cityCode 城市定位编码
	 * @param isConcat 是否拼接
	 * @return
	 */
	List<ContractTypeDropdownVo> listContractTypeDropdown(String cityCode, boolean isConcat);
	
	
}
