package mixc.be.rsms.business.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.annotation.OperateLog;
import mixc.be.rsms.business.common.config.TransactionCostItemConfig;
import mixc.be.rsms.business.common.enums.BusinessEnum;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.feignclient.AuthorServerClient;
import mixc.be.rsms.business.common.feignclient.DataServerClient;
import mixc.be.rsms.business.convert.*;
import mixc.be.rsms.business.domain.dto.*;
import mixc.be.rsms.business.domain.pojo.*;
import mixc.be.rsms.business.mapper.*;
import mixc.be.rsms.business.service.IDepositService;
import mixc.be.rsms.business.service.IOrderService;
import mixc.be.rsms.business.service.ITransactionReportService;
import mixc.be.rsms.business.utils.PermissionUtil;
import mixc.be.rsms.business.vo.*;
import mixc.be.rsms.common.constant.RsmsConstant;
import mixc.be.rsms.common.enums.*;
import mixc.be.rsms.common.utils.*;
import mixc.be.rsms.pojo.author.UserDetailsMicroResp;
import mixc.be.rsms.pojo.business.TransactionReportResp;
import mixc.be.rsms.pojo.data.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 成交报告表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class TransactionReportServiceImpl implements ITransactionReportService {

    private final SnowFlake snowFlake;

    private final FlowNumberUtil flowNumberUtil;

    private final TbBusinessTransactionReportMapper transactionReportMapper;

    private final TbBusinessContractInfoMapper contractInfoMapper;

    private final TbBusinessReceiveCommissionPlanMapper receiveCommissionPlanMapper;

    private final IDepositService depositService;

    private final TbBusinessDepositMapper depositMapper;

    private final TbBusinessTransactionInfoMapper transactionInfoMapper;

    private final TbBusinessOwnershipTransferMapper ownershipTransferMapper;

    private final TbBusinessContractMapper bizContractMapper;

    private final DataServerClient dataServerClient;

    private final DataTransactionReportConvert reportConvert;

    private final DataReceiveCommissionPlanConvert planConvert;

    private final DataContractInfoConvert contractInfoConvert;

    private final DataDepositConvert depositConvert;

    private final OwnershipTransferConvert ownershipTransferConvert;

    private final DataParkingSpaceConvert parkingSpaceConvert;

    private final DataFileInfoConvert fileInfoConvert;

    private final AuthorServerClient authorServerClient;

    private final IOrderService orderService;
    private final TbBusinessContractSignMappingMapper contractSignMappingMapper;

    private final TransactionCostItemConfig transactionCostItemConfig;


    @Value("${sf.common.new_community.code}")
    private String newCommunityCode;

    @Override
    public IPage<TransactionReportVo> page(TransactionReportQueryParams params) {
        if (ObjectUtils.isEmpty(params)) {
            throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
        }
        if (params.getPageNum() < 1 || params.getPageSize() < 1) {
            throw new BusinessException(CommonEnum.PAGE_PARAMS_ERROR);
        }
        paramHandle(params);

        // 用户名模糊查询无数据，返回空
        if (!ObjectUtils.isEmpty(params.getCustomerNameKeyWord()) && ObjectUtils.isEmpty(params.getCustomerIdList())) {
            Page<TransactionReportVo> transactionReportVoPage = new Page<>(params.getPageNum(), params.getPageSize(), 0);
            transactionReportVoPage.setRecords(List.of());
            return transactionReportVoPage;
        }

        // 房源关键字不为空，但是查询到的房源ID 为空，则表示无数据
        if (!ObjectUtils.isEmpty(params.getKeyword())
                && ObjectUtils.isEmpty(params.getHouseIdList())
                && ObjectUtils.isEmpty(params.getParkingSpaceIdList())) {
            Page<TransactionReportVo> transactionReportVoPage = new Page<>(params.getPageNum(), params.getPageSize(), 0);
            transactionReportVoPage.setRecords(List.of());
            return transactionReportVoPage;
        }
        // 异步查询对应权限码的 用户ID
        CompletableFuture<Map<String, List<Long>>> supplyAsync = CompletableFuture.supplyAsync(() -> {
            return PermissionUtil.syncGetUserIdByPermissionCode(List.of(
                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_VIEW.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_EDIT.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_DELETE.getCode(),

                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_AUDIT_OR_REJECT.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVERSE_AUDIT.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVIEW_OR_REJECT.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_CLOSE.getCode()
            ));
        });

        List<Long> permissionUserIdList = PermissionUtil.getUserIdListByPermissionCode(List.of(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_VIEW.getCode()))
                .getOrDefault(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_VIEW.getCode(), new LinkedList<>())
                .stream()
                .distinct()
                .toList();
        Page<TbBusinessTransactionReport> page = new Page<>(params.getPageNum(), params.getPageSize());
        IPage<TbBusinessTransactionReport> reportPage = transactionReportMapper.selectByEntity(page, params, permissionUserIdList);

        if (ObjectUtils.isEmpty(reportPage.getRecords())) {
            Page<TransactionReportVo> reportVoPage = new Page<>(reportPage.getCurrent(), reportPage.getSize(), reportPage.getTotal());
            reportVoPage.setRecords(List.of());
            return reportVoPage;
        }

        List<TbBusinessTransactionReport> records = reportPage.getRecords();
        List<Long> houseIdList = records.stream()
                .map(TbBusinessTransactionReport::getHouseId)
                .filter(Objects::nonNull)
                .toList();
        Map<Long, HouseInfoVo> houseInfoVoMap = dataServerClient.listHouseInfoByIds(houseIdList).stream()
                .collect(Collectors.toMap(HouseInfoVo::getId, Function.identity(), (existing, next) -> existing));

        List<Long> parkingSpaceIdList = records.stream()
                .map(TbBusinessTransactionReport::getParkingSpaceId)
                .filter(Objects::nonNull)
                .toList();
        Map<Long, ParkingSpaceVo> parkingSpaceRespMap = dataServerClient.getParkingSpaceByIdList(parkingSpaceIdList)
                .stream()
                .map(parkingSpaceConvert::resp2Vo)
                .collect(Collectors.toMap(ParkingSpaceVo::getId, Function.identity(), (existing, next) -> existing));

        Map<Long, CustomerSourceResp> customerNameMap = dataServerClient.innerQueryCustomerByIdList(records.stream().map(TbBusinessTransactionReport::getCustomerId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(CustomerSourceResp::getId, Function.identity(), (existing, next) -> existing));

        Map<Long, NewHouseResp> newHouseRespMap = dataServerClient.getNewHouseByIdList(records.stream()
                        .map(TbBusinessTransactionReport::getNewHouseCommunityId)
                        .filter(Objects::nonNull)
                        .toList())
                .stream()
                .collect(Collectors.toMap(NewHouseResp::getId, Function.identity(), (existing, next) -> existing));

        Map<Long, UserDetailsMicroResp> brokerDetailMap = new HashMap<>();
        List<Long> brokerIdList = records.stream().map(TbBusinessTransactionReport::getBrokerId).filter(Objects::nonNull).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(brokerIdList)) {
            brokerDetailMap.putAll(authorServerClient.getUserDetailsMicro(brokerIdList)
                    .stream()
                    .collect(Collectors.toMap(UserDetailsMicroResp::getId, Function.identity(), (existing, next) -> existing)));
        }
        Map<String, List<Long>> permissionCodeMap = supplyAsync.join();

        List<TransactionReportVo> list = records.stream().map(item -> {
                    TransactionReportVo reportVo = reportConvert.dataToVo(item);
                    setResultVo(reportVo, item, houseInfoVoMap, parkingSpaceRespMap, newHouseRespMap, customerNameMap, brokerDetailMap, permissionCodeMap);
                    return reportVo;
                })
                .toList();
        Page<TransactionReportVo> reportVoPage = new Page<>(reportPage.getCurrent(), reportPage.getSize(), reportPage.getTotal());
        reportVoPage.setRecords(list);
        return reportVoPage;
    }

    /**
     * 查询参数处理
     * @param params
     */
    private void paramHandle(TransactionReportQueryParams params) {
        // 客户ID 模糊查询
        if (!ObjectUtils.isEmpty(params.getCustomerNameKeyWord())) {

            List<Long> list = dataServerClient.innerQueryCustomerByName(params.getCustomerNameKeyWord())
                    .stream()
                    .map(CustomerSourceResp::getId)
                    .toList();
            params.setCustomerIdList(list);
        }
        // 房源关键字模糊查询、车位关键字模糊查询
        if (!ObjectUtils.isEmpty(params.getKeyword())) {
            List<Long> houseIdByCommunityKeyWord = dataServerClient.getHouseIdByCommunityKeyWord(params.getKeyword());
            params.setHouseIdList(houseIdByCommunityKeyWord);

            List<Long> parkingSpaceId = dataServerClient.getParkingSpaceIdByCommunityKeyWord(params.getKeyword());
            params.setParkingSpaceIdList(parkingSpaceId);
        }
    }

    /**
     * 设置返回前端的 成交报告信息
     * @param reportVo 返回前端的成交报告对象
     * @param item  后端对象
     * @param houseInfoVoMap
     * @param parkingSpaceRespMap
     * @param newHouseRespMap
     * @param customerNameMap
     * @param finalBrokerDetailMap
     * @param permissionCodeMap
     */
    private void setResultVo(TransactionReportVo reportVo, TbBusinessTransactionReport item,
                             Map<Long, HouseInfoVo> houseInfoVoMap,
                             Map<Long, ParkingSpaceVo> parkingSpaceRespMap,
                             Map<Long, NewHouseResp> newHouseRespMap,
                             Map<Long, CustomerSourceResp> customerNameMap,
                             Map<Long, UserDetailsMicroResp> finalBrokerDetailMap,
                             Map<String, List<Long>> permissionCodeMap) {
        if (!ObjectUtils.isEmpty(item.getHouseId())) {
            HouseInfoVo houseInfoVo = houseInfoVoMap.get(item.getHouseId());
            reportVo.setHouseInfo(houseInfoVo);
        }
        if (!ObjectUtils.isEmpty(item.getParkingSpaceId())) {
            ParkingSpaceVo parkingSpaceVo = parkingSpaceRespMap.get(item.getParkingSpaceId());
            reportVo.setParkingSpaceInfo(parkingSpaceVo);
        }
        if (!ObjectUtils.isEmpty(item.getNewHouseCommunityId())) {
            NewHouseResp newHouseResp = newHouseRespMap.get(item.getNewHouseCommunityId());
            if (!ObjectUtils.isEmpty(newHouseResp)) {
                reportVo.setNewHouseCommunityName(newHouseResp.getCommunityName());
            }
        }
        // 成交报告状态为 已收款、已关闭 时，才会有 收起日期
        if (ObjectUtils.isEmpty(reportVo.getStatus()) ||
                (!DropdownEnum.TRANSFER_STATUS_CLOSED.getDictKey().equals(reportVo.getStatus())
                        && !DropdownEnum.TRANSFER_STATUS_PAYED.getDictKey().equals(reportVo.getStatus()))) {
            reportVo.setCollectAllDate(null);
        }
        if (!ObjectUtils.isEmpty(customerNameMap.get(reportVo.getCustomerId()))) {
            reportVo.setCustomerName(customerNameMap.get(reportVo.getCustomerId()).getName());
            reportVo.setCustomerMobile(PersonDataEncryptUtil.mobileEncrypt(customerNameMap.get(reportVo.getCustomerId()).getMobile()));
            reportVo.setCustomerMobileEncode(Base64Util.encodeData(customerNameMap.get(reportVo.getCustomerId()).getMobile()));
        }
        if (!ObjectUtils.isEmpty(finalBrokerDetailMap.get(reportVo.getBrokerId()))) {
            reportVo.setBrokerName(finalBrokerDetailMap.get(reportVo.getBrokerId()).getEmpName());
            reportVo.setBrokerMobile(PersonDataEncryptUtil.mobileEncrypt(finalBrokerDetailMap.get(reportVo.getBrokerId()).getMobile()));
            reportVo.setBrokerMobileEncode(Base64Util.encodeData(finalBrokerDetailMap.get(reportVo.getBrokerId()).getMobile()));
            reportVo.setBrokerStore(finalBrokerDetailMap.get(reportVo.getBrokerId()).getStoreName());
        }

        reportVo.setPermissions(givePermissions(permissionCodeMap, item, item.getStatus()));
    }

    @Override
    @OperateLog(operateModule = "log_transaction", operateType = "delete")
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteById(Long id) {
        TbBusinessTransactionReport tbBusinessTransactionReport = getById(id);
        List<OperateLogResp> operateLogResps = dataServerClient.queryOperateLogByBusinessId(List.of(tbBusinessTransactionReport.getId()))
                .stream()
                .collect(Collectors.groupingBy(OperateLogResp::getBusinessId))
                .get(tbBusinessTransactionReport.getId());
        if (!ObjectUtils.isEmpty(operateLogResps)) {
            boolean allMatch = operateLogResps.stream().allMatch(item -> DropdownEnum.OPERATE_LOG_OPERATE_TYPE_ADD.getDictKey().equals(item.getOperateType()));
            if (!allMatch) {
                throw new BusinessException(BusinessEnum.TRANSACTION_REPORT_DELETE_ERROR);
            }
        }
        // 只有新增记录，则允许删除
        LambdaUpdateWrapper<TbBusinessTransactionReport> set = Wrappers.lambdaUpdate(TbBusinessTransactionReport.class)
                .eq(TbBusinessTransactionReport::getId, tbBusinessTransactionReport.getId())
                .set(TbBusinessTransactionReport::getIsDeleted, RsmsConstant.DEL_FLAG_YES)
                .set(TbBusinessTransactionReport::getUpdateUser, StpUtil.getLoginIdAsLong());
        int update = transactionReportMapper.update(set);
        deleteRelevantInfo(id, true);

        LambdaUpdateWrapper<TbBusinessContractInfo> sets = Wrappers.lambdaUpdate(TbBusinessContractInfo.class)
                .eq(TbBusinessContractInfo::getReportId, id)
                .eq(TbBusinessContractInfo::getIsDeleted, RsmsConstant.DEL_FLAG_NO)
                .set(TbBusinessContractInfo::getIsDeleted, RsmsConstant.DEL_FLAG_YES)
                .set(TbBusinessContractInfo::getUpdateUser, StpUtil.getLoginIdAsLong());
        contractInfoMapper.update(sets);

        LambdaUpdateWrapper<TbBusinessOwnershipTransfer> ownerSet = Wrappers.lambdaUpdate(TbBusinessOwnershipTransfer.class)
                .eq(TbBusinessOwnershipTransfer::getTransactionReportId, tbBusinessTransactionReport.getId())
                .eq(TbBusinessOwnershipTransfer::getIsDeleted, RsmsConstant.DEL_FLAG_NO)
                .set(TbBusinessOwnershipTransfer::getIsDeleted, RsmsConstant.DEL_FLAG_YES)
                .set(TbBusinessOwnershipTransfer::getUpdateUser, StpUtil.getLoginIdAsLong());
        ownershipTransferMapper.update(ownerSet);

        return update;
    }

    @Override
    @OperateLog(operateModule = "log_transaction", operateType = "submit")
    @Transactional(rollbackFor = Exception.class)
    public Long submit(TransactionReportEditParams params) {
        if (ObjectUtils.isEmpty(params)) {
            throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
        }
        // 参数检查
        validateParams(params.getTransactionReport());
        Long reportId = insideSaveOrUpdateTransactionReport(params.getTransactionReport());
        LambdaUpdateWrapper<TbBusinessTransactionReport> set = Wrappers.lambdaUpdate(TbBusinessTransactionReport.class)
                .eq(TbBusinessTransactionReport::getId, reportId)
                .set(TbBusinessTransactionReport::getStatus, DropdownEnum.TRANSFER_STATUS_AUDITING.getDictKey())
                .set(TbBusinessTransactionReport::getUpdateUser, StpUtil.getLoginIdAsLong());
        transactionReportMapper.update(set);
        changeHouseAndCustomerStatus(params.getTransactionReport().getHouseId(), params.getTransactionReport().getCustomerId(), params.getTransactionReport().getCategory());
        // 新增 成交报告-转佣收佣行、意向金-交易信息转佣行
        TransactionReportAddParams report = params.getTransactionReport();
        if (!ObjectUtils.isEmpty(report.getDepositId())) {
            // 处理并生成转佣收佣行
            doInsertConvertCommissionPlan(reportId,
                    report.getCategory(),
                    report.getDepositId(),
                    report.getNewHouseCommunityId(),
                    report.getParkingSpaceId(),
                    report.getHouseId(),
                    report.getTransactionContractNum());
            addConvertCommissionOnDeposit(report);
        }

        // 新增一条对应的权证过户记录
        // 如果存在对应的权证过户记录，则不做新增
        LambdaQueryWrapper<TbBusinessOwnershipTransfer> eq = Wrappers.lambdaQuery(TbBusinessOwnershipTransfer.class)
                .eq(TbBusinessOwnershipTransfer::getTransactionReportId, reportId);
        List<TbBusinessOwnershipTransfer> existOwnership = ownershipTransferMapper.selectList(eq);
        if (ObjectUtils.isEmpty(existOwnership)) {
            insertOwnership(reportId, params);
        }
        return reportId;
    }

    @Override
    @OperateLog(operateModule = "log_transaction", operateType = "close")
    @Transactional(rollbackFor = Exception.class)
    public Long close(TransactionReportEditParams params) {
        if (ObjectUtils.isEmpty(params)) {
            throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
        }
        if (ObjectUtils.isEmpty(params.getId())) {
            throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
        }
        TbBusinessTransactionReport transactionReport = getById(params.getId());
        if (!DropdownEnum.TRANSFER_STATUS_PAYED.getDictKey().equals(transactionReport.getStatus())) {
            throw new BusinessException(BusinessEnum.TRANSACTION_REPORT_STATUS_ERROR);
        }
        QueryWrapper<TbBusinessReceiveCommissionPlan> commissionPlanQueryWrapper = Wrappers.query(TbBusinessReceiveCommissionPlan.class)
                .select("sum(receivable_commission) as receivable_commission", "sum(received_commission) as received_commission")
                .eq("report_id", transactionReport.getId())
                .groupBy("report_id");
        TbBusinessReceiveCommissionPlan tbBusinessReceiveCommissionPlan = receiveCommissionPlanMapper.selectOne(commissionPlanQueryWrapper);
        // 待收佣金为0 可关闭
        // 实收 = 所有的本次收佣金额总和
        if (tbBusinessReceiveCommissionPlan.getReceivableCommission().compareTo(tbBusinessReceiveCommissionPlan.getReceivedCommission()) != 0) {
            throw new BusinessException(BusinessEnum.TRANSACTION_REPORT_CLOSE_ERROR);
        }
        // 关联的意向金【剩余金额】为0
        if (!ObjectUtils.isEmpty(transactionReport.getDepositId())) {
            QueryWrapper<TbBusinessTransactionInfo> transactionInfoQueryWrapper = Wrappers.query(TbBusinessTransactionInfo.class)
                    .select("deposit_id"
                            // 剩余金额 = 收款 - 退款 - 佣金
                            , String.format("sum( case transaction_type when '%s' then transaction_amount else transaction_amount * -1 end) as transaction_amount", DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey()))
                    .eq("deposit_id", transactionReport.getDepositId())
                    .groupBy("deposit_id");
            TbBusinessTransactionInfo tbBusinessTransactionInfo = transactionInfoMapper.selectOne(transactionInfoQueryWrapper);
            if (!ObjectUtils.isEmpty(tbBusinessTransactionInfo) && tbBusinessTransactionInfo.getTransactionAmount().compareTo(new BigDecimal("0.00")) != 0) {
                throw new BusinessException(BusinessEnum.TRANSACTION_REPORT_CLOSE_ERROR);
            }
        }
        LambdaUpdateWrapper<TbBusinessTransactionReport> set = Wrappers.lambdaUpdate(TbBusinessTransactionReport.class)
                .eq(TbBusinessTransactionReport::getId, transactionReport.getId())
                .set(TbBusinessTransactionReport::getStatus, DropdownEnum.TRANSFER_STATUS_CLOSED.getDictKey())
                .set(TbBusinessTransactionReport::getUpdateUser, StpUtil.getLoginIdAsLong());
        transactionReportMapper.update(set);

        ownershipTransferMapper.update(Wrappers.lambdaUpdate(TbBusinessOwnershipTransfer.class)
                .eq(TbBusinessOwnershipTransfer::getTransactionReportId, transactionReport.getId())
                .set(TbBusinessOwnershipTransfer::getTransferStatus, DropdownEnum.TRANSFER_STATUS_CLOSED.getDictKey()));
        return transactionReport.getId();
    }

    @Override
    @OperateLog(operateModule = "log_transaction", operateType = "audit")
    @Transactional(rollbackFor = Exception.class)
    public Long auditOrReject(TransactionReportEditParams params) {
        if (ObjectUtils.isEmpty(params)) {
            throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
        }
        if (ObjectUtils.isEmpty(params.getId())) {
            throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
        }
        TbBusinessTransactionReport transactionReport = getById(params.getId());
        if (!DropdownEnum.TRANSFER_STATUS_AUDITING.getDictKey().equals(transactionReport.getStatus())) {
            throw new BusinessException(BusinessEnum.TRANSACTION_REPORT_STATUS_ERROR);
        }
        if (ObjectUtils.isEmpty(params.getPass())) {
            throw new BusinessException(BusinessEnum.AUDIT_STATUS_ERROR);
        }
        LambdaUpdateWrapper<TbBusinessTransactionReport> set = Wrappers.lambdaUpdate(TbBusinessTransactionReport.class)
                .eq(TbBusinessTransactionReport::getId, transactionReport.getId());
        // 审核通过
        if (params.getPass()) {
            set.set(TbBusinessTransactionReport::getStatus, DropdownEnum.TRANSFER_STATUS_REVIEWING.getDictKey());
        } else { // 驳回
            set.set(TbBusinessTransactionReport::getStatus, DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey());
        }
        set.set(TbBusinessTransactionReport::getUpdateUser, StpUtil.getLoginIdAsLong());
        transactionReportMapper.update(set);

        LambdaUpdateWrapper<TbBusinessOwnershipTransfer> eq = Wrappers.lambdaUpdate(TbBusinessOwnershipTransfer.class)
                .eq(TbBusinessOwnershipTransfer::getTransactionReportId, transactionReport.getId());
        if (params.getPass()) {
            eq.set(TbBusinessOwnershipTransfer::getTransferStatus, DropdownEnum.TRANSFER_STATUS_REVIEWING.getDictKey());
        }else {
            eq.set(TbBusinessOwnershipTransfer::getTransferStatus, DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey());
        }
        ownershipTransferMapper.update(eq);
        return transactionReport.getId();
    }

    @Override
    @OperateLog(operateModule = "log_transaction", operateType = "review")
    @Transactional(rollbackFor = Exception.class)
    public Long reviewOrReject(TransactionReportEditParams params) {
        if (ObjectUtils.isEmpty(params)) {
            throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
        }
        if (ObjectUtils.isEmpty(params.getId())) {
            throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
        }
        TbBusinessTransactionReport transactionReport = getById(params.getId());
        if (!DropdownEnum.TRANSFER_STATUS_REVIEWING.getDictKey().equals(transactionReport.getStatus())) {
            throw new BusinessException(BusinessEnum.TRANSACTION_REPORT_STATUS_ERROR);
        }
        if (ObjectUtils.isEmpty(params.getPass())) {
            throw new BusinessException(BusinessEnum.AUDIT_STATUS_ERROR);
        }
        LambdaUpdateWrapper<TbBusinessTransactionReport> set = Wrappers.lambdaUpdate(TbBusinessTransactionReport.class)
                .eq(TbBusinessTransactionReport::getId, transactionReport.getId());
        // 复核通过
        if (params.getPass()) {
            set.set(TbBusinessTransactionReport::getStatus, DropdownEnum.TRANSFER_STATUS_REVIEWED.getDictKey());
        } else { // 驳回
            set.set(TbBusinessTransactionReport::getStatus, DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey());
        }
        set.set(TbBusinessTransactionReport::getUpdateUser, StpUtil.getLoginIdAsLong());
        transactionReportMapper.update(set);

        LambdaUpdateWrapper<TbBusinessOwnershipTransfer> eq = Wrappers.lambdaUpdate(TbBusinessOwnershipTransfer.class)
                .eq(TbBusinessOwnershipTransfer::getTransactionReportId, transactionReport.getId());
        if (params.getPass()) {
            eq.set(TbBusinessOwnershipTransfer::getTransferStatus, DropdownEnum.TRANSFER_STATUS_REVIEWED.getDictKey());
        }else {
            eq.set(TbBusinessOwnershipTransfer::getTransferStatus, DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey());
        }
        ownershipTransferMapper.update(eq);

        if (params.getPass()) {
            // 生成订单数据、业务数据关联订单数据、存在转佣记录时，推送收费系统
            postProcessReview(transactionReport);
        }
        return transactionReport.getId();
    }

    /**
     * 审核通过后的后置处理：订单数据的生成、业务数据关联生辰的订单数据
     * @param transactionReport
     */
    private void postProcessReview(TbBusinessTransactionReport transactionReport) {
        List<TbBusinessReceiveCommissionPlan> commissionPlans = receiveCommissionPlanMapper.selectList(Wrappers.lambdaQuery(TbBusinessReceiveCommissionPlan.class)
                .eq(TbBusinessReceiveCommissionPlan::getReportId, transactionReport.getId()));
        // 根据交费人不同，获取到对应的主订单号
        Map<String, Long> payerOrderMap = commissionPlans.stream()
                .filter(item -> !ObjectUtils.isEmpty(item.getOrderId()))
                .collect(Collectors.toMap(TbBusinessReceiveCommissionPlan::getPayer, TbBusinessReceiveCommissionPlan::getOrderId, (existing, next) -> existing));

        // 本次提交需要生成的 收款子订单
        Map<String, List<SubOrderReceiveAddParams>> subOrderMap = new HashMap<>(Map.of(
                DropdownEnum.TRANSACTION_PAYER_CUSTOMER.getDictKey(),
                new LinkedList<>(),
                DropdownEnum.TRANSACTION_PAYER_OWNER.getDictKey(),
                new LinkedList<>(),
                DropdownEnum.TRANSACTION_PAYER_OTHER.getDictKey(),
                new LinkedList<>()
        ));
        commissionPlans.stream()
                // 意向金ID 为空，则表示为 收款订单
                .filter(item -> ObjectUtils.isEmpty(item.getOrderId()))
                .filter(item -> ObjectUtils.isEmpty(item.getDepositId()))
                .forEach(plan -> {
                    // 获取到 交费人的 收款子订单列表
                    List<SubOrderReceiveAddParams> subOrder = subOrderMap.get(plan.getPayer());
                    subOrder.add(generateSubOrderReceiveAddParams(plan, plan.getReceivableCommission(), null));
                    subOrderMap.put(plan.getPayer(), subOrder);
                });
        // 客户的转佣子订单
        List<SubOrderCommissionAddParams> commissionAddParamsList = new LinkedList<>();
        if (!ObjectUtils.isEmpty(transactionReport.getDepositId())) {
            // 转佣行仅会存在一笔，故只有第一次审核通过生成订单时，会进行转佣子订单的生成
            Optional<TbBusinessReceiveCommissionPlan> any = commissionPlans.stream()
                    .filter(plan -> !ObjectUtils.isEmpty(plan.getOrderStatus()))
                    .findAny();
            // 未查询到有订单状态的 收佣行，标识未生成过订单
            if (any.isEmpty()) {
                // 意向金 收款行只会存在一条收款记录
                List<TbBusinessTransactionInfo> receiveInfos = transactionInfoMapper.selectList(Wrappers.lambdaQuery(TbBusinessTransactionInfo.class)
                        .eq(TbBusinessTransactionInfo::getDepositId, transactionReport.getDepositId())
                        .eq(TbBusinessTransactionInfo::getOrderStatus, DropdownEnum.TRANSACTION_ORDER_STATUS_PAYED.getDictKey())
                        .eq(TbBusinessTransactionInfo::getTransactionType, DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey()));
                if (ObjectUtils.isEmpty(receiveInfos)) {
                    throw new BusinessException(BusinessEnum.DEPOSIT_STATUS_ERROR);
                }
                BigDecimal available = receiveInfos.getFirst().getTransactionAmount();
                // 如果可用余额大于 转佣金额 ，则 仅转佣部分金额，剩余意向金收款金额 由用户发起退款，否则，全部意向金转佣
                SubOrderCommissionAddParams commissionAddParams = generateCommissionAddParam(receiveInfos.getFirst(),
                        available.compareTo(transactionReport.getCustomerCommission()) > 0 ? transactionReport.getCustomerCommission() : available);
                // 转佣记录行的 业务ID
                commissionPlans.stream()
                        .filter(item -> DropdownEnum.TRANSACTION_PAYER_CUSTOMER.getDictKey().equals(item.getPayer()))
                        .filter(item -> !ObjectUtils.isEmpty(item.getDepositId()))
                        .findAny()
                        .ifPresent(yxj -> commissionAddParams.setBusinessId(yxj.getId()));
                commissionAddParamsList.add(commissionAddParams);
            }
        }

        subOrderMap.keySet().forEach(item -> {
            // 存在主订单，则仅新增子订单
            if (!ObjectUtils.isEmpty(payerOrderMap.get(item))) {
                // 交费人为客户的时候，存在转佣记录
                if (DropdownEnum.TRANSACTION_PAYER_CUSTOMER.getDictKey().equals(item)) {
                    orderService.addSubOrder(payerOrderMap.get(item), subOrderMap.get(item), List.of(), commissionAddParamsList);
                }else {
                    orderService.addSubOrder(payerOrderMap.get(item), subOrderMap.get(item), List.of(), List.of());
                }
            }else { // 主订单号为空，则先创建主订单
                OrderAddParams orderAddParams = generateOrderAddParam(transactionReport, item, subOrderMap.get(item), commissionAddParamsList);
                if (ObjectUtils.isEmpty(orderAddParams)) {
                    return;
                }
                TbBusinessOrder save = orderService.save(orderAddParams);

                // 对应交易人关联主订单
                payerOrderMap.put(item, save.getId());
            }
        });
        // 记录本次生成订单的业务数据ID
        // 做一次业务数据的 订单状态更新，仅针对于之前未关联过订单数据的记录
        List<Long> planIdList = commissionPlans.stream()
                .filter(plan -> ObjectUtils.isEmpty(plan.getOrderId()))
                .map(TbBusinessReceiveCommissionPlan::getId)
                .toList();

        payerOrderMap.keySet()
                .forEach(payerOrder -> {
                    OrderVo orderVo = orderService.detail(payerOrderMap.get(payerOrder));
                    // 交费人为客户时，先根据收佣子订单更新业务数据状态
                    if (DropdownEnum.TRANSACTION_PAYER_CUSTOMER.getDictKey().equals(payerOrder)) {
                        List<OrderCommissionVo> commissionOrder = orderVo.getCommissionOrder();
                        if (!ObjectUtils.isEmpty(commissionOrder)) {
                            List<TbBusinessReceiveCommissionPlan> plans = commissionOrder.stream()
                                    .filter(Objects::nonNull)
                                    .filter(orderCommissionVo -> planIdList.contains(orderCommissionVo.getBusinessId()))
                                    .map(commission -> {
                                        // 一笔意向金只能转佣一次，且只会生成一条转佣记录
                                        TbBusinessReceiveCommissionPlan plan = new TbBusinessReceiveCommissionPlan();
                                        plan.setId(commission.getBusinessId());
                                        plan.setOrderId(orderVo.getId());
                                        plan.setOrderCode(orderVo.getOrderCode());
                                        plan.setSubOrderCode(commission.getSubOrderCode());
                                        plan.setOrderStatus(subOrderStatus2CommissionPlanOrderStatus(commission.getOrderStatus()));
                                        return plan;
                                    }).toList();
                            if (!ObjectUtils.isEmpty(plans)) {
                                receiveCommissionPlanMapper.updateById(plans);
                            }
                        }
                    }
                    if (!ObjectUtils.isEmpty(orderVo.getReceiveOrder())) {
                        List<TbBusinessReceiveCommissionPlan> plans = orderVo.getReceiveOrder().stream()
                                .filter(Objects::nonNull)
                                .filter(orderReceiveVo -> planIdList.contains(orderReceiveVo.getBusinessId()))
                                .map(receive -> {
                                    TbBusinessReceiveCommissionPlan plan = new TbBusinessReceiveCommissionPlan();
                                    plan.setId(receive.getBusinessId());
                                    plan.setOrderId(orderVo.getId());
                                    plan.setOrderCode(orderVo.getOrderCode());
                                    plan.setSubOrderId(receive.getId());
                                    plan.setSubOrderCode(receive.getSubOrderCode());
                                    plan.setOrderStatus(subOrderStatus2CommissionPlanOrderStatus(receive.getOrderStatus()));
                                    return plan;
                                }).toList();
                        if (!ObjectUtils.isEmpty(plans)) {
                            receiveCommissionPlanMapper.updateById(plans);
                        }
                    }
                    // 发起大额转账订单推送收费系统
                    orderService.bigAmountTransferPush(orderVo.getId());
                });
        // 检查是否存在转佣订单，存在则推送收费系统
        Long customerOrderId = payerOrderMap.get(DropdownEnum.TRANSACTION_PAYER_CUSTOMER.getDictKey());
        if (!ObjectUtils.isEmpty(customerOrderId)) {
            orderService.prepareConvertCommission(customerOrderId);
        }
    }

    /**
     * 子订单状态转 收佣行订单状态
     * @param orderStatus
     * @return
     */
    @Override
    public String subOrderStatus2CommissionPlanOrderStatus(String orderStatus) {
        if (DropdownEnum.ORDER_STATUS_UNPAY.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSFER_RECEIVE_UNPAY.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_PAYING.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSFER_RECEIVE_PAYING.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_PAYED.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSFER_RECEIVE_PAYED.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_UNCONVERT.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSFER_RECEIVE_UNCONVERT.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_CONVERTING.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSFER_RECEIVE_CONVERTING.getDictKey();
        }else if (DropdownEnum.ORDER_STATUS_CONVERT.getDictKey().equals(orderStatus)) {
            return DropdownEnum.TRANSFER_RECEIVE_CONVERT.getDictKey();
        }
        return null;
    }

    /**
     * 生成主订单信息
     * @param transactionReport 成交报告
     * @param payer 交费人
     * @param subOrderReceiveAddParams 收款子订单
     * @param commissionAddParamsList 转佣子订单
     * @return
     */
    private OrderAddParams generateOrderAddParam(TbBusinessTransactionReport transactionReport,
                                                 String payer,
                                                 List<SubOrderReceiveAddParams> subOrderReceiveAddParams,
                                                 List<SubOrderCommissionAddParams> commissionAddParamsList) {
        if (ObjectUtils.isEmpty(subOrderReceiveAddParams) && ObjectUtils.isEmpty(commissionAddParamsList)) {
            return null;
        }
        OrderAddParams orderAddParams = new OrderAddParams();
        orderAddParams.setContractNum(transactionReport.getTransactionContractNum());
        if (!ObjectUtils.isEmpty(transactionReport.getHouseId())) {
           orderAddParams.setHouseIds(String.join(RsmsConstant.SPLIT_COMMA, Stream.of(transactionReport.getHouseId()).map(String::valueOf).toList()));
        }
        if (!ObjectUtils.isEmpty(transactionReport.getParkingSpaceId())) {
            orderAddParams.setParkingIds(String.join(RsmsConstant.SPLIT_COMMA, Stream.of(transactionReport.getParkingSpaceId()).map(String::valueOf).toList()));
        }
        // 新房项目相关字段
        if (!ObjectUtils.isEmpty(transactionReport.getBuilding())
                && !ObjectUtils.isEmpty(transactionReport.getUnit())
                && !ObjectUtils.isEmpty(transactionReport.getHouseNumber())
                && !ObjectUtils.isEmpty(transactionReport.getNewHouseCommunityId())) {
            orderAddParams.setBuilding(transactionReport.getBuilding());
            orderAddParams.setUnit(transactionReport.getUnit());
            orderAddParams.setHouseNumber(transactionReport.getHouseNumber());
            orderAddParams.setNewHouseCommunityId(transactionReport.getNewHouseCommunityId());
        }
        if (!ObjectUtils.isEmpty(transactionReport.getHouseId())) {
            List<String> list = dataServerClient.listHouseInfoByIds(List.of(transactionReport.getHouseId()))
                    .stream()
                    .map(HouseInfoVo::getCommunityId)
                    .map(String::valueOf)
                    .toList();
            orderAddParams.setCommunityIds(String.join(RsmsConstant.SPLIT_COMMA, list));
        }
        if (!ObjectUtils.isEmpty(transactionReport.getParkingSpaceId())) {
            List<String> list = dataServerClient.getParkingSpaceByIdList(List.of(transactionReport.getParkingSpaceId()))
                    .stream()
                    .map(ParkingSpaceResp::getCommunityId)
                    .map(String::valueOf)
                    .toList();
            orderAddParams.setCommunityIds(String.join(RsmsConstant.SPLIT_COMMA, list));
        }
        if (DropdownEnum.TRANSACTION_PAYER_CUSTOMER.getDictKey().equals(payer)) {
            Map<Long, CustomerSourceResp> customerMap = dataServerClient.innerQueryCustomerByIdList(List.of(transactionReport.getCustomerId()))
                    .stream()
                    .collect(Collectors.toMap(CustomerSourceResp::getId, Function.identity(), (existing, next) -> existing));
            orderAddParams.setCustomerName(customerMap.get(transactionReport.getCustomerId()).getName());
            orderAddParams.setCustomerMobile(customerMap.get(transactionReport.getCustomerId()).getMobile());
        } else if (DropdownEnum.TRANSACTION_PAYER_OWNER.getDictKey().equals(payer)) {
            Optional<TbBusinessContractInfo> any = contractInfoMapper.selectList(Wrappers.lambdaQuery(TbBusinessContractInfo.class)
                            .eq(TbBusinessContractInfo::getReportId, transactionReport.getId()))
                    .stream()
                    .findAny();
            any.ifPresent(info -> {
                orderAddParams.setCustomerName(info.getOwnerName());
                orderAddParams.setCustomerMobile(info.getOwnerMobile());
            });
        }else {
            // 收费人第三方的时候，设置为 经纪人的 姓名和手机号
            UserDetailsMicroResp userDetailsMicroResp = authorServerClient.getUserDetailsMicro(List.of(transactionReport.getBrokerId()))
                    .stream()
                    .collect(Collectors.toMap(UserDetailsMicroResp::getId, Function.identity(), (existing, next) -> existing))
                    .get(transactionReport.getBrokerId());
            if (!ObjectUtils.isEmpty(userDetailsMicroResp)) {
                orderAddParams.setCustomerName(userDetailsMicroResp.getEmpName());
                orderAddParams.setCustomerMobile(userDetailsMicroResp.getMobile());
            }
        }
        String type = DropdownEnum.ORDER_SOURCE_TYPE_YJ.getDictKey();
        orderAddParams.setTransactionType(type);
        orderAddParams.setSubOrderReceiveAddParams(subOrderReceiveAddParams);
        if (DropdownEnum.TRANSACTION_PAYER_CUSTOMER.getDictKey().equals(payer)) {
            orderAddParams.setSubOrderCommissionAddParams(commissionAddParamsList);
        }
        return orderAddParams;
    }

    /**
     * 生成 转佣子订单
     * @return
     */
    private SubOrderCommissionAddParams generateCommissionAddParam(TbBusinessTransactionInfo info, BigDecimal amount) {
        SubOrderCommissionAddParams commissionAddParams = new SubOrderCommissionAddParams();
        commissionAddParams.setExportAmount(amount);
        commissionAddParams.setExportSubOrderId(info.getSubOrderId());
        commissionAddParams.setExportSubOrderCode(info.getSubOrderCode());
        return commissionAddParams;
    }

    /**
     * 生成收款子订单
     *
     * @param plan
     * @return
     */
    private SubOrderReceiveAddParams generateSubOrderReceiveAddParams(TbBusinessReceiveCommissionPlan plan, BigDecimal amount, String remark) {
        SubOrderReceiveAddParams subOrderReceiveAddParams = new SubOrderReceiveAddParams();
        subOrderReceiveAddParams.setGoodName(DropdownEnum.ORDER_SOURCE_TYPE_YJ.getDictValue());
        subOrderReceiveAddParams.setPayMode(DropdownEnum.ORDER_PAY_MODEL_2.getDictKey());
        subOrderReceiveAddParams.setPayAmount(amount);
        subOrderReceiveAddParams.setDiscountAmount(new BigDecimal("0.00"));
        subOrderReceiveAddParams.setBusinessId(plan.getId());
        subOrderReceiveAddParams.setRemark(remark);
        subOrderReceiveAddParams.setReceiveCommunityId(plan.getCostItemId()); // 收款项目ID 通过收费系统接口返回
        CostItemInfoResp costItemInfoResp = dataServerClient.queryOne(plan.getCostItemCode(), null);
        if (!ObjectUtils.isEmpty(costItemInfoResp)) {
            subOrderReceiveAddParams.setReceiveCommunityName(costItemInfoResp.getSfCostItemName());
        }
        return subOrderReceiveAddParams;
    }

    /**
     * 新增意向金交易信息行 的转佣记录
     * @param report
     */
    private void addConvertCommissionOnDeposit(TransactionReportAddParams report) {
        // 意向金交易信息：新增意向金转佣记录
        if (!ObjectUtils.isEmpty(report.getDepositId())) {
            DepositVo statistics = depositService.getAmountStatistics(report.getDepositId());
            BigDecimal availableAmount = statistics.getReceiveAmount().subtract(statistics.getRefundAmount()).subtract(statistics.getCommissionAmount());
            log.info("当前成交报告 : {} 绑定的意向金：{} 下可转佣的金额为：{}", report.getId(), report.getDepositId(), statistics);
            if (availableAmount.compareTo(BigDecimal.ZERO) <= 0) {
                // 转佣金额为 0则不生成转佣记录
                return;
            }
            // 意向金明细行新增转佣记录
            TbBusinessTransactionInfo tbBusinessTransactionInfo = new TbBusinessTransactionInfo();
            tbBusinessTransactionInfo.setId(snowFlake.nextId());
            tbBusinessTransactionInfo.setDepositId(report.getDepositId());
            tbBusinessTransactionInfo.setTransactionDateTime(LocalDateTime.now());
            tbBusinessTransactionInfo.setOrderStatus(DropdownEnum.TRANSACTION_ORDER_STATUS_UNCOMMISSION.getDictKey());

            TransactionCostItemConfig.TransactionType2CostItem transactionType2CostItem = reportCategory2TransactionType(report.getCategory());
            CostItemInfoResp costItemInfoResp = dataServerClient.queryOne(transactionType2CostItem.getCostItemCode(), null);
            Optional<DropdownEnum> any = Arrays.stream(DropdownEnum.values())
                    .filter(dropdownEnum -> "transaction_type".equals(dropdownEnum.getDictCode()))
                    .filter(dropdownEnum -> costItemInfoResp.getCostItemName().equals(dropdownEnum.getDictValue()))
                    .findAny();
            if (any.isEmpty()) {
                log.info("字典中查询意向金 交易类型：{} 未查询到,检查配置文件.....", costItemInfoResp.getCostItemName());
                throw new BusinessException(CommonEnum.SYSTEM_ERROR);
            }

            tbBusinessTransactionInfo.setTransactionType(any.get().getDictKey());
            // 如果意向金可用余额 大于 成交报告客户佣金 则 转佣金额 为 客户佣金，剩余 意向金余额由客户发起退款，否则，为意向金余额
            tbBusinessTransactionInfo.setTransactionAmount(availableAmount.compareTo(report.getCustomerCommission()) > 0 ? report.getCustomerCommission() : availableAmount);
            tbBusinessTransactionInfo.setContractNum(report.getTransactionContractNum());
            transactionInfoMapper.insert(tbBusinessTransactionInfo);

            // 反填成交报告关联的合同编号
            LambdaUpdateWrapper<TbBusinessDeposit> depositLambdaUpdateWrapper = Wrappers.lambdaUpdate(TbBusinessDeposit.class)
                    .eq(TbBusinessDeposit::getId, report.getDepositId())
                    .set(TbBusinessDeposit::getTransactionContractNum, report.getTransactionContractNum());
            depositMapper.update(depositLambdaUpdateWrapper);
        }
    }

    /**
     * 根据成交报告类型，查询配置文件中对应的配置项
     * @param category
     * @return
     */
    private TransactionCostItemConfig.TransactionType2CostItem reportCategory2TransactionType(String category) {
        Optional<String> any = Arrays.stream(DropdownEnum.values())
                .filter(dropdownEnum -> dropdownEnum.getDictKey().equals(category))
                .map(DropdownEnum::getDictValue)
                .findAny();
        if (any.isEmpty()) {
            log.info("成交报告类型：{} 在枚举类中未查询到，检查配置文件.....", category);
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }
        Optional<TransactionCostItemConfig.TransactionType2CostItem> type2CostItem = transactionCostItemConfig.getTransactionType2CostItem()
                .stream()
                .filter(transactionType2CostItem -> {
                    return transactionType2CostItem.getCategory().equals(any.get());
                }).findAny();
        if (type2CostItem.isEmpty()) {
            log.info("成交报告类型：{}, 类型值：{} 在 nacos 配置信息中未查询到，检查配置文件.....", category, any.get());
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }
        return type2CostItem.get();
    }

    @Override
    @OperateLog(operateModule = "log_transaction", operateType = "anti_audit")
    @Transactional(rollbackFor = Exception.class)
    public Long reverseAudit(TransactionReportEditParams params) {
        if (ObjectUtils.isEmpty(params)) {
            throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
        }
        if (ObjectUtils.isEmpty(params.getId())) {
            throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
        }
        TbBusinessTransactionReport transactionReport = getById(params.getId());
        if (!DropdownEnum.TRANSFER_STATUS_REVIEWED.getDictKey().equals(transactionReport.getStatus())
                && !DropdownEnum.TRANSFER_STATUS_UNPAY.getDictKey().equals(transactionReport.getStatus())
                && !DropdownEnum.TRANSFER_STATUS_PART_PAY.getDictKey().equals(transactionReport.getStatus())
                && !DropdownEnum.TRANSFER_STATUS_PAYED.getDictKey().equals(transactionReport.getStatus())) {
            throw new BusinessException(BusinessEnum.TRANSACTION_REPORT_STATUS_ERROR);
        }
        LambdaUpdateWrapper<TbBusinessTransactionReport> set = Wrappers.lambdaUpdate(TbBusinessTransactionReport.class)
                .eq(TbBusinessTransactionReport::getId, transactionReport.getId())
                .set(TbBusinessTransactionReport::getStatus, DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey())
                .set(TbBusinessTransactionReport::getUpdateUser, StpUtil.getLoginIdAsLong());
        transactionReportMapper.update(set);

        LambdaUpdateWrapper<TbBusinessOwnershipTransfer> eq = Wrappers.lambdaUpdate(TbBusinessOwnershipTransfer.class)
                .eq(TbBusinessOwnershipTransfer::getTransactionReportId, transactionReport.getId())
                .set(TbBusinessOwnershipTransfer::getTransferStatus, DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey());
        ownershipTransferMapper.update(eq);
        return transactionReport.getId();
    }

    @Override
    @OperateLog(operateModule = "log_transaction", operateType = "add")
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(TransactionReportAddParams params) {
        Long id = insideSaveOrUpdateTransactionReport(params);
        LambdaUpdateWrapper<TbBusinessOwnershipTransfer> eq = Wrappers.lambdaUpdate(TbBusinessOwnershipTransfer.class)
                .eq(TbBusinessOwnershipTransfer::getTransactionReportId, id)
                .set(TbBusinessOwnershipTransfer::getTransferStatus, DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey());
        ownershipTransferMapper.update(eq);
        return id;
    }

    @Override
    public TransactionReportVo getDetail(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
        }
        TbBusinessTransactionReport transactionReport = getById(id);
        TransactionReportVo transactionReportVo = reportConvert.dataToVo(transactionReport);

        CompletableFuture<Map<String, List<Long>>> supplyAsync = CompletableFuture.supplyAsync(() -> {
            return PermissionUtil.syncGetUserIdByPermissionCode(List.of(
                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_VIEW.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_EDIT.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_DELETE.getCode(),

                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_AUDIT_OR_REJECT.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVERSE_AUDIT.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVIEW_OR_REJECT.getCode(),
                    PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_CLOSE.getCode()
            ));
        });

        // 填充客户信息
        if (!ObjectUtils.isEmpty(transactionReport.getCustomerId())) {
            setCustomerProperties(transactionReport.getCustomerId(), transactionReportVo);
        }
        // 获取并填充维护人信息
        UserDetailsMicroResp userInfo = getUserInfo(transactionReport.getBrokerId());
        if (!ObjectUtils.isEmpty(userInfo)) {
            transactionReportVo.setBrokerName(userInfo.getEmpName());
            transactionReportVo.setBrokerMobile(PersonDataEncryptUtil.mobileEncrypt(userInfo.getMobile()));
            transactionReportVo.setBrokerMobileEncode(Base64Util.encodeData(userInfo.getMobile()));
            transactionReportVo.setBrokerStore(userInfo.getStoreName());
        }
        // 填充房源、车位、新房信息
        if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_9.getDictKey().equals(transactionReportVo.getCategory())
                && !ObjectUtils.isEmpty(transactionReportVo.getNewHouseCommunityId())) {
            // 设置新房 新房项目 楼栋单元信息为虚拟信息，不存在关联表
            NewHouseResp newHouseResp = dataServerClient.getNewHouseByIdList(List.of(transactionReportVo.getNewHouseCommunityId()))
                    .stream()
                    .collect(Collectors.toMap(NewHouseResp::getId, Function.identity(), (existing, next) -> existing))
                    .get(transactionReportVo.getNewHouseCommunityId());
            transactionReportVo.setNewHouseCommunityName(newHouseResp.getCommunityName());
        } else if (!ObjectUtils.isEmpty(transactionReport.getParkingSpaceId())
                && (
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_3.getDictKey().equals(transactionReportVo.getCategory())
                        || DropdownEnum.DEPOSIT_CATEGORY_DPS_C_4.getDictKey().equals(transactionReportVo.getCategory())
                        || DropdownEnum.DEPOSIT_CATEGORY_DPS_C_10.getDictKey().equals(transactionReportVo.getCategory())
        )) {
            // 设置车位信息
            setParkingSpaceInfo(transactionReport.getParkingSpaceId(), transactionReportVo);
        } else if (!ObjectUtils.isEmpty(transactionReport.getHouseId())){ // 设置房源信息
            setHouseInfo(transactionReport.getHouseId(), transactionReportVo);
        }

        // 填充成交报告 合同附件ID
        List<DataFileInfoVo> fileInfoVos = dataServerClient.listFileInfo(transactionReportVo.getId(), null, false).getData();
        transactionReportVo.setReportFiles(fileInfoVos);
        // 填充合同人信息
        setTransactionReportContractInfo(transactionReportVo);
        // 填充收佣信息
        setReceiveCommissionPlan(transactionReportVo);

        // 判断是否关联合同，允许修改合同相关字段
        transactionReportVo.setLinkContract(checkLinkContract(transactionReportVo.getTransactionContractNum()));
        // 填充意向金信息
        setDepositInfo(transactionReportVo);
        // 填充权证过户信息
        setOwnershipTransferInfo(transactionReportVo);

        Map<String, List<Long>> permissionCodeMap = supplyAsync.join();
        transactionReportVo.setPermissions(givePermissions(permissionCodeMap, transactionReport, transactionReportVo.getStatus()));
        return transactionReportVo;
    }

    /**
     * 设置 合同人信息
     * @param transactionReportVo
     */
    private void setTransactionReportContractInfo(TransactionReportVo transactionReportVo) {
        LambdaQueryWrapper<TbBusinessContractInfo> contractInfoLambdaQueryWrapper = Wrappers.lambdaQuery(TbBusinessContractInfo.class)
                .eq(TbBusinessContractInfo::getReportId, transactionReportVo.getId());
        TbBusinessContractInfo tbBusinessContractInfo = contractInfoMapper.selectOne(contractInfoLambdaQueryWrapper);
        if (!ObjectUtils.isEmpty(tbBusinessContractInfo)) {
            ContractInfoVo contractInfoVo = contractInfoConvert.dataToVo(tbBusinessContractInfo);
            contractInfoVo.setCustomerMobile(PersonDataEncryptUtil.mobileEncrypt(tbBusinessContractInfo.getCustomerMobile()));
            contractInfoVo.setCustomerMobileEncode(Base64Util.encodeData(tbBusinessContractInfo.getCustomerMobile()));
            contractInfoVo.setOwnerMobile(PersonDataEncryptUtil.mobileEncrypt(tbBusinessContractInfo.getOwnerMobile()));
            contractInfoVo.setOwnerMobileEncode(Base64Util.encodeData(tbBusinessContractInfo.getOwnerMobile()));
            contractInfoVo.setCustomerIcardNum(contractInfoVo.getCustomerIcardNum());
            contractInfoVo.setOwnerIcardNum(contractInfoVo.getOwnerIcardNum());
            transactionReportVo.setContractInfo(contractInfoVo);
        }
    }

    /**
     * 详情 设置收佣计划行
     * @param transactionReportVo
     */
    private void setReceiveCommissionPlan(TransactionReportVo transactionReportVo) {
        LambdaQueryWrapper<TbBusinessReceiveCommissionPlan> planLambdaQueryWrapper = Wrappers.lambdaQuery(TbBusinessReceiveCommissionPlan.class)
                .eq(TbBusinessReceiveCommissionPlan::getReportId, transactionReportVo.getId());
        List<TbBusinessReceiveCommissionPlan> tbBusinessReceiveCommissionPlans = receiveCommissionPlanMapper.selectList(planLambdaQueryWrapper);
        if (!ObjectUtils.isEmpty(tbBusinessReceiveCommissionPlans)) {
            List<String> itemCode = tbBusinessReceiveCommissionPlans.stream()
                    .map(TbBusinessReceiveCommissionPlan::getCostItemCode)
                    .filter(Objects::nonNull)
                    .toList();

            Map<String, CostItemInfoResp> costItemInfoRespMap = dataServerClient.queryByZsCostItemCode(itemCode)
                    .stream()
                    .collect(Collectors.toMap(CostItemInfoResp::getCostItemCode, Function.identity()));
            List<Long> planIdList = tbBusinessReceiveCommissionPlans.stream()
                    .map(TbBusinessReceiveCommissionPlan::getId)
                    .toList();
            Map<Long, DataFileInfoVo> planFileMap = dataServerClient.findByBusinessIdList(planIdList)
                    .getData()
                    .stream()
                    .collect(Collectors.toMap(DataFileInfoVo::getBusinessId, Function.identity(), (existing, next) -> existing));

            List<ReceiveCommissionPlanVo> receiveCommissionPlanVos = tbBusinessReceiveCommissionPlans.stream()
                    .map(planConvert::dataToVo)
                    .peek(item -> {
                        CostItemInfoResp costItemInfoResp = costItemInfoRespMap.get(item.getCostItemCode());
                        if (!ObjectUtils.isEmpty(costItemInfoResp)) {
                            item.setCostItemDesc(costItemInfoResp.getCostItemName());
                        }
                        if (!ObjectUtils.isEmpty(planFileMap.get(item.getId()))) {
                            item.setEnclosureFile(planFileMap.get(item.getId()));
                        }
                        if (!ObjectUtils.isEmpty(item.getOrderId())
                                || !ObjectUtils.isEmpty(item.getSubOrderId())
                                || !ObjectUtils.isEmpty(item.getDepositId())
                                || !ObjectUtils.isEmpty(item.getActualPayment())) {
                            item.setEdit(false);
                        } else {
                            item.setEdit(true);
                        }
                    })
                    .toList();
            transactionReportVo.setReceiveCommissionPlans(receiveCommissionPlanVos);
            if (!ObjectUtils.isEmpty(receiveCommissionPlanVos)) {
                Optional<LocalDateTime> max = tbBusinessReceiveCommissionPlans.stream().map(TbBusinessReceiveCommissionPlan::getUpdateTime).max(LocalDateTime::compareTo);
                max.ifPresent(localDateTime -> transactionReportVo.setCollectAllDate(localDateTime.toLocalDate()));
            }
        }else {
            transactionReportVo.setReceiveCommissionPlans(List.of());
        }
    }

    /**
     * 成交报告提交时 新增权证过户记录
     * @param reportId
     * @param params
     */
    private void insertOwnership(Long reportId, TransactionReportEditParams params) {
        TbBusinessOwnershipTransfer tbBusinessOwnershipTransfer = new TbBusinessOwnershipTransfer();
        tbBusinessOwnershipTransfer.setId(snowFlake.nextId());
        tbBusinessOwnershipTransfer.setTransactionReportId(reportId);
        tbBusinessOwnershipTransfer.setTransferContractNumber(params.getTransactionReport().getTransactionContractNum());
        tbBusinessOwnershipTransfer.setTransferStatus(DropdownEnum.TRANSFER_STATUS_AUDITING.getDictKey());
        tbBusinessOwnershipTransfer.setHouseId(params.getTransactionReport().getHouseId());
        tbBusinessOwnershipTransfer.setParkingSpaceId(params.getTransactionReport().getParkingSpaceId());
        tbBusinessOwnershipTransfer.setBuilding(params.getTransactionReport().getBuilding());
        tbBusinessOwnershipTransfer.setUnit(params.getTransactionReport().getUnit());
        tbBusinessOwnershipTransfer.setHouseNumber(params.getTransactionReport().getHouseNumber());
        tbBusinessOwnershipTransfer.setNewHouseCommunityId(params.getTransactionReport().getNewHouseCommunityId());
        tbBusinessOwnershipTransfer.setCustomerId(params.getTransactionReport().getCustomerId());
        tbBusinessOwnershipTransfer.setCurrentTransferStage(DropdownEnum.TRANSFER_STAGE_TS1.getDictKey());
        ownershipTransferMapper.insert(tbBusinessOwnershipTransfer);
    }

    private TbBusinessTransactionReport getById(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
        }
        TbBusinessTransactionReport tbBusinessTransactionReport = transactionReportMapper.selectById(id);
        if (ObjectUtils.isEmpty(tbBusinessTransactionReport)) {
            throw new BusinessException(CommonEnum.REQUEST_OBJ_NOT_EXIST);
        }
        return tbBusinessTransactionReport;
    }

    /**
     * 新增/更新 成交报告，供新增/更新、提交接口调用
     * @param params
     * @return
     */
    private Long insideSaveOrUpdateTransactionReport(TransactionReportAddParams params) {
        if (ObjectUtils.isEmpty(params)) {
            throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
        }
        if (!ObjectUtils.isEmpty(params.getId())) {
            TbBusinessTransactionReport byId = getById(params.getId());
            // 成交状态为【未审核】时，相关权限人员可以新增/编辑成交报告
            if (!DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey().equals(byId.getStatus())) {
                throw new BusinessException(BusinessEnum.TRANSACTION_REPORT_STATUS_ERROR);
            }
        }
        TbBusinessTransactionReport tbBusinessTransactionReport = reportConvert.addParamsToData(params);
        // 修改时，则删除合同人信息、收佣计划，重新做新增操作
        if (!ObjectUtils.isEmpty(tbBusinessTransactionReport.getId())) {
            deleteRelevantInfo(tbBusinessTransactionReport.getId(), false);
            // 成交报告编码永远不变
            tbBusinessTransactionReport.setReportCode(null);
            tbBusinessTransactionReport.setUpdateUser(StpUtil.getLoginIdAsLong());

            // 合同关联性检查，关联，不允许修改合同相关字段
            if (checkLinkContract(tbBusinessTransactionReport.getTransactionContractNum())) {
                tbBusinessTransactionReport.setTransactionContractNum(null);
                tbBusinessTransactionReport.setHouseId(null);
                // 车位
                tbBusinessTransactionReport.setParkingSpaceId(null);
                tbBusinessTransactionReport.setCustomerId(null);
                tbBusinessTransactionReport.setOwnerInfo(null);
                tbBusinessTransactionReport.setDealDate(null);
                tbBusinessTransactionReport.setDealAmount(null);
                tbBusinessTransactionReport.setBrokerId(null);
                tbBusinessTransactionReport.setOwnerCommission(null);
                tbBusinessTransactionReport.setCustomerCommission(null);
                // 新房项目相关字段也不允许修改
                tbBusinessTransactionReport.setBuilding(null);
                tbBusinessTransactionReport.setUnit(null);
                tbBusinessTransactionReport.setHouseNumber(null);
                tbBusinessTransactionReport.setNewHouseCommunityId(null);
            }else {
                // 未关联合同的 记录，才可以修改合同双方信息，关联了合同的，不允许修改双方信息
                TbBusinessContractInfo tbBusinessContractInfo = contractInfoConvert.addParamsToData(params.getContractInfo());
                tbBusinessContractInfo.setReportId(tbBusinessTransactionReport.getId());
                tbBusinessContractInfo.setCreateUser(StpUtil.getLoginIdAsLong());
                contractInfoMapper.updateById(tbBusinessContractInfo);
            }
            // 如果已生成订单，则不允许修改关联的意向金
            if (hasOrderInfo(tbBusinessTransactionReport.getId())) {
                tbBusinessTransactionReport.setDepositId(null);
            }
            // 如果 类目是 住宅新盘买卖 ，则清空 房源ID、车位ID 字段
            if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_9.getDictKey().equals(tbBusinessTransactionReport.getCategory())) {
                tbBusinessTransactionReport.setHouseId(null);
                tbBusinessTransactionReport.setParkingSpaceId(null);
            }else { // 否则，清空新房项目相关字段
                tbBusinessTransactionReport.setBuilding(null);
                tbBusinessTransactionReport.setUnit(null);
                tbBusinessTransactionReport.setHouseNumber(null);
                tbBusinessTransactionReport.setNewHouseCommunityId(null);
            }
            transactionReportMapper.updateById(tbBusinessTransactionReport);
        } else {
            tbBusinessTransactionReport.setId(snowFlake.nextId());
            tbBusinessTransactionReport.setReportCode(flowNumberUtil.getFlowNumber(BusinessPrefixEnum.TRANSACTION_REPORT_KEY_PREFIX.getCode(), 6));
            tbBusinessTransactionReport.setStatus(DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey());
            tbBusinessTransactionReport.setCreateUser(StpUtil.getLoginIdAsLong());
            tbBusinessTransactionReport.setCreateTime(LocalDateTime.now());
            // 如果 类目是 住宅新盘买卖 ，则清空 房源ID、车位ID 字段
            if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_9.getDictKey().equals(tbBusinessTransactionReport.getCategory())) {
                tbBusinessTransactionReport.setHouseId(null);
                tbBusinessTransactionReport.setParkingSpaceId(null);
            }else { // 否则，清空新房项目相关字段
                tbBusinessTransactionReport.setBuilding(null);
                tbBusinessTransactionReport.setUnit(null);
                tbBusinessTransactionReport.setHouseNumber(null);
                tbBusinessTransactionReport.setNewHouseCommunityId(null);
            }
            transactionReportMapper.insert(tbBusinessTransactionReport);

            // 新增合同人信息
            TbBusinessContractInfo tbBusinessContractInfo = contractInfoConvert.addParamsToData(params.getContractInfo());
            tbBusinessContractInfo.setId(snowFlake.nextId());
            tbBusinessContractInfo.setReportId(tbBusinessTransactionReport.getId());
            tbBusinessContractInfo.setCreateUser(StpUtil.getLoginIdAsLong());
            contractInfoMapper.insert(tbBusinessContractInfo);
        }
        // 新增文件信息
        if (!ObjectUtils.isEmpty(params.getReportFiles())) {
            params.getReportFiles().forEach(item -> {
                insertFileInfo(tbBusinessTransactionReport.getId(), item.getId(), item.getBusinessType());
            });
        }
        Map<String, ReceiveCommunityVo> receiveCommunityVoMap = getReceiveCommunityList(params.getHouseId(), params.getParkingSpaceId(), params.getNewHouseCommunityId(), params.getTransactionContractNum())
                .stream()
                .collect(Collectors.toMap(ReceiveCommunityVo::getZsItemCode, Function.identity()));
        // 这里每次都是全量数据（不包含意向金转佣 的收佣行记录）
        params.getReceiveCommissionPlans()
                .forEach(item -> {
                    // 如果是绑定订单记录，则不允许修改，不做新增
                    if (!ObjectUtils.isEmpty(item.getOrderId()) || !ObjectUtils.isEmpty(item.getSubOrderId())) {
                        return;
                    }
                    if (!ObjectUtils.isEmpty(item.getDepositId())) {
                        return;
                    }
                    TbBusinessReceiveCommissionPlan tbBusinessReceiveCommissionPlan = planConvert.addParamsToData(item);
                    tbBusinessReceiveCommissionPlan.setId(snowFlake.nextId());
                    tbBusinessReceiveCommissionPlan.setReportId(tbBusinessTransactionReport.getId());
                    ReceiveCommunityVo receiveCommunityVo = receiveCommunityVoMap.get(item.getCostItemCode());
                    // 收佣行收费项描述字段为 收费系统对应的收费向名称
                    if (!ObjectUtils.isEmpty(receiveCommunityVo)) {
                        tbBusinessReceiveCommissionPlan.setCostItemDesc(receiveCommunityVo.getSfItemName());
                    }
                    tbBusinessReceiveCommissionPlan.setCreateUser(StpUtil.getLoginIdAsLong());
                    receiveCommissionPlanMapper.insert(tbBusinessReceiveCommissionPlan);
                    if (!ObjectUtils.isEmpty(item.getEnclosureFileId())) {
                        insertFileInfo(tbBusinessReceiveCommissionPlan.getId(), item.getEnclosureFileId(), null);
                    }
                });
        // 已收讫的单据新增明细行后，需要重新判断是否已收款
        return tbBusinessTransactionReport.getId();
    }

    /**
     * 新增转佣 收佣行 的 处理
     * @param id
     * @param category
     * @param depositId
     * @param newHouseCommunityId
     * @param parkingSpaceId
     * @param houseId
     * @param contractNum
     */
    private void doInsertConvertCommissionPlan(Long id, String category, Long depositId, Long newHouseCommunityId, Long parkingSpaceId, Long houseId, String contractNum) {
        // 意向金转佣记录由后端生成，且只有未审核过的记录才允许生成意向金转佣记录
        // 由于修改时，会全量删除历史关联数据，故这里仅作新增操作
        if (!ObjectUtils.isEmpty(depositId) && !hasOrderInfo(id)) {
            TbBusinessDeposit deposit = depositMapper.selectById(depositId);
            if (ObjectUtils.isEmpty(deposit)) {
                throw new BusinessException(CommonEnum.REQUEST_OBJ_NOT_EXIST);
            }
            List<TbBusinessTransactionInfo> transactionInfos = transactionInfoMapper.selectList(Wrappers.lambdaQuery(TbBusinessTransactionInfo.class)
                    .eq(TbBusinessTransactionInfo::getDepositId, deposit.getId()));
            // 转佣金额
            DepositVo statistics = depositService.getAmountStatistics(deposit.getId());
            // 意向金中剩余的可转佣金额
            BigDecimal convertCommission = statistics.getReceiveAmount().subtract(statistics.getRefundAmount()).subtract(statistics.getCommissionAmount());
            // 剩余金额大于 0 才能转佣
            if (convertCommission.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("意向金的剩余金额的可转佣金额小于0.00 不生成转佣收佣行......");
                return;
            }
            // 查询收费项 value
            String costItemId = "";
            TransactionCostItemConfig.TransactionType2CostItem transactionType2CostItem = reportCategory2TransactionType(category);
            CostItemInfoResp costItemInfoResp = dataServerClient.queryOne(transactionType2CostItem.getCostItemCode(), null);
            String costItemName = costItemInfoResp.getCostItemName();
            final String costItemCode = transactionType2CostItem.getCostItemCode();
            log.info("当前转佣金意向金 类目 ： {}转换后，对应的收费项描述内容：{} code ：{}", deposit.getCategory(), costItemName, costItemCode);

            List<ReceiveCommunityVo> receiveCommunityList = new LinkedList<>();
            if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_9.getDictKey().equals(category)) {
                receiveCommunityList.addAll(getReceiveCommunityList(null, newHouseCommunityId, contractNum));
            } else if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_3.getDictKey().equals(category)
                    || DropdownEnum.DEPOSIT_CATEGORY_DPS_C_4.getDictKey().equals(category)
                    || DropdownEnum.DEPOSIT_CATEGORY_DPS_C_10.getDictKey().equals(category)) {
                // 车位(车位的项目属于二手房项目)
                ParkingSpaceResp parkingSpaceResp = dataServerClient.getParkingSpaceByIdList(List.of(parkingSpaceId))
                        .stream()
                        .collect(Collectors.toMap(ParkingSpaceResp::getId, Function.identity(), (existing, next) -> existing))
                        .get(parkingSpaceId);
                if (!ObjectUtils.isEmpty(parkingSpaceResp)) {
                    receiveCommunityList.addAll(getReceiveCommunityList(parkingSpaceResp.getCommunityId(), null, contractNum));
                }
            }else { // 二手房
                HouseInfoVo houseInfoVo = dataServerClient.listHouseInfoByIds(List.of(houseId))
                        .stream()
                        .collect(Collectors.toMap(HouseInfoVo::getId, Function.identity(), (existing, next) -> existing))
                        .get(houseId);
                if (!ObjectUtils.isEmpty(houseInfoVo)) {
                    receiveCommunityList.addAll(getReceiveCommunityList(houseInfoVo.getCommunityId(), null, contractNum));
                }
            }
            log.info("查询到的收费项信息：{}", JacksonUtil.toJSON(receiveCommunityList));
            // 这里通过 费用项code 来匹配对应的费项
            Optional<ReceiveCommunityVo> any = receiveCommunityList.stream()
                    .filter(i -> {
                        return i.getZsItemCode().equals(costItemCode);
                    }).findAny();
            if (any.isEmpty()) {
                log.info("未查询到对应的收费项，目标收费项code :{} 收费项名称：{}，业务流程中断........", costItemCode, costItemName);
                throw new BusinessException(BusinessEnum.RECEIVECOMMUNITY_NOT_EXIST_ERROR);
            }
            costItemId = any.get().getItemId();
            insertConvertCommissionPlan(id, costItemId, costItemCode, costItemName, convertCommission, deposit.getId(),
                    transactionInfos.stream().filter(info -> {
                                return DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey().equals(info.getTransactionType());
                            }).map(TbBusinessTransactionInfo::getId)
                            .toList()
            );

        }
    }

    /**
     * 成交报告是否已生成订单过
     * @param reportId
     * @return
     */
    private Boolean hasOrderInfo(Long reportId) {
        if (ObjectUtils.isEmpty(reportId)) {
            return false;
        }
        List<TbBusinessReceiveCommissionPlan> commissionPlans = receiveCommissionPlanMapper.selectList(Wrappers.lambdaQuery(TbBusinessReceiveCommissionPlan.class)
                .eq(TbBusinessReceiveCommissionPlan::getReportId, reportId));
        if (ObjectUtils.isEmpty(commissionPlans)) {
            return false;
        }
        return commissionPlans.stream()
                .anyMatch(plan -> {
                    return !ObjectUtils.isEmpty(plan.getOrderId())
                            || !ObjectUtils.isEmpty(plan.getOrderCode())
                            || !ObjectUtils.isEmpty(plan.getOrderStatus())
                            || !ObjectUtils.isEmpty(plan.getSubOrderCode())
                            || !ObjectUtils.isEmpty(plan.getSubOrderId());
                });
    }

    /**
     * 新增意向金转佣  的 收佣计划行
     */
    private void insertConvertCommissionPlan(Long reportId, String costItemId, String costItemCode, String costItemDesc, BigDecimal amount, Long depositId, List<Long> fileBusinessId) {
        // 意向金剩余金额 大于零则生成转佣记录
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        TbBusinessReceiveCommissionPlan tbBusinessReceiveCommissionPlan = new TbBusinessReceiveCommissionPlan();
        tbBusinessReceiveCommissionPlan.setId(snowFlake.nextId());
        tbBusinessReceiveCommissionPlan.setReportId(reportId);
        tbBusinessReceiveCommissionPlan.setCostItemId(costItemId);
        tbBusinessReceiveCommissionPlan.setCostItemCode(costItemCode);
        tbBusinessReceiveCommissionPlan.setPayer(DropdownEnum.TRANSACTION_PAYER_CUSTOMER.getDictKey());
        tbBusinessReceiveCommissionPlan.setReceivableCommission(amount);
        // 交易信息的转佣记录中，交易金额即为转佣金额
        tbBusinessReceiveCommissionPlan.setReceivedCommission(amount);
        // 实际缴费时间设置为创建时间
        tbBusinessReceiveCommissionPlan.setActualPayment(LocalDateTime.now());
        tbBusinessReceiveCommissionPlan.setExpectPayment(LocalDate.now());
        tbBusinessReceiveCommissionPlan.setRemark("意向金转佣");
        tbBusinessReceiveCommissionPlan.setDepositId(depositId);
        tbBusinessReceiveCommissionPlan.setCreateUser(StpUtil.getLoginIdAsLong());
        receiveCommissionPlanMapper.insert(tbBusinessReceiveCommissionPlan);
        fileBusinessId.forEach(businessId -> {
            dataServerClient.listFileInfo(businessId, null, false)
                    .getData()
                    .stream()
                    .map(DataFileInfoVo::getFileId)
                    .filter(Objects::nonNull)
                    .forEach(fileId -> {
                        insertFileInfo(tbBusinessReceiveCommissionPlan.getId(), fileId, null);
                    });
        });
    }

    /**
     * 检查是否关联合同
     * @param contractNum
     * @return
     */
    private Boolean checkLinkContract(String contractNum) {
        if (ObjectUtils.isEmpty(contractNum)) {
            return false;
        }
        List<TbBusinessContract> bizContractList = bizContractMapper.selectList(Wrappers.lambdaQuery(TbBusinessContract.class)
                .eq(TbBusinessContract::getContractNumber, contractNum));
        if (!ObjectUtils.isEmpty(bizContractList)) {
            // 存在对应的合同，不允许修改合同对应字段
            return true;
        }else {
            return false;
        }
    }

    /**
     * 设置客户姓名手机号
     *
     * @param customerId
     * @param transactionReportVo
     */
    private void setCustomerProperties(Long customerId, TransactionReportVo transactionReportVo) {
        CustomerSourceResp customerSourceResp = dataServerClient.innerQueryCustomerByIdList(List.of(customerId))
                .stream()
                .collect(Collectors.toMap(CustomerSourceResp::getId, Function.identity(), (existing, next) -> existing))
                .get(customerId);
        if (!ObjectUtils.isEmpty(customerSourceResp)) {
            transactionReportVo.setCustomerName(customerSourceResp.getName());
            transactionReportVo.setCustomerMobile(PersonDataEncryptUtil.mobileEncrypt(customerSourceResp.getMobile()));
            transactionReportVo.setCustomerMobileEncode(Base64Util.encodeData(customerSourceResp.getMobile()));
        }
    }

    /**
     * 获取用户的信息
     *
     * @param userId
     * @return
     */
    private UserDetailsMicroResp getUserInfo(Long userId) {
        UserDetailsMicroResp userDetailsMicroResp = new UserDetailsMicroResp();
        if (ObjectUtils.isEmpty(userId)) {
            return userDetailsMicroResp;
        }
        Optional<UserDetailsMicroResp> first = authorServerClient.getUserDetailsMicro(List.of(userId))
                .stream()
                .filter(Objects::nonNull)
                .filter(item -> item.getId().equals(userId))
                .findFirst();
        if (first.isPresent()) {
            userDetailsMicroResp = first.get();
        }
        return userDetailsMicroResp;
    }

    private void setDepositInfo(TransactionReportVo transactionReportVo) {
        if (ObjectUtils.isEmpty(transactionReportVo.getDepositId())) {
            return;
        }
        TbBusinessDeposit tbBusinessDeposit = depositMapper.selectById(transactionReportVo.getDepositId());
        if (ObjectUtils.isEmpty(tbBusinessDeposit)) {
            return;
        }
        DepositVo depositVo = depositConvert.dataToVo(tbBusinessDeposit);
        // 填充 剩余金额、退款金额、佣金金额、剩余金额
        DepositVo statistics = depositService.getAmountStatistics(depositVo.getId());
        if (!ObjectUtils.isEmpty(statistics)) {
            depositVo.setActualReceiveAmount(statistics.getReceiveAmount());
            depositVo.setRefundAmount(statistics.getRefundAmount());
            depositVo.setCommissionAmount(statistics.getCommissionAmount());
            depositVo.setBalanceAmount(statistics.getReceiveAmount().subtract(statistics.getRefundAmount()).subtract(statistics.getCommissionAmount()));
        }
        // 填充经纪人信息
        if (!ObjectUtils.isEmpty(depositVo.getBrokerId())) {
            Optional<UserDetailsMicroResp> first = authorServerClient.getUserDetailsMicro(List.of(depositVo.getBrokerId()))
                    .stream()
                    .filter(item -> item.getId().equals(depositVo.getBrokerId()))
                    .findFirst();
            if (first.isPresent()) {
                UserDetailsMicroResp userDetailsMicroResp = first.get();
                depositVo.setBrokerMobile(PersonDataEncryptUtil.mobileEncrypt(userDetailsMicroResp.getMobile()));
                depositVo.setBrokerMobileEncode(Base64Util.encodeData(userDetailsMicroResp.getMobile()));
                depositVo.setBrokerName(userDetailsMicroResp.getEmpName());
                depositVo.setBrokerStoreName(userDetailsMicroResp.getStoreName());
            }
        }
        // 填充合同附件
        LambdaQueryWrapper<TbBusinessTransactionInfo> eq = Wrappers.lambdaQuery(TbBusinessTransactionInfo.class)
                .eq(TbBusinessTransactionInfo::getDepositId, depositVo.getId());
        List<Long> transactionInfoIdList = transactionInfoMapper.selectList(eq)
                .stream()
                .map(TbBusinessTransactionInfo::getId)
                .toList();
        List<DataFileInfoVo> transactionInfoFile = dataServerClient.findByBusinessIdList(transactionInfoIdList)
                .getData()
                .stream()
                .filter(Objects::nonNull)
                .toList();
        depositVo.setTransactionInfoFile(transactionInfoFile);
        transactionReportVo.setDeposit(depositVo);
    }


    /**
     * 填充权证过户信息
     *
     * @param reportVo
     */
    private void setOwnershipTransferInfo(TransactionReportVo reportVo) {
        if (ObjectUtils.isEmpty(reportVo.getTransactionContractNum())) {
            return;
        }
        LambdaQueryWrapper<TbBusinessOwnershipTransfer> eq = Wrappers.lambdaQuery(TbBusinessOwnershipTransfer.class)
                .eq(TbBusinessOwnershipTransfer::getTransactionReportId, reportVo.getId());
        TbBusinessOwnershipTransfer tbBusinessOwnershipTransfer = ownershipTransferMapper.selectOne(eq);
        reportVo.setOwnershipTransfer(ownershipTransferConvert.dataToVo(tbBusinessOwnershipTransfer));
    }

    /**
     * 设置房源信息
     *
     * @param houseId
     * @param reportVo
     */
    private void setHouseInfo(Long houseId, TransactionReportVo reportVo) {
        if (ObjectUtils.isEmpty(houseId)) {
            return;
        }
        Optional<HouseInfoVo> first = dataServerClient.listHouseInfoByIds(List.of(houseId))
                .stream()
                .filter(item -> item.getId().equals(houseId))
                .findFirst();
        first.ifPresent(reportVo::setHouseInfo);
    }

    /**
     * 设置车位信息
     * @param parkingSpaceId
     * @param reportVo
     */
    private void setParkingSpaceInfo(Long parkingSpaceId, TransactionReportVo reportVo) {
        ParkingSpaceVo parkingSpaceVo1 = dataServerClient.getParkingSpaceByIdList(List.of(parkingSpaceId))
                .stream()
                .filter(Objects::nonNull)
                .map(item -> {
                    ParkingSpaceVo parkingSpaceVo = parkingSpaceConvert.resp2Vo(item);
                    parkingSpaceVo.setPictureList(fileInfoConvert.resp2Vo(item.getPictureList()));
                    return parkingSpaceVo;
                }).collect(Collectors.toMap(ParkingSpaceVo::getId, Function.identity(), (existing, next) -> existing))
                .get(parkingSpaceId);
        if (!ObjectUtils.isEmpty(parkingSpaceVo1)) {
            reportVo.setParkingSpaceInfo(parkingSpaceVo1);
        }
    }

    /**
     * 新增文件业务表记录
     *
     * @param businessId 业务主键
     * @param fileId     文件Id
     */
    private void insertFileInfo(Long businessId, Long fileId, String businessType) {
        if (ObjectUtils.isEmpty(fileId)) {
            return;
        }
        FileInfoResp fileInfo = new FileInfoResp();
        fileInfo.setBusinessId(businessId);
        fileInfo.setFileId(fileId);
        fileInfo.setBusinessType(businessType);
        fileInfo.setId(snowFlake.nextId());
        dataServerClient.innerBatchSave(List.of(fileInfo));
    }

    /**
     * 删除合同人信息、收佣信息、文件信息
     *
     * @param id
     * @param deleteOrderInfo 是否删除绑定了订单信息的 收佣信息
     */
    private void deleteRelevantInfo(Long id, Boolean deleteOrderInfo) {
        // 清空文件记录表相关信息
        LambdaQueryWrapper<TbBusinessReceiveCommissionPlan> planLambdaQueryWrapper = Wrappers.lambdaQuery(TbBusinessReceiveCommissionPlan.class)
                .eq(TbBusinessReceiveCommissionPlan::getReportId, id);
        List<TbBusinessReceiveCommissionPlan> tbBusinessReceiveCommissionPlans = receiveCommissionPlanMapper.selectList(planLambdaQueryWrapper);
        List<Long> list = new ArrayList<>(tbBusinessReceiveCommissionPlans
                .stream().map(TbBusinessReceiveCommissionPlan::getId)
                .toList());
        list.add(id);
        dataServerClient.deleteByBusinessIdList(list);

        LambdaUpdateWrapper<TbBusinessReceiveCommissionPlan> eq = Wrappers.lambdaUpdate(TbBusinessReceiveCommissionPlan.class)
                .eq(TbBusinessReceiveCommissionPlan::getReportId, id)
                .eq(TbBusinessReceiveCommissionPlan::getIsDeleted, RsmsConstant.DEL_FLAG_NO);
        if (!ObjectUtils.isEmpty(deleteOrderInfo) && !deleteOrderInfo) {
            eq.isNull(TbBusinessReceiveCommissionPlan::getOrderId)
                    .isNull(TbBusinessReceiveCommissionPlan::getOrderCode)
                    .isNull(TbBusinessReceiveCommissionPlan::getSubOrderId)
                    .isNull(TbBusinessReceiveCommissionPlan::getSubOrderCode)
                    .isNull(TbBusinessReceiveCommissionPlan::getOrderStatus);
        }
        eq.set(TbBusinessReceiveCommissionPlan::getIsDeleted, RsmsConstant.DEL_FLAG_YES)
                .set(TbBusinessReceiveCommissionPlan::getUpdateUser, StpUtil.getLoginIdAsLong());
        receiveCommissionPlanMapper.update(eq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateInfo(String contractNum) {
        TbBusinessContract tbBizContract = bizContractMapper.selectOne(Wrappers.lambdaQuery(TbBusinessContract.class)
                .eq(TbBusinessContract::getContractNumber, contractNum));
        if (ObjectUtils.isEmpty(tbBizContract)) {
            throw new BusinessException(BusinessEnum.CONTRACT_ID_NOT_EXIST);
        }
        if (!DropdownEnum.CONTRACT_STATUS_SIGNED_FINISH.getDictKey().equals(tbBizContract.getContractStatus())) {
            throw new BusinessException(BusinessEnum.CONTRACT_STATUS_ERROR);
        }
        TbBusinessContractSignMapping tbBusinessContractSignMapping = contractSignMappingMapper.selectOne(Wrappers.lambdaQuery(TbBusinessContractSignMapping.class)
                .eq(TbBusinessContractSignMapping::getContractType, tbBizContract.getContractType()));
        //  未在映射表配置，则默认合同类型不支持生成成交报告
        if (ObjectUtils.isEmpty(tbBusinessContractSignMapping)) {
            throw new BusinessException(BusinessEnum.NOT_SUPPORT_CONTRACT_TYPE_CONVERT_TRANSACTION_ERROR);
        }
        TransactionReportAddParams transactionReportAddParams = new TransactionReportAddParams();
        transactionReportAddParams.setTransactionContractNum(tbBizContract.getContractNumber());
        Optional<String> any1 = Arrays.stream(DropdownEnum.values())
                .filter(dropdownEnum -> "deposit_category".equals(dropdownEnum.getDictCode()))
                .filter(dropdownEnum -> !ObjectUtils.isEmpty(tbBusinessContractSignMapping.getTransactionCategory()) &&
                        tbBusinessContractSignMapping.getTransactionCategory().equals(dropdownEnum.getDictValue()))
                .map(DropdownEnum::getDictKey)
                .findAny();
        if (any1.isEmpty()) {
            log.info("合同签约类型映射表 查询对应的 成交报告类目失败，合同类型：{}", tbBusinessContractSignMapping.getTransactionCategory());
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }
        transactionReportAddParams.setCategory(any1.get());
        transactionReportAddParams.setCustomerId(tbBizContract.getCustomerId());
        transactionReportAddParams.setHouseId(tbBizContract.getHouseId());
        transactionReportAddParams.setParkingSpaceId(tbBizContract.getParkingSpaceId());
        transactionReportAddParams.setNewHouseCommunityId(tbBizContract.getNewHouseCommunityId());
        transactionReportAddParams.setBuilding(tbBizContract.getBuilding());
        transactionReportAddParams.setUnit(tbBizContract.getUnit());
        transactionReportAddParams.setHouseNumber(tbBizContract.getHouseNumber());
        transactionReportAddParams.setOwnerInfo(tbBizContract.getLeaserUserName() + "|" + tbBizContract.getLeaserMobile());
        transactionReportAddParams.setDealDate(tbBizContract.getContractSignDate().toLocalDate());
        transactionReportAddParams.setDealAmount(tbBizContract.getTransactionPrice());
        transactionReportAddParams.setBrokerId(tbBizContract.getCreateUser());
        transactionReportAddParams.setOwnerCommission(tbBizContract.getLeaserCommission());
        transactionReportAddParams.setCustomerCommission(tbBizContract.getCustomerCommission());
        List<TransactionReportFileAddParams> reportFileAddParams = new LinkedList<>();

        //附件信息
        ResultVo<List<DataFileInfoVo>> resultVo = dataServerClient.listFileInfo(tbBizContract.getId(), null, true);
        List<DataFileInfoVo> contractFileList = resultVo.getData();
        if (!ObjectUtils.isEmpty(contractFileList)) {
            Map<String, List<DataFileInfoVo>> groupFileMap = contractFileList
                    .stream().collect(Collectors.groupingBy(DataFileInfoVo::getBusinessType));
            groupFileMap.forEach((k, v) -> {
                if (RsmsConstant.LEASER_OWNERSHIP.equals(k)) {
                    v.forEach(i -> {
                        TransactionReportFileAddParams fileAddParams = new TransactionReportFileAddParams();
                        fileAddParams.setId(i.getFileId());
                        fileAddParams.setBusinessType(DropdownEnum.FILE_BUSINESS_TYPE_OWNERSHIP.getDictKey());
                        reportFileAddParams.add(fileAddParams);
                    });
                } else if (RsmsConstant.IDENTITY.equals(k)) {
                    v.forEach(i -> {
                        TransactionReportFileAddParams fileAddParams = new TransactionReportFileAddParams();
                        fileAddParams.setId(i.getFileId());
                        fileAddParams.setBusinessType(DropdownEnum.FILE_BUSINESS_TYPE_IDENTITY.getDictKey());
                        reportFileAddParams.add(fileAddParams);
                    });
                } else if (RsmsConstant.HOUSE_HANDOVER.equals(k)) {
                    v.forEach(i -> {
                        TransactionReportFileAddParams fileAddParams = new TransactionReportFileAddParams();
                        fileAddParams.setId(i.getFileId());
                        fileAddParams.setBusinessType(DropdownEnum.FILE_BUSINESS_TYPE_OTHER.getDictKey());
                        reportFileAddParams.add(fileAddParams);
                    });
                }
            });
        }
        transactionReportAddParams.setReportFiles(reportFileAddParams);
        transactionReportAddParams.setSignDate(tbBizContract.getContractSignDate().toLocalDate());
        // 合同双方信息
        ContractInfoAddParams contractInfoAddParams = new ContractInfoAddParams();
        contractInfoAddParams.setOwnerName(tbBizContract.getLeaserUserName());
        contractInfoAddParams.setOwnerIcardNum(tbBizContract.getLeaserIdNumber());
        contractInfoAddParams.setOwnerMobile(tbBizContract.getLeaserMobile());
        contractInfoAddParams.setOwnerAddress(tbBizContract.getLeaserAddress());

        Map<Long, String> customerNameMap = dataServerClient.innerQueryCustomerByIdList(List.of(tbBizContract.getCustomerId()))
                .stream()
                .collect(Collectors.toMap(CustomerSourceResp::getId, CustomerSourceResp::getName));
        contractInfoAddParams.setCustomerName(customerNameMap.get(tbBizContract.getCustomerId()));
        contractInfoAddParams.setCustomerIcardNum(tbBizContract.getCustomerIdNumber());
        contractInfoAddParams.setCustomerMobile(tbBizContract.getCustomerMobile());
        contractInfoAddParams.setCustomerAddress(tbBizContract.getCustomerAddress());
        transactionReportAddParams.setContractInfo(contractInfoAddParams);

        ReceiveCommissionPlanAddParams customerPlan = new ReceiveCommissionPlanAddParams();
        ReceiveCommissionPlanAddParams ownerPlan = new ReceiveCommissionPlanAddParams();

        List<ReceiveCommunityVo> receiveCommunityList = new LinkedList<>();
        // 生成成交报告时，费用类型从合同的映射信息配置表中取
        // 设置收费项ID
        if (!ObjectUtils.isEmpty(tbBizContract.getHouseId())) {
            HouseInfoVo houseInfoVo = dataServerClient.listHouseInfoByIds(List.of(tbBizContract.getHouseId()))
                    .stream()
                    .collect(Collectors.toMap(HouseInfoVo::getId, Function.identity(), (existing, next) -> existing))
                    .get(tbBizContract.getHouseId());
            receiveCommunityList.addAll(getReceiveCommunityList(houseInfoVo.getCommunityId(), null, contractNum));
        }else if (!ObjectUtils.isEmpty(tbBizContract.getParkingSpaceId())) {
            // 新房、车位类合同时，获取收费项
            ParkingSpaceResp parkingSpaceResp = dataServerClient.getParkingSpaceByIdList(List.of(tbBizContract.getParkingSpaceId()))
                    .stream()
                    .collect(Collectors.toMap(ParkingSpaceResp::getId, Function.identity(), (existing, next) -> existing))
                    .get(tbBizContract.getParkingSpaceId());
            receiveCommunityList.addAll(getReceiveCommunityList(parkingSpaceResp.getCommunityId(), null, contractNum));
        }else if (!ObjectUtils.isEmpty(tbBizContract.getNewHouseCommunityId())) {
            receiveCommunityList.addAll(getReceiveCommunityList(null, tbBizContract.getNewHouseCommunityId(), contractNum));
        }
        Optional<ReceiveCommunityVo> any = receiveCommunityList.stream()
                .filter(i -> {
                    return !ObjectUtils.isEmpty(tbBusinessContractSignMapping.getTransactionFeeTypeCode())
                            && i.getZsItemCode().equals(tbBusinessContractSignMapping.getTransactionFeeTypeCode());
                })
                .findAny();
        if (any.isEmpty()) {
            log.error("未配置合同类型 ：{} 与成交报告时费用类型映射信息信息....", tbBizContract.getContractType());
            throw new BusinessException(BusinessEnum.RECEIVECOMMUNITY_NOT_EXIST_ERROR);
        }
        ReceiveCommunityVo receiveCommunity = any.get();
        customerPlan.setCostItemId(receiveCommunity.getItemId());
        ownerPlan.setCostItemId(receiveCommunity.getItemId());

        customerPlan.setCostItemCode(tbBusinessContractSignMapping.getTransactionFeeTypeCode());
        ownerPlan.setCostItemCode(tbBusinessContractSignMapping.getTransactionFeeTypeCode());

        customerPlan.setCostItemDesc(tbBusinessContractSignMapping.getTransactionFeeType());
        customerPlan.setPayer(DropdownEnum.TRANSACTION_PAYER_CUSTOMER.getDictKey());
        customerPlan.setReceivableCommission(tbBizContract.getCustomerCommission());
        customerPlan.setExpectPayment(LocalDate.now());
        customerPlan.setReceivedCommission(new BigDecimal("0.00"));


        ownerPlan.setCostItemDesc(tbBusinessContractSignMapping.getTransactionFeeType());
        ownerPlan.setPayer(DropdownEnum.TRANSACTION_PAYER_OWNER.getDictKey());
        ownerPlan.setReceivableCommission(tbBizContract.getLeaserCommission());
        ownerPlan.setExpectPayment(LocalDate.now());
        ownerPlan.setReceivedCommission(new BigDecimal("0.00"));


        transactionReportAddParams.setReceiveCommissionPlans(List.of(customerPlan, ownerPlan));
        saveOrUpdate(transactionReportAddParams);
        //更新合同状态
        tbBizContract.setContractStatus(DropdownEnum.CONTRACT_STATUS_GENERATED_REPORT.getDictKey());
        tbBizContract.setUpdateUser(AuthUtil.getLoginIdAsLong());
        bizContractMapper.updateById(tbBizContract);
    }

    @Override
    public BigDecimal achievementStatistics(Long userId) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime firstDayOfMonth = LocalDateTime.now().withDayOfMonth(1);
        String first = firstDayOfMonth.format(dateTimeFormatter);
        LocalDateTime endDayOfMonth = LocalDateTime.now().withDayOfMonth(LocalDateTime.now().toLocalDate().lengthOfMonth());
        String end = endDayOfMonth.format(dateTimeFormatter);
        return receiveCommissionPlanMapper.selectList(Wrappers.lambdaQuery(TbBusinessReceiveCommissionPlan.class)
                        .eq(TbBusinessReceiveCommissionPlan::getCreateUser, userId)
                        .in(TbBusinessReceiveCommissionPlan::getOrderStatus, List.of(
                                DropdownEnum.TRANSFER_RECEIVE_PART_PAY.getDictKey(),
                                DropdownEnum.TRANSFER_RECEIVE_PAYED.getDictKey()
                        ))
                        .between(TbBusinessReceiveCommissionPlan::getActualPayment, first + " 00:00:00", end + " 23:59:59")
                ).stream()
                .map(TbBusinessReceiveCommissionPlan::getReceivedCommission)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    @Override
    public Long turnoverStatistics(Long userId) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime firstDayOfMonth = LocalDateTime.now().withDayOfMonth(1);
        String first = firstDayOfMonth.format(dateTimeFormatter);
        LocalDateTime endDayOfMonth = LocalDateTime.now().withDayOfMonth(LocalDateTime.now().toLocalDate().lengthOfMonth());
        String end = endDayOfMonth.format(dateTimeFormatter);
        return transactionReportMapper.selectCount(Wrappers.lambdaQuery(TbBusinessTransactionReport.class)
                .eq(TbBusinessTransactionReport::getBrokerId, userId)
                .in(TbBusinessTransactionReport::getStatus, List.of(
                        DropdownEnum.TRANSFER_STATUS_REVIEWED.getDictKey(),
                        DropdownEnum.TRANSFER_STATUS_CLOSED.getDictKey(),
                        DropdownEnum.TRANSFER_STATUS_UNPAY.getDictKey(),
                        DropdownEnum.TRANSFER_STATUS_PART_PAY.getDictKey(),
                        DropdownEnum.TRANSFER_STATUS_PAYED.getDictKey()
                ))
                .between(TbBusinessTransactionReport::getDealDate, first + " 00:00:00", end + " 23:59:59"));

    }

    /**
     * 分页记录分别赋值权限
     *
     * @param permissionCodeMap
     * @return
     */
    private List<String> givePermissions(Map<String, List<Long>> permissionCodeMap, TbBusinessTransactionReport report, String status) {
        List<String> permissions = new LinkedList<>();
        permissionCodeMap.keySet().forEach(key -> {
            List<Long> permissionUserIdOnCode = permissionCodeMap.getOrDefault(key, List.of());
            Stream.of(
                            PermissionIsAllValueEnum.ALL.getValue(),
                            report.getBrokerId()
                    ).filter(permissionUserIdOnCode::contains)
                    .findAny()
                    .ifPresent(permissionUserId -> permissions.add(key));
        });
        if (DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey().equals(status)) {
            // 未审核状态，为草稿状态 仅允许编辑，删除
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_AUDIT_OR_REJECT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVERSE_AUDIT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVIEW_OR_REJECT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_CLOSE.getCode());
        }else if(DropdownEnum.TRANSFER_STATUS_AUDITING.getDictKey().equals(status)){
            // 审核中 允许查看、审核\驳回
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_EDIT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_DELETE.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVERSE_AUDIT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVIEW_OR_REJECT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_CLOSE.getCode());
        }else if(DropdownEnum.TRANSFER_STATUS_REVIEWING.getDictKey().equals(status)){
            // 复核中 仅允许 查看、复核/驳回
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_EDIT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_DELETE.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_AUDIT_OR_REJECT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVERSE_AUDIT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_CLOSE.getCode());
        } else if (DropdownEnum.TRANSFER_STATUS_UNPAY.getDictKey().equals(status)
                || DropdownEnum.TRANSFER_STATUS_PART_PAY.getDictKey().equals(status)) {
            // 待收款、部分付款 仅允许 查看、反审核
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_EDIT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_DELETE.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_AUDIT_OR_REJECT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVIEW_OR_REJECT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_CLOSE.getCode());
        } else if (DropdownEnum.TRANSFER_STATUS_REVIEWED.getDictKey().equals(status)
                || DropdownEnum.TRANSFER_STATUS_PAYED.getDictKey().equals(status)) {
            // 已复核、已收讫 仅允许 查看、反审核、结案
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_EDIT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_DELETE.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_AUDIT_OR_REJECT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVIEW_OR_REJECT.getCode());
        } else if (DropdownEnum.TRANSFER_STATUS_CLOSED.getDictKey().equals(status)) {
            // 已结案 仅允许 查看
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_EDIT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_DELETE.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_AUDIT_OR_REJECT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVERSE_AUDIT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_REVIEW_OR_REJECT.getCode());
            permissions.remove(PermissionEnum.TRANSACTION_MANAGE_TRANSACTION_REPORT_CLOSE.getCode());
        }
        return permissions;
    }

    /**
     * 提交时检查参数
     * @param params
     */
    private void validateParams(TransactionReportAddParams params) {
        if (ObjectUtils.isEmpty(params)) {
            throw new BusinessException("成交报告参数不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(params.getTransactionContractNum())) {
            // 成交合同编号
            throw new BusinessException("成交合同编号不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(params.getCategory())) {
            // 类目
            throw new BusinessException("类目参数不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(params.getCustomerId())) {
            // 客户ID
            throw new BusinessException("客户ID不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(params.getHouseId()) &&  List.of(
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_1.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_2.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_5.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_6.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_7.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_8.getDictKey()
        ).contains(params.getCategory())) {
            // 房源ID
            throw new BusinessException("房源ID不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(params.getParkingSpaceId()) &&  List.of(
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_3.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_4.getDictKey()
        ).contains(params.getCategory())) {
            // 车位ID
            throw new BusinessException("车位ID不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (DropdownEnum.DEPOSIT_CATEGORY_DPS_C_9.getDictKey().equals(params.getCategory()) && (
                ObjectUtils.isEmpty(params.getBuilding())
                || ObjectUtils.isEmpty(params.getUnit())
                || ObjectUtils.isEmpty(params.getHouseNumber())
                || ObjectUtils.isEmpty(params.getNewHouseCommunityId())
                )) {
            // 新房
            throw new BusinessException("新房项目ID、楼栋号、单元、房号均不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (!DropdownEnum.DEPOSIT_CATEGORY_DPS_C_9.getDictKey().equals(params.getCategory())) {
            if (ObjectUtils.isEmpty(params.getOwnerInfo())) {
                // 业主信息
                throw new BusinessException("业主信息不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
            }
            if (ObjectUtils.isEmpty(params.getOwnerCommission())) {
                // 业主佣金
                throw new BusinessException("业主佣金不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
            }
        }

        if (ObjectUtils.isEmpty(params.getDealDate())) {
            // 成交日期
            throw new BusinessException("成交日期不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(params.getDealAmount())) {
            // 成交金额
            throw new BusinessException("成交金额不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(params.getBrokerId())) {
            // 经纪人ID
            throw new BusinessException("经纪人ID不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }

        if (ObjectUtils.isEmpty(params.getCustomerCommission())) {
            // 客户佣金
            throw new BusinessException("客户佣金不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(params.getReportFiles())) {
            // 合同附件
            throw new BusinessException("合同附件不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        params.getReportFiles().forEach(file -> {
            if (ObjectUtils.isEmpty(file.getId())) {
                // 文件ID
                throw new BusinessException("文件ID不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
            }
            if (ObjectUtils.isEmpty(file.getBusinessType())) {
                // 业务类型
                throw new BusinessException("业务类型不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
            }
        });
        if (ObjectUtils.isEmpty(params.getSignDate())) {
            // 签约日期
            throw new BusinessException("签约日期不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(params.getContractInfo())) {
            // 合同双方信息
            throw new BusinessException("合同双方信息不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (!DropdownEnum.DEPOSIT_CATEGORY_DPS_C_9.getDictKey().equals(params.getCategory())) {
            if (ObjectUtils.isEmpty(params.getContractInfo().getCustomerAddress())) {
                // 乙方地址
                throw new BusinessException("乙方地址不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
            }
            if (ObjectUtils.isEmpty(params.getContractInfo().getCustomerMobile())) {
                // 乙方手机号
                throw new BusinessException("乙方手机号不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
            }
            if (ObjectUtils.isEmpty(params.getContractInfo().getCustomerName())) {
                // 乙方姓名
                throw new BusinessException("乙方姓名不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
            }
        }


        if (ObjectUtils.isEmpty(params.getContractInfo().getOwnerAddress())) {
            // 甲方地址
            throw new BusinessException("甲方地址不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(params.getContractInfo().getOwnerMobile())) {
            // 甲方手机号
            throw new BusinessException("甲方手机号不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(params.getContractInfo().getOwnerName())) {
            // 甲方姓名
            throw new BusinessException("甲方姓名不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }

        if (ObjectUtils.isEmpty(params.getReceiveCommissionPlans())) {
            // 收佣信息
            throw new BusinessException("收佣信息不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
        params.getReceiveCommissionPlans().forEach(plan -> {
            if (ObjectUtils.isEmpty(plan.getCostItemDesc())) {
                // 费用项目
                throw new BusinessException("费用项目不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
            }
            if (ObjectUtils.isEmpty(plan.getPayer())) {
                // 交费人
                throw new BusinessException("交费人不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
            }
            if (ObjectUtils.isEmpty(plan.getReceivableCommission())) {
                // 应收佣金
                throw new BusinessException("应收佣金不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
            }
            if (ObjectUtils.isEmpty(plan.getExpectPayment())) {
                // 预期交费时间
                throw new BusinessException("预期交费时间不能为空",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
            }
        });
        Map<String, List<ReceiveCommissionPlanAddParams>> collect = params.getReceiveCommissionPlans()
                .stream()
                .collect(Collectors.groupingBy(ReceiveCommissionPlanAddParams::getPayer));
        List<ReceiveCommissionPlanAddParams> ownerCommissionPlanList = collect.get(DropdownEnum.TRANSACTION_PAYER_OWNER.getDictKey());
        if (!ObjectUtils.isEmpty(ownerCommissionPlanList)) {
            BigDecimal ownerCommission = ownerCommissionPlanList
                    .stream()
                    .map(ReceiveCommissionPlanAddParams::getReceivableCommission)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (ownerCommission.compareTo(params.getOwnerCommission()) > 0) {
                throw new BusinessException("收佣计划中业主收佣金额不得大于业主佣金",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
            }
        }

        // 前端传过来的收佣行客户佣金总计
        BigDecimal customerCommission = new BigDecimal("0.00");
        List<ReceiveCommissionPlanAddParams> customerCommissionPlanList = collect.get(DropdownEnum.TRANSACTION_PAYER_CUSTOMER.getDictKey());
        if (!ObjectUtils.isEmpty(customerCommissionPlanList)) {
            customerCommission = customerCommissionPlanList
                    .stream()
                    .filter(info -> ObjectUtils.isEmpty(info.getDepositId()))
                    .map(ReceiveCommissionPlanAddParams::getReceivableCommission)
                    .reduce(customerCommission, BigDecimal::add);
        }

        // 如果是已收款成交报告，则必须 收佣行 客户佣金 + 库里的转佣金额 <= 表头客户佣金总额
        if (!ObjectUtils.isEmpty(params.getId())) {
            // 查询库里的 意向金转佣金额
            List<TbBusinessReceiveCommissionPlan> commissionPlans = receiveCommissionPlanMapper.selectList(Wrappers.lambdaQuery(TbBusinessReceiveCommissionPlan.class)
                    .eq(TbBusinessReceiveCommissionPlan::getReportId, params.getId())
                    .isNotNull(TbBusinessReceiveCommissionPlan::getDepositId));
            if (!ObjectUtils.isEmpty(commissionPlans)) {
                // 加上库里的转佣金额
                BigDecimal reduce = commissionPlans.stream()
                        .map(TbBusinessReceiveCommissionPlan::getReceivedCommission)
                        .reduce(customerCommission, BigDecimal::add);
                if (reduce.compareTo(params.getCustomerCommission()) > 0) {
                    throw new BusinessException(BusinessEnum.TRANSACTION_REPORT_CONVERT_COMMISSION_ERROR);
                }
            }
        }
        // 如果是未收款过成交报告， 则收佣行客户佣金 + 意向金转佣金额 <= 表头客户佣金
        if (!ObjectUtils.isEmpty(params.getDepositId())) {
            LambdaQueryWrapper<TbBusinessTransactionInfo> eq = Wrappers.lambdaQuery(TbBusinessTransactionInfo.class)
                    .eq(TbBusinessTransactionInfo::getDepositId, params.getDepositId())
                    .notIn(TbBusinessTransactionInfo::getTransactionType, List.of(
                            DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey(),
                            DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey()
                    ));
            List<TbBusinessTransactionInfo> transactionInfos = transactionInfoMapper.selectList(eq);

            // 如果成交报告已绑定当前意向金，则跳过判断
            TbBusinessTransactionReport report = null;
            if (!ObjectUtils.isEmpty(params.getId())) {
                report = transactionReportMapper.selectById(params.getId());
            }

            // 一笔意向金仅允许进行一次转佣
            if (!ObjectUtils.isEmpty(transactionInfos)
                    && (ObjectUtils.isEmpty(report) 
                            || ObjectUtils.isEmpty(report.getDepositId())
                            || !report.getDepositId().equals(params.getDepositId())
                    )
            ) {
                throw new BusinessException(BusinessEnum.TRANSACTION_REPORT_DEPOSIT_TRANSACTION_ERROR);
            }

            // 意向金 不能关联非草稿状态的成交报告
            Optional<TbBusinessTransactionReport> any = transactionReportMapper.selectList(Wrappers.lambdaQuery(TbBusinessTransactionReport.class)
                            .eq(TbBusinessTransactionReport::getDepositId, params.getDepositId()))
                    .stream()
                    .filter(r -> {
                        return !DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey().equals(r.getStatus());
                    }).findAny();
            if (any.isPresent()) {
                throw new BusinessException(BusinessEnum.DEPOSIT_REPEAT_LINKED_ERROR);
            }
            // 意向金剩余金额 + 收款金额 不能大于 表头里的 客户佣金金额
            DepositVo statistics = depositService.getAmountStatistics(params.getDepositId());
            BigDecimal balanceAmount = new BigDecimal("0.00");
            if (!ObjectUtils.isEmpty(statistics)) {
                balanceAmount = customerCommission.add(statistics.getReceiveAmount().subtract(statistics.getRefundAmount()).subtract(statistics.getCommissionAmount()));
            }
            if (balanceAmount.compareTo(params.getCustomerCommission()) > 0) {
                throw new BusinessException(BusinessEnum.TRANSACTION_REPORT_CONVERT_COMMISSION_ERROR);
            }
        }
        if (customerCommission.compareTo(params.getCustomerCommission()) > 0) {
            throw new BusinessException(BusinessEnum.TRANSACTION_REPORT_CONVERT_COMMISSION_ERROR);
        }
        // 成交合同编号不能重复
        LambdaQueryWrapper<TbBusinessTransactionReport> eq = Wrappers.lambdaQuery(TbBusinessTransactionReport.class)
                .eq(TbBusinessTransactionReport::getTransactionContractNum, params.getTransactionContractNum())
                .notIn(TbBusinessTransactionReport::getStatus, List.of(DropdownEnum.TRANSFER_STATUS_UNADUDIT.getDictKey()));
        if (!ObjectUtils.isEmpty(params.getId())) {
            eq.notIn(TbBusinessTransactionReport::getId, List.of( params.getId()));
        }
        List<TbBusinessTransactionReport> tbBusinessTransactionReports = transactionReportMapper.selectList(eq);
        if (!ObjectUtils.isEmpty(tbBusinessTransactionReports)) {
            throw new BusinessException("成交合同编号重复",BusinessEnum.TRANSACTION_REPORT_PARAM_NOT_NULL_ERROR.getCode());
        }
    }

    /**
     * 改变房源、客源状态
     * @param houseId
     * @param customerId
     * @param category
     */
    private void changeHouseAndCustomerStatus(Long houseId, Long customerId, String category) {
        // 我售相关状态
        List<String> sellKey = List.of(
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_1.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_3.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_6.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_8.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_9.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_10.getDictKey()
        );
        // 我租相关状态
        List<String> rentKey = List.of(
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_2.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_4.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_5.getDictKey(),
                DropdownEnum.DEPOSIT_CATEGORY_DPS_C_7.getDictKey()
        );
        String customerStatus = null;
        String customerProperties = null;
        String houseRentStatus = null;
        String houseProp = null;
        // 成交类型是住宅二手房买卖、车位二手房买卖、商铺二手房买卖、写字楼二手房买卖、住宅新盘买卖、车位新盘买卖，
        // 则对应的房屋信息【租售状态】变为【我售】，【房源属性】变为【封盘】，
        // 对应的客户信息【客源状态】变为【我售】，【客户属性】变为【成交客】
        if (sellKey.contains(category)) {
            houseRentStatus = DropdownEnum.RENT_STATUS_SELL_ME.getDictKey();
            customerStatus = DropdownEnum.CUSTOMER_SOURCE_STATUS_MY_SELL.getDictKey();
        } else if (rentKey.contains(category)) {
            // 住宅二手房租赁、车位二手房租赁、商铺二手房租赁、写字楼二手房租赁，
            // 则对应的房屋信息【租售状态】变为【我租】，【房源属性】变为【封盘】，
            // 对应的客户信息【客源状态】变为【我租】，【客户属性】变为【成交客】
            houseRentStatus = DropdownEnum.RENT_STATUS_RENT_ME.getDictKey();
            customerStatus = DropdownEnum.CUSTOMER_SOURCE_STATUS_MY_RENT.getDictKey();
        }
        houseProp = DropdownEnum.HOUSE_PROP_CLOSE_PLATE.getDictKey();
        customerProperties = DropdownEnum.CUSTOMER_SOURCE_PROPERTIES_OK_CUSTOMER.getDictKey();
        if (!ObjectUtils.isEmpty(houseId)) {
            dataServerClient.innerUpdateHouseStatus(houseId, houseRentStatus, houseProp);
        }
        if (!ObjectUtils.isEmpty(customerId)) {
            dataServerClient.innerUpdateCustomerStatus(customerId, customerStatus, customerProperties);
        }
    }


    @Override
    public List<TransactionReportResp> queryTransactionReportByCustomerId(List<Long> customerIds) {
        if (!ObjectUtils.isEmpty(customerIds)) {
            List<TbBusinessTransactionReport> tbBusinessTransactionReports = transactionReportMapper.selectList(Wrappers.lambdaQuery(TbBusinessTransactionReport.class)
                    .in(TbBusinessTransactionReport::getCustomerId, customerIds));
            return reportConvert.data2Resp(tbBusinessTransactionReports);
        }
        return List.of();
    }

    @Override
    public List<TransactionReportResp> queryTransactionReportIdById(List<Long> ids) {
        if (!ObjectUtils.isEmpty(ids)) {
            List<TbBusinessTransactionReport> tbBusinessTransactionReports = transactionReportMapper.selectList(Wrappers.lambdaQuery(TbBusinessTransactionReport.class)
                    .in(TbBusinessTransactionReport::getId, ids));
            return reportConvert.data2Resp(tbBusinessTransactionReports);
        }
        return List.of();
    }

    @Override
    public List<TransactionReportResp> selectInfoOnOperateLog(List<Long> businessIdList) {
        if (!ObjectUtils.isEmpty(businessIdList)) {
            List<TbBusinessTransactionReport> tbBusinessTransactionReports = transactionReportMapper.selectInfoOnOperateLog(businessIdList);
            return reportConvert.data2Resp(tbBusinessTransactionReports);
        }
        return List.of();
    }

    @Override
    public List<TransactionReportResp> queryTransactionReportByAssetsId(Long assetsId) {
        if (!ObjectUtils.isEmpty(assetsId)) {
            List<TbBusinessTransactionReport> tbBusinessTransactionReports = transactionReportMapper.selectList(Wrappers.lambdaQuery(TbBusinessTransactionReport.class)
                    .like(TbBusinessTransactionReport::getHouseId, assetsId)
                    .or()
                    .like(TbBusinessTransactionReport::getParkingSpaceId, assetsId)
                    .or()
                    .like(TbBusinessTransactionReport::getNewHouseCommunityId, assetsId));
            return reportConvert.data2Resp(tbBusinessTransactionReports);
        }
        return List.of();
    }

    /**
     * 根据房源 、车位 、新房项目、合同号 查询 收费项，所有参数不允许同时为空
     * @param houseId
     * @param parkingSpaceId
     * @param newHouseCommunityId
     * @param transactionContractNum
     * @return
     */
    private List<ReceiveCommunityVo> getReceiveCommunityList(Long houseId, Long parkingSpaceId, Long newHouseCommunityId, String transactionContractNum) {
        Long communityId = null;
        if (!ObjectUtils.isEmpty(houseId)) {
            HouseInfoVo houseInfoVo = dataServerClient.listHouseInfoByIds(List.of(houseId))
                    .stream()
                    .collect(Collectors.toMap(HouseInfoVo::getId, Function.identity()))
                    .get(houseId);
            if (!ObjectUtils.isEmpty(houseInfoVo)) {
                communityId = houseInfoVo.getCommunityId();
            }
        } else if (!ObjectUtils.isEmpty(parkingSpaceId)) {
            ParkingSpaceResp parkingSpaceResp = dataServerClient.getParkingSpaceByIdList(List.of(parkingSpaceId))
                    .stream()
                    .collect(Collectors.toMap(ParkingSpaceResp::getId, Function.identity()))
                    .get(parkingSpaceId);
            if (!ObjectUtils.isEmpty(parkingSpaceResp)) {
                communityId = parkingSpaceResp.getCommunityId();
            }
        }
        return getReceiveCommunityList(communityId, newHouseCommunityId, transactionContractNum);
    }

    /**
     * 根据项目ID 查询收费项（仅适用二手房项目）
     *
     * @param communityId
     * @param transactionContractNum
     * @return
     */
    @Override
    public List<ReceiveCommunityVo> getReceiveCommunityList(Long communityId, Long newHouseCommunityId, String transactionContractNum) {
        if (ObjectUtils.isEmpty(communityId) && ObjectUtils.isEmpty(newHouseCommunityId)) {
            return List.of();
        }
        Integer type = 5;   // 默认查询佣金的收费子类
        List<ReceiveCommunityVo> receiveCommunity = new LinkedList<>();
        if (!ObjectUtils.isEmpty(communityId)) {
            CommunityResp communityResp = dataServerClient.getCommunityListByIds(List.of(communityId))
                    .stream()
                    .collect(Collectors.toMap(CommunityResp::getId, Function.identity(), (existing, next) -> existing))
                    .get(communityId);
            if (ObjectUtils.isEmpty(communityResp)) {
                return List.of();
            }
            receiveCommunity.addAll(orderService.getReceiveCommunity(communityResp.getCommunityCode(), false, transactionContractNum, type));
        } else if (!ObjectUtils.isEmpty(newHouseCommunityId)) {
            NewHouseResp newHouseResp = dataServerClient.getNewHouseByIdList(List.of(newHouseCommunityId))
                    .stream()
                    .collect(Collectors.toMap(NewHouseResp::getId, Function.identity(), (existing, next) -> existing))
                    .get(newHouseCommunityId);
            receiveCommunity.addAll(orderService.getReceiveCommunity(newCommunityCode, false, transactionContractNum, type));
//            return orderService.getReceiveCommunity(newHouseResp.getCommunityCode(), false, transactionContractNum);
        }
        return receiveCommunity;
    }


    @Override
    public PreviewPerformanceVo previewPerformance(PreviewPerformanceParams params) {
        if (ObjectUtils.isEmpty(params)) {
            return null;
        }
        String categoryCode = params.getCategoryCode();
        if (ObjectUtils.isEmpty(categoryCode)) {
            return null;
        }
        PreviewPerformanceVo result = new PreviewPerformanceVo();

        TransactionRelResp transactionRelResp = dataServerClient.queryTransactionRelAll()
                .get(categoryCode);
        TenthsDetailsResp detailByOrderIdAndBusinessType = getCompanyTenthsDetailsRespByUserId(params.getBrokerId(), transactionRelResp.getBusinessTypeCode());
        if (ObjectUtils.isEmpty(detailByOrderIdAndBusinessType)) {
            return null;
        }
        List<AutoAllocationVoVo> autoAllocationVos = generateAutoAllocations(detailByOrderIdAndBusinessType, params.getCustomerCommission(), params.getOwnerCommission(), params.getMarketingFee());
        Map<String, Long> roleUserMap = new LinkedHashMap<>();
        CustomerSourceResp customerSourceResp = null;
        if (!ObjectUtils.isEmpty(params.getCustomerId())) {
            customerSourceResp = dataServerClient.innerQueryCustomerByIdList(List.of(params.getCustomerId()))
                    .stream()
                    .collect(Collectors.toMap(CustomerSourceResp::getId, Function.identity()))
                    .get(params.getCustomerId());
            result.setCustomerRecommendPersonId(Long.parseLong(customerSourceResp.getRecommendPersonId()));
        }

        if (!ObjectUtils.isEmpty(params.getAssetsId())) {
            if (List.of(
                    DropdownEnum.DEPOSIT_CATEGORY_DPS_C_1.getDictKey(),
                    DropdownEnum.DEPOSIT_CATEGORY_DPS_C_2.getDictKey(),
                    DropdownEnum.DEPOSIT_CATEGORY_DPS_C_5.getDictKey(),
                    DropdownEnum.DEPOSIT_CATEGORY_DPS_C_6.getDictKey(),
                    DropdownEnum.DEPOSIT_CATEGORY_DPS_C_7.getDictKey(),
                    DropdownEnum.DEPOSIT_CATEGORY_DPS_C_8.getDictKey()
            ).contains(params.getCategoryCode())) {
                // 房源
                HouseInfoVo houseInfoVo = dataServerClient.listHouseInfoByIds(List.of(params.getAssetsId()))
                        .stream()
                        .collect(Collectors.toMap(HouseInfoVo::getId, Function.identity()))
                        .get(params.getAssetsId());
                roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_HOUSE_HOUSE_INPUT.getDictKey(), houseInfoVo.getCreateUser());
                roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_HOUSE_HOUSE_MAINTAINER.getDictKey(), houseInfoVo.getMaintenanceBy());
                roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_HOUSE_HOUSE_OPENER.getDictKey(), houseInfoVo.getOpenedBy());
                roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_HOUSE_HOUSE_PIC.getDictKey(), houseInfoVo.getPicBy());
                roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_HOUSE_HOUSE_VIDEO.getDictKey(), houseInfoVo.getVideoBy());
                roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_HOUSE_HOUSE_KEY.getDictKey(), houseInfoVo.getKeyBy());
                roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_HOUSE_HOUSE_DELEGATE.getDictKey(), houseInfoVo.getDelegateBy());
                roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_HOUSE_HOUSE_BARGAIN.getDictKey(), houseInfoVo.getDickBy());

                if (!ObjectUtils.isEmpty(customerSourceResp)) {
                    roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_HOUSE_CLIENT_FIRST_INPUT.getDictKey(), customerSourceResp.getFirstRecordPersonId());
                    roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_HOUSE_CLIENT_MAINTAINER.getDictKey(), customerSourceResp.getMaintainPersonId());
                }
                // TODo 房源新增推荐人
                result.setHouseRecommendPersonId(houseInfoVo.getId());
            } else if (List.of(
                    DropdownEnum.DEPOSIT_CATEGORY_DPS_C_3.getDictKey(),
                    DropdownEnum.DEPOSIT_CATEGORY_DPS_C_4.getDictKey(),
                    DropdownEnum.DEPOSIT_CATEGORY_DPS_C_10.getDictKey()
            ).contains(params.getCategoryCode())) {
                // 车位
                ParkingSpaceResp parkingSpaceResp = dataServerClient.getParkingSpaceByIdList(List.of(params.getAssetsId()))
                        .stream()
                        .collect(Collectors.toMap(ParkingSpaceResp::getId, Function.identity()))
                        .get(params.getAssetsId());
                roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_PARKING_HOUSE_INPUT.getDictKey(), parkingSpaceResp.getCreateUser());
                roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_PARKING_HOUSE_MAINTAINER.getDictKey(), parkingSpaceResp.getMaintainer());
                roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_PARKING_HOUSE_OPENER.getDictKey(), parkingSpaceResp.getOpener());
                if (!ObjectUtils.isEmpty(customerSourceResp)) {
                    roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_NEW_HOUSE_HOUSE_CLIENT_FIRST_INPUT.getDictKey(), customerSourceResp.getFirstRecordPersonId());
                    roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_NEW_HOUSE_HOUSE_CLIENT_MAINTAINER.getDictKey(), customerSourceResp.getMaintainPersonId());
                }
            } else if (List.of(
                    DropdownEnum.DEPOSIT_CATEGORY_DPS_C_9.getDictKey()
            ).contains(params.getCategoryCode())) {
                // 新房
                NewHouseResp newHouseResp = dataServerClient.getNewHouseByIdList(List.of(params.getAssetsId()))
                        .stream()
                        .collect(Collectors.toMap(NewHouseResp::getId, Function.identity()))
                        .get(params.getAssetsId());
                if (!ObjectUtils.isEmpty(customerSourceResp)) {
                    roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_NEW_HOUSE_HOUSE_CLIENT_FIRST_INPUT.getDictKey(), customerSourceResp.getFirstRecordPersonId());
                    roleUserMap.put(DropdownEnum.FINANCES_CLINCH_SHARING_ROLE_NEW_HOUSE_HOUSE_CLIENT_MAINTAINER.getDictKey(), customerSourceResp.getMaintainPersonId());
                }
            }
            autoAllocationVos = setUserInfo(roleUserMap, autoAllocationVos);
            autoAllocationVos = computerTenthsTransfer(autoAllocationVos);
        }
        result.setAllocationItem(autoAllocationVos);
        Map<Long, String> userMap = authorServerClient.getUserDetailsMicro(Stream.of(
                                result.getHouseRecommendPersonId(),
                                result.getCustomerRecommendPersonId()
                        ).filter(Objects::nonNull)
                        .toList())
                .stream()
                .collect(Collectors.toMap(UserDetailsMicroResp::getId, UserDetailsMicroResp::getEmpName));
        result.setHouseRecommendPersonName(userMap.getOrDefault(result.getHouseRecommendPersonId(), null));
        result.setCustomerRecommendPersonName(userMap.getOrDefault(result.getCustomerRecommendPersonId(), null));
        return result;
    }

    private TenthsDetailsResp getCompanyTenthsDetailsRespByUserId(Long userId, String businessTypeCode) {
        String storeId = authorServerClient.getUserDetailsMicro(List.of(
                        userId
                )).stream()
                .collect(Collectors.toMap(UserDetailsMicroResp::getId, UserDetailsMicroResp::getStoreId))
                .get(userId);
        Long districtIdByUserId = dataServerClient.findDistrictIdByUserId(Long.parseLong(storeId));
        TenthsDetailsResp detailByOrderIdAndBusinessType = dataServerClient.getDetailByOrgIdAndBusinessType(districtIdByUserId, businessTypeCode);
        if (!ObjectUtils.isEmpty(detailByOrderIdAndBusinessType)) {
            return detailByOrderIdAndBusinessType;
        }

        // 区的没查到，查大区的
        Long regionIdByUserId = dataServerClient.findRegionIdByUserId(Long.parseLong(storeId));
        detailByOrderIdAndBusinessType = dataServerClient.getDetailByOrgIdAndBusinessType(regionIdByUserId, businessTypeCode);
        if (!ObjectUtils.isEmpty(detailByOrderIdAndBusinessType)) {
            return detailByOrderIdAndBusinessType;
        }
        return null;
    }

    private List<AutoAllocationVoVo> generateAutoAllocations(TenthsDetailsResp detailByOrderIdAndBusinessType, BigDecimal customerCommission, BigDecimal ownerCommission, BigDecimal markingFee) {
        return detailByOrderIdAndBusinessType.getData()
                .stream()
                .map(tenthsDetailsItemResp -> {
                    AutoAllocationVoVo autoAllocationVo = new AutoAllocationVoVo();
                    autoAllocationVo.setTenthsRoleCode(tenthsDetailsItemResp.getTenthsRoleCode());
                    autoAllocationVo.setTenthsRoleValue(tenthsDetailsItemResp.getTenthsRoleValue());
                    autoAllocationVo.setTenthsTransferCode(tenthsDetailsItemResp.getTenthsTransferCode());
                    autoAllocationVo.setTenthsTransferValue(tenthsDetailsItemResp.getTenthsTransferValue());

                    autoAllocationVo.setPercentageCode(detailByOrderIdAndBusinessType.getPercentageCode());
                    autoAllocationVo.setPercentageValue(DropdownEnum.getDictValue(DropdownEnum.PERCENTAGE_TYPE_PERCENTAGE.getDictCode(), detailByOrderIdAndBusinessType.getPercentageCode()));
                    autoAllocationVo.setTenthsRatio(tenthsDetailsItemResp.getTenthsRatio());
                    autoAllocationVo.setTenthsMoney(tenthsDetailsItemResp.getTenthsMoney());
                    if (!ObjectUtils.isEmpty(customerCommission)
                            && !ObjectUtils.isEmpty(ownerCommission)
                            && !ObjectUtils.isEmpty(markingFee)) {
                        // 比例分成
                        if (DropdownEnum.PERCENTAGE_TYPE_PERCENTAGE.getDictKey().equals(autoAllocationVo.getPercentageCode())) {
                            BigDecimal multiply = customerCommission.add(ownerCommission).multiply(autoAllocationVo.getTenthsRatio().divide(new BigDecimal("100")));
                            autoAllocationVo.setAmount(multiply);
                        } else { // 固定金额
                            autoAllocationVo.setAmount(autoAllocationVo.getTenthsMoney());
                        }
                    }
                    return autoAllocationVo;
                })
                .toList();
    }

    /**
     * 填充分成人信息
     * @param roleUserMap
     * @param autoAllocationVos
     */
    private List<AutoAllocationVoVo> setUserInfo(Map<String, Long> roleUserMap, List<AutoAllocationVoVo> autoAllocationVos) {
        Map<Long, UserDetailsMicroResp> userDetailsMicroRespMap = authorServerClient.getUserDetailsMicro(roleUserMap.values().stream().toList())
                .stream()
                .collect(Collectors.toMap(UserDetailsMicroResp::getId, Function.identity(), (existing, next) -> existing));
        autoAllocationVos.forEach(autoAllocationVo -> {
            if (!ObjectUtils.isEmpty(autoAllocationVo.getTenthsRoleCode())
                    && !ObjectUtils.isEmpty(roleUserMap.get(autoAllocationVo.getTenthsRoleCode()))) {
                Long userId = roleUserMap.get(autoAllocationVo.getTenthsRoleCode());
                UserDetailsMicroResp userDetailsMicroResp = userDetailsMicroRespMap.get(userId);
                autoAllocationVo.setUserId(userDetailsMicroResp.getId());
                autoAllocationVo.setUserName(userDetailsMicroResp.getEmpName());
                autoAllocationVo.setUserMobile(userDetailsMicroResp.getMobileEncode());
            }
        });
        return autoAllocationVos;
    }

    /**
     * 转移金额累计
     * @param autoAllocationVos
     */
    private List<AutoAllocationVoVo> computerTenthsTransfer(List<AutoAllocationVoVo> autoAllocationVos) {
        Map<String, List<AutoAllocationVoVo>> transferMap = autoAllocationVos.stream()
                .filter(autoAllocationVo -> !ObjectUtils.isEmpty(autoAllocationVo.getTenthsTransferCode()))
                .collect(Collectors.groupingBy(AutoAllocationVoVo::getTenthsTransferCode));
        autoAllocationVos.forEach(autoAllocationVo -> {
            if (!ObjectUtils.isEmpty(autoAllocationVo.getTenthsRoleCode())
                    && !ObjectUtils.isEmpty(transferMap.get(autoAllocationVo.getTenthsRoleCode()))) {
                BigDecimal tenthsTransferAmount = transferMap.get(autoAllocationVo.getTenthsRoleCode())
                        .stream()
                        .map(AutoAllocationVoVo::getAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                if (!ObjectUtils.isEmpty(autoAllocationVo.getAmount())) {
                    autoAllocationVo.setAmount(tenthsTransferAmount.add(autoAllocationVo.getAmount()));
                }
            }
        });
        // 清空转移角色人的 金额
        autoAllocationVos.forEach(autoAllocationVo -> {
            if (!ObjectUtils.isEmpty(transferMap.get(autoAllocationVo.getTenthsTransferCode()))) {
                autoAllocationVo.setAmount(null);
            }
        });

        return autoAllocationVos;
    }

    @Override
    public List<TbBusinessTransactionReport> queryTransactionReportBySettleStatus() {
        LambdaQueryWrapper<TbBusinessTransactionReport> queryWrapper = new LambdaQueryWrapper<>();
        //当前时间的前一天，转换为时分秒
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1).withHour(23).withMinute(59).withSecond(59);
        queryWrapper.le(TbBusinessTransactionReport::getReviewDateTime, yesterday);
        //签约结算状态为null
        queryWrapper.isNull(TbBusinessTransactionReport::getContractPerSettleStatus);
        List<TbBusinessTransactionReport> tbBusinessTransactionReports = transactionReportMapper.selectList(queryWrapper);
        if (!ObjectUtils.isEmpty(tbBusinessTransactionReports)) {
            return tbBusinessTransactionReports;
        }
        return List.of();
    }
}
