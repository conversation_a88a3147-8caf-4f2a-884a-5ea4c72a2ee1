package mixc.be.rsms.business.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.feignclient.DataServerClient;
import mixc.be.rsms.business.service.ICompanyService;
import mixc.be.rsms.business.vo.CompanyVo;
import mixc.be.rsms.pojo.data.EnterpriseResp;

/**
 * @ClassName BizContractServiceImpl
 * @Desription 合同业务实现类
 * <AUTHOR>
 * @date 2024-12-13
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class CompanyServiceImpl implements ICompanyService {
	
	private final DataServerClient dataClient;
	
	@Override
	public List<CompanyVo> listCompanys() {
		List<CompanyVo> result = new ArrayList<>();
		List<EnterpriseResp> enterpriseList = dataClient.listEnterprise();
		if (!CollectionUtils.isEmpty(enterpriseList)) {
			result = enterpriseList.stream().map(item -> {
				CompanyVo vo = new CompanyVo();
				vo.setCompanyId(item.getId());
				vo.setCompanyName(item.getEnterpriseName());
				return vo;
			}).collect(Collectors.toList());
		}
		return result;
	}


}
