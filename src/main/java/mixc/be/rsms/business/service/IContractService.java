package mixc.be.rsms.business.service;

import java.util.List;

import mixc.be.rsms.business.domain.dto.ContractSignAddParams;
import mixc.be.rsms.business.vo.ContractDetailVo;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.third.JoyContractPreview;

/**
 * @ClassName IContractService
 * @Description 房源业务接口
 * <AUTHOR>
 * @date 2024-09-13
 */
public interface IContractService {

	/**
	 * 保存合同基本信息
	 * @param addParams 合同基本信息
	 * @return
	 */
	ResultVo<JoyContractPreview> addContractSign(ContractSignAddParams addParams);
	
	/**
	 * 合同详情查询
	 * @param contractId 合同ID
	 * @oaran sourceType 来源类型
	 * @return
	 */
	ContractDetailVo contractDetail(Long contractId, String sourceType);
	
	/**
	 * 更新合同
	 * @param updateParams 更新参数
	 */
	ResultVo<JoyContractPreview> updateContract(ContractSignAddParams updateParams);
	
	/**
	 * 合同删除
	 * @param contractId 合同ID
	 */
	void deleteContract(Long contractId);
	
	/**
	 * 合同状态更新回调
	 * @param joyContractSignature 签名串，结合 Joy-Contract-Timestamp ，Joy-Contract-Nonce，请求体参数
	 * @param joyContractTimestamp 时间戳，此时间戳还可以用于判断是否拒绝过期应答
	 * @param joyContractNonce 随机数，可用于判重 
	 * @param joyContractCallbackParamJson 状态更新回调参数
	 */
	ResultVo<String> contractStatusCallBack(
			String joyContractSignature, 
			String joyContractTimestamp, 
			String joyContractNonce, 
			String joyContractCallbackParamJson);
	
	/**
	 * 根据合同编号集合获取合同号
	 * @param contractNumbers 合同编号集合
	 * @return
	 */
	List<String> listContractByNumbers(List<String> contractNumbers);
	
}
