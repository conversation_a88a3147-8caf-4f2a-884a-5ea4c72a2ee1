package mixc.be.rsms.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import mixc.be.rsms.business.domain.dto.DepositAddParams;
import mixc.be.rsms.business.domain.dto.DepositAuditParams;
import mixc.be.rsms.business.domain.dto.DepositQueryParams;
import mixc.be.rsms.business.domain.dto.DepositRefundParam;
import mixc.be.rsms.business.vo.DepositVo;
import mixc.be.rsms.pojo.business.DepositResp;

import java.util.List;

/**
 * <p>
 * 成交意向金/订金表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
public interface IDepositService {

    IPage<DepositVo> page(DepositQueryParams params);

    Long saveOrUpdate(DepositAddParams depositAddParams);

    Integer deleteDeposit(Long id);

    Long submitDeposit(DepositAuditParams params);

    Integer auditOrReject(DepositAuditParams params);

    /**
     * 子订单状态转 交易信息行订单状态
     *
     * @param orderStatus
     * @return
     */
    String subOrderStatus2TransactionInfoOrderStatus(String orderStatus);

    Integer reverseAudit(DepositAuditParams params);

    DepositVo getDeposit(Long id);

    void refund(DepositRefundParam param);

    /**
     * 查询未绑定成交报告的 意向金/订金 记录
     * @param depositCode   意向金编码
     * @param bindingTransactionReport  是否绑定成交合同
     * @return
     */
    List<DepositVo> queryUnBindingTransactionsReportList(String depositCode, Long customerId, Boolean bindingTransactionReport);

    /**
     * 内部保存意向金、订金 供 保存\更新、提交调用
     *
     * @param depositAddParams
     * @return
     */
    Long insideSaveOrUpdateDeposit(DepositAddParams depositAddParams);

    /**
     * 获取 收款、退款、佣金 统计数据
     * @param id
     * @return
     */
    DepositVo getAmountStatistics(Long id);
    /**
     * 根据客户ID 查询意向金
     *
     * @param customerIds
     * @return
     */
    List<DepositResp> queryDepositByCustomerId(List<Long> customerIds);

    /**
     * 根据Id 查询意向金
     * @param ids
     * @return
     */
    List<DepositResp> queryDepositById(List<Long> ids);

    /**
     * 根据资产ID(房源Id、车位ID) 查询意向金
     * @param assetsIds
     * @return
     */
    List<DepositResp> queryDepositByAssetsId(List<Long> assetsIds);


    /**
     * 根据资产Id(房源Id、车位ID)、客户手机号 查询意向金
     * @param assetsId
     * @return
     */
    List<DepositResp> queryDepositByAssetsIdAndCustomerMobile(Long assetsId, String customerMobile);
}
