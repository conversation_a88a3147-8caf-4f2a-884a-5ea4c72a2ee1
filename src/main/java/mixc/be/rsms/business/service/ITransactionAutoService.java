package mixc.be.rsms.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionAuto;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 成交报告-业绩分配信息-自动分配 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface ITransactionAutoService extends IService<TbBusinessTransactionAuto> {

    /**
     * @description
     * <AUTHOR>
     * @param[1] transactionIds
     * @throws
     * @return List<TbBusinessTransactionAuto>
     * @time 2025/6/4 16:07
     */
    Map<Long, List<TbBusinessTransactionAuto>> findAutoByTransactionId(List<Long> transactionIds);

}
