package mixc.be.rsms.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import mixc.be.rsms.business.domain.dto.*;
import mixc.be.rsms.business.domain.pojo.TbBusinessOrder;
import mixc.be.rsms.business.vo.OrderVo;
import mixc.be.rsms.business.vo.ReceiveCommunityVo;
import mixc.be.rsms.pojo.business.CancelPayReq;
import mixc.be.rsms.pojo.business.OrderResp;
import mixc.be.rsms.pojo.business.PayOrderReq;
import mixc.be.rsms.pojo.business.ThirdPayResultResp;
import mixc.be.rsms.pojo.third.SFCallBackResp;
import mixc.be.rsms.pojo.third.SFOrderCallBackParam;
import mixc.be.rsms.pojo.third.SFOrderRefundCallBackParam;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/09
 */
public interface IOrderService {

    IPage<OrderVo> page(OrderQueryParams orderQueryParams);

    OrderVo detail(Long id);

    List<OrderResp> queryOrderByContractNum(List<String> contractNums, String customerMobile);

    Page<OrderResp> queryOrderByCustomerMobile(Integer pageNum, Integer pageSize, String customerMobile);

    TbBusinessOrder save(OrderAddParams orderAddParams);

    TbBusinessOrder addSubOrder(Long orderId, List<SubOrderReceiveAddParams> subOrderReceiveAddParams, List<SubOrderRefundAddParams> subOrderRefundAddParams, List<SubOrderCommissionAddParams> subOrderCommissionAddParams);

    void bigAmountTransferPush(Long orderId);

    void posPay(PayOrderReq params);

    ThirdPayResultResp thirdPay(PayOrderReq params);

    void cancelPay(CancelPayReq cancelPayReq);

    OrderResp queryTradeOrderInfo(Long orderId, Long subOrderId);

    void prepareConvertCommission(Long orderId);

    Page<OrderResp> mobileQueryOrderByUserId(Integer pageNum,
                                             Integer pageSize,
                                             String orderStatus,
                                             String transactionType,
                                             String customerNameKeyWord,
                                             String houseKeyWord,
                                             String parkingKeyWord,
                                             String mobileKeyWord);

    /**
     * 收费系统 订单回调
     *
     * @param callBackParam
     */
    SFCallBackResp payCallBackOnSF(SFOrderCallBackParam callBackParam);

    /**
     * 收费系统 退款回调
     * @param refundCallBackParam
     * @return
     */
    SFCallBackResp refundCallBackOnSF(SFOrderRefundCallBackParam refundCallBackParam);

    /**
     * 处理支付回调
     * @param orderCode 主订单编号
     * @param subOrderCode  子订单编号
     * @param status    状态：success、fail、close
     * @param payChannel 支付渠道
     * @param payFinishTime 支付时间
     * @param payAmount 支付金额
     */
    void handlePayCallBack(String orderCode, String subOrderCode, String status, String payChannel, LocalDateTime payFinishTime, BigDecimal payAmount);

    void handleThirdPayCallBack(Boolean fromInnerTask,
                                String transactionId,
                                String outOrderNo,
                                String subOutOrderNo,
                                String status,
                                String payChannel,
                                LocalDateTime payFinishTime,
                                BigDecimal payAmount);
    /**
     * 处理退款回调
     * @param refundCode 退款单号
     * @param status 退款状态
     * @param refundFinishTime 退款时间
     * @param refundAmount 退款金额
     */
    void handleRefundCallBack(String refundCode, String status, LocalDateTime refundFinishTime, BigDecimal refundAmount);

    /**
     * 调用收费系统获取收费项
     *
     * @param zxCommunityCode
     * @param readCache
     * @param transactionContractNum
     * @param type 查询类型 4-意向金 5-佣金
     * @return
     */
    List<ReceiveCommunityVo> getReceiveCommunity(String zxCommunityCode, Boolean readCache, String transactionContractNum, Integer type);
}
