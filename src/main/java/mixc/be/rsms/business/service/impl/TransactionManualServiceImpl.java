package mixc.be.rsms.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionManual;
import mixc.be.rsms.business.mapper.TbBusinessTransactionManualMapper;
import mixc.be.rsms.business.service.ITransactionManualService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 成交报告-业绩分配信息-手动分配 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class TransactionManualServiceImpl extends ServiceImpl<TbBusinessTransactionManualMapper, TbBusinessTransactionManual> implements ITransactionManualService {

    @Override
    public Map<Long, List<TbBusinessTransactionManual>> findManualByTransactionId(List<Long> transactionIds) {
        if(ObjectUtils.isEmpty(transactionIds)){
            return Map.of();
        }
        LambdaQueryWrapper<TbBusinessTransactionManual> queryWrapper = new LambdaQueryWrapper<TbBusinessTransactionManual>()
                .in(TbBusinessTransactionManual::getTransactionId, transactionIds);
        List<TbBusinessTransactionManual> transactionManualList = list(queryWrapper);
        if(!ObjectUtils.isEmpty(transactionManualList)){
            return transactionManualList.stream().collect(Collectors.groupingBy(TbBusinessTransactionManual::getTransactionId));
        }
        return Map.of();
    }
}
