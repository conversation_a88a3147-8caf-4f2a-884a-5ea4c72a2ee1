package mixc.be.rsms.business.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.enums.BusinessEnum;
import mixc.be.rsms.business.common.enums.ResultEnum;
import mixc.be.rsms.business.common.exception.BizException;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.exception.UnAuthException;
import mixc.be.rsms.business.common.feignclient.AuthorServerClient;
import mixc.be.rsms.business.common.feignclient.DataServerClient;
import mixc.be.rsms.business.common.feignclient.ThirdServiceClient;
import mixc.be.rsms.business.convert.BizContractConvert;
import mixc.be.rsms.business.domain.dto.ContractArchiveParams;
import mixc.be.rsms.business.domain.dto.ContractFileInfoParam;
import mixc.be.rsms.business.domain.dto.ContractSignAddParams;
import mixc.be.rsms.business.domain.dto.ContractSignQueryParams;
import mixc.be.rsms.business.domain.dto.FileInfoParam;
import mixc.be.rsms.business.domain.dto.JoyContractCallbackParam;
import mixc.be.rsms.business.domain.dto.JoyContractCallbackParam.ContractSign;
import mixc.be.rsms.business.domain.pojo.TbBusinessContract;
import mixc.be.rsms.business.domain.pojo.TbBusinessDeposit;
import mixc.be.rsms.business.mapper.TbBusinessContractMapper;
import mixc.be.rsms.business.mapper.TbBusinessDepositMapper;
import mixc.be.rsms.business.service.IBizContractService;
import mixc.be.rsms.business.service.ICompanyService;
import mixc.be.rsms.business.service.IContractSignTypeService;
import mixc.be.rsms.business.utils.PermissionUtil;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.CompanyVo;
import mixc.be.rsms.business.vo.ContractPageVo;
import mixc.be.rsms.business.vo.ContractTypeDropdownVo;
import mixc.be.rsms.business.vo.DataFileInfoVo;
import mixc.be.rsms.business.vo.HouseInfoVo;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.common.constant.RsmsConstant;
import mixc.be.rsms.common.enums.CommonEnum;
import mixc.be.rsms.common.enums.DropdownEnum;
import mixc.be.rsms.common.enums.PermissionEnum;
import mixc.be.rsms.common.enums.UserTipTemplateEnum;
import mixc.be.rsms.common.utils.AuthUtil;
import mixc.be.rsms.common.utils.Base64Util;
import mixc.be.rsms.common.utils.DateUtil;
import mixc.be.rsms.common.utils.PageUtil;
import mixc.be.rsms.common.utils.PersonDataEncryptUtil;
import mixc.be.rsms.common.utils.SnowFlake;
import mixc.be.rsms.common.utils.StpEmployeeUtil;
import mixc.be.rsms.common.utils.TemplateUtil;
import mixc.be.rsms.pojo.author.UserDetailsMicroResp;
import mixc.be.rsms.pojo.data.ContractNumberResp;
import mixc.be.rsms.pojo.data.CustomerSourceResp;
import mixc.be.rsms.pojo.data.FileInfoResp;
import mixc.be.rsms.pojo.data.ParkingSpaceResp;
import mixc.be.rsms.pojo.data.UserTipParam;
import mixc.be.rsms.pojo.mobile.ContractResp;
import mixc.be.rsms.pojo.mobile.CustomerAssetDetailResp;
import mixc.be.rsms.pojo.mobile.CustomerAssetResp;
import mixc.be.rsms.pojo.third.JoyContractBaseFields;
import mixc.be.rsms.pojo.third.JoyContractFieldInfoResp;
import mixc.be.rsms.pojo.third.JoyContractSigned;
import mixc.be.rsms.pojo.third.JoyContractSignedRecord;
import mixc.be.rsms.pojo.third.JoyContractSignedResp;
import mixc.be.rsms.pojo.third.JoyRecallParam;
import mixc.be.rsms.pojo.third.JoyResultResp;

/**
 * @ClassName BizContractServiceImpl
 * @Desription 合同业务实现类
 * <AUTHOR>
 * @date 2024-12-13
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class BizContractServiceImpl implements IBizContractService {
	
	private final TbBusinessContractMapper bizContractMapper;
	private final BizContractConvert bizContractConvert;
	private final ICompanyService companyService;
	private final TbBusinessDepositMapper depositMapper;
	private final SnowFlake snowFlake;
	private final AuthorServerClient authorServerClient;
	private final DataServerClient dataServerClient;
	private final ThirdServiceClient thirdServerClient;
	private final StringRedisTemplate stringRedisTemplate;
	private final IContractSignTypeService contractSignTypeService;
	
	@Value("${joy.sign_page_url}")
	private String joySignPageUrl;

	@Value("${joy.contract_detail_url}")
	private String joyContractDetailUrl;
	
	@Value("${joy.contract_lease_date_value}")
	private String joyContractLeaseDateValue;

	@Override
	public Long addContractSignBaseInfo(ContractSignAddParams addParams) {
		if (ObjectUtils.isEmpty(addParams)) {
			throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
		}
		//校验甲方代理人信息
		if (!ObjectUtils.isEmpty(addParams.getPartAParams())) {
			String leaserProxyUserName = addParams.getPartAParams().getLeaserProxyUserName();
			String leaserProxyUserMobile = addParams.getPartAParams().getLeaserProxyUserMobile();
			String leaserProxyUserIdNumber = addParams.getPartAParams().getLeaserProxyUserIdNumber();
			if (!validate(leaserProxyUserName, leaserProxyUserMobile, leaserProxyUserIdNumber)) {
				throw new BusinessException(BusinessEnum.LEASER_PROXY_INFO_NOT_EMPTY);
			}
		}
		//校验乙方代理人信息
		if (!ObjectUtils.isEmpty(addParams.getPartBParams())) {
			String customerProxyUserName = addParams.getPartBParams().getCustomerProxyUserName();
			String customerProxyUserMobile = addParams.getPartBParams().getCustomerProxyUserMobile();
			String customerProxyUserIdNumber = addParams.getPartBParams().getCustomerProxyUserIdNumber();
			if (!validate(customerProxyUserName, customerProxyUserMobile, customerProxyUserIdNumber)) {
				throw new BusinessException(BusinessEnum.CUSTOMER_PROXY_INFO_NOT_EMPTY);
			}
		}
		String contractNumber = addParams.getContractBaseInfo().getContractNumber();
		TbBusinessContract queryContract = bizContractMapper
				.selectOne(new LambdaQueryWrapper<TbBusinessContract>().eq(TbBusinessContract::getContractNumber, contractNumber).last("limit 1"));
		//合同重复校验
		if (!ObjectUtils.isEmpty(queryContract)) {
			throw new BusinessException(BusinessEnum.DUPLICATE_CONTRACT_NUMBER);
		}
		//校验合同号是否可用
		ContractNumberResp contractNumberResp = dataServerClient.getByContractNumber(
				contractNumber, addParams.getContractBaseInfo().getIntermediaryCompanyId());
		if (!ObjectUtils.isEmpty(contractNumberResp)) {
			String type = contractNumberResp.getType();
			log.info("合同号：{} 状态为：{} ", contractNumber, type);
			if (!DropdownEnum.CONTRACT_NUMBER_TYPE_NEW.getDictKey().equals(type)) {
				throw new BusinessException(BusinessEnum.CONTRACT_NUMBER_STATUS_ERROR);
			}
		}
		TbBusinessContract bizContract = bizContractConvert.convert(addParams);
		Long contractId = snowFlake.nextId();
		bizContract.setId(contractId);
		bizContract.setContractStatus(DropdownEnum.CONTRACT_STATUS_DRAFT.getDictKey());
		bizContract.setCreateUser(AuthUtil.getLoginIdAsLong());
		bizContractMapper.insert(bizContract);
		//更新合同号状态
//		ContractNumberParam updateParam = new ContractNumberParam();
//		updateParam.setCompanyId(addParams.getContractBaseInfo().getIntermediaryCompanyId());
//		updateParam.setContractNumber(contractNumber);
//		if (!ObjectUtils.isEmpty(contractNumberResp)) {
//			Integer flag = dataServerClient.updateContractNumber(updateParam);
//			if (!ObjectUtils.isEmpty(flag) && flag.intValue() != 1) {
//				throw new BusinessException(BusinessEnum.CONTRACT_NUMBER_HAS_USED);
//			}
//		} else {
//			Integer flag = dataServerClient.updateContractNumberRule(updateParam);
//			if (!ObjectUtils.isEmpty(flag) && flag.intValue() != 1) {
//				throw new BusinessException(BusinessEnum.CONTRACT_NUMBER_HAS_USED);
//			}
//		}
		return contractId;
	}

	private boolean validate(String leaserProxyUserName, String leaserProxyUserMobile, String leaserProxyUserIdNumber) {
		if (!ObjectUtils.isEmpty(leaserProxyUserName) 
				|| !ObjectUtils.isEmpty(leaserProxyUserMobile) 
				|| !ObjectUtils.isEmpty(leaserProxyUserIdNumber)) {
			return !ObjectUtils.isEmpty(leaserProxyUserName) && !ObjectUtils.isEmpty(leaserProxyUserMobile) && !ObjectUtils.isEmpty(leaserProxyUserIdNumber);
		}
		return true;
	}

	@Override
	public Page<ContractPageVo> pageContractSign(ContractSignQueryParams queryParams) {
		if (ObjectUtils.isEmpty(queryParams)) {
			throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
		}
		if (ObjectUtils.isEmpty(queryParams.getPageNum()) || ObjectUtils.isEmpty(queryParams.getPageSize())) {
			throw new BusinessException(CommonEnum.PAGE_PARAMS_NOT_NULL);
		}
		Page<ContractPageVo> resultPage = new Page<>();
		//获取权限
		List<Long> permissionUserIdList = authorServerClient
				.getUserPermissionByCode(PermissionEnum.ESIGN_CONTRACT_MANAGE_VIEW.getCode());
		log.info("合同签约列表权限查询到的用户ID信息为：{}", permissionUserIdList);
		LambdaQueryWrapper<TbBusinessContract> queryWrapper = assembleQueryConditions(queryParams);
		queryWrapper.orderByDesc(TbBusinessContract::getCreateTime);
		//房源、房号
		if (!ObjectUtils.isEmpty(queryParams.getContractHouseInfo())) {
			List<Long> houseIdList = dataServerClient.getHouseIdByCommunityKeyWord(queryParams.getContractHouseInfo());
			List<Long> parkIdList = dataServerClient.getParkingSpaceByKeyWord(queryParams.getContractHouseInfo());
			if (CollectionUtils.isEmpty(houseIdList) && CollectionUtils.isEmpty(parkIdList)) {
				return resultPage;
			} 
			queryWrapper.nested(wrapper -> {
				if (!CollectionUtils.isEmpty(houseIdList)) {
					wrapper.or().in(TbBusinessContract::getHouseId, houseIdList);
				}
				if (!CollectionUtils.isEmpty(parkIdList)) {
					wrapper.or().in(TbBusinessContract::getParkingSpaceId, parkIdList);
				}
			});
		}
		if (!ObjectUtils.isEmpty(queryParams.getHouseNumber())) {
			List<Long> houseIdList = dataServerClient.getHouseIdByCommunityKeyWord(queryParams.getHouseNumber());
			if (CollectionUtils.isEmpty(houseIdList)) {
				return resultPage;
			}
			queryWrapper.in(TbBusinessContract::getHouseId, houseIdList);
		}
		//乙方
		if (!ObjectUtils.isEmpty(queryParams.getCustomerName())) {
			List<CustomerSourceResp> customerList = dataServerClient.innerQueryCustomerByName(queryParams.getCustomerName());
			if (CollectionUtils.isEmpty(customerList)) {
				return resultPage;
			}
			List<Long> customerUserIdList = customerList.stream().map(CustomerSourceResp::getId).collect(Collectors.toList());
			queryWrapper.in(TbBusinessContract::getCustomerId, customerUserIdList);
		}
		//创建人
		if (!ObjectUtils.isEmpty(queryParams.getCreator())) {
			List<Long> creatorUserIdList = authorServerClient.getUserIdsByUserName(queryParams.getCreator());
			if (CollectionUtils.isEmpty(creatorUserIdList)) {
				return resultPage;
			}
			queryWrapper.in(TbBusinessContract::getCreateUser, creatorUserIdList);
		}
		//合同类型和状态
		if (!ObjectUtils.isEmpty(queryParams.getContractType())) {
			queryWrapper.eq(TbBusinessContract::getContractType, queryParams.getContractType());
		}
		if (!ObjectUtils.isEmpty(queryParams.getContractStatus())) {
			queryWrapper.eq(TbBusinessContract::getContractStatus, queryParams.getContractStatus());
		}
		//合同编号
		if (!ObjectUtils.isEmpty(queryParams.getContractNumber())) {
			queryWrapper.like(TbBusinessContract::getContractNumber, queryParams.getContractNumber());
		}
		//权限条件
		if (!CollectionUtils.isEmpty(permissionUserIdList)) {
			if (permissionUserIdList.get(0).longValue() != RsmsConstant.PERMISSION_FOR_ALL) {
				queryWrapper.in(TbBusinessContract::getCreateUser, permissionUserIdList);
			}
		}
		Page<TbBusinessContract> page = bizContractMapper.selectPage(new Page<>(queryParams.getPageNum(), queryParams.getPageSize()), queryWrapper);
		List<TbBusinessContract> bizContractList = page.getRecords();
		if (!CollectionUtils.isEmpty(bizContractList)) {
			List<ContractTypeDropdownVo> contractTypeDropdownVoList = contractSignTypeService.listContractTypeDropdown(null, false);
			List<Long> createUserIdList = bizContractList.stream().map(TbBusinessContract::getCreateUser).collect(Collectors.toList());
			List<Long> customerIdList = bizContractList.stream().map(TbBusinessContract::getCustomerId).collect(Collectors.toList());
			List<Long> houseIdList = bizContractList.stream()
					.filter(item -> !(ObjectUtils.isEmpty(item.getHouseId()))).map(TbBusinessContract::getHouseId).collect(Collectors.toList());
			List<Long> parkSpaceIdList = bizContractList.stream()
					.filter(item -> !(ObjectUtils.isEmpty(item.getParkingSpaceId()))).map(TbBusinessContract::getParkingSpaceId).collect(Collectors.toList());
			//获取创建人
			List<UserDetailsMicroResp> userDetailsMicroRespList = authorServerClient.getUserDetailsMicro(createUserIdList);
			List<CustomerSourceResp> customerSourceRespList = dataServerClient.innerQueryCustomerByIdList(customerIdList);
			List<HouseInfoVo> houseInfoVoList = dataServerClient.listHouseInfoByIds(houseIdList);
			List<ParkingSpaceResp> parkSpaceRespList = dataServerClient.getParkingSpaceByIdList(parkSpaceIdList);
			Map<String, ContractTypeDropdownVo> contractTypeMap = new HashMap<>();
			Map<Long, UserDetailsMicroResp> userDetailMap = new HashMap<>();
			Map<Long, CustomerSourceResp> customerResourceMap = new HashMap<>();
			Map<Long, HouseInfoVo> houseInfoMap = new HashMap<>();
			Map<Long, ParkingSpaceResp> parkSpaceMap = new HashMap<>();
			if (!CollectionUtils.isEmpty(contractTypeDropdownVoList)) {
				contractTypeMap = contractTypeDropdownVoList.stream().collect(Collectors.toMap(ContractTypeDropdownVo::getContractType, vo -> vo));
			}
			if (!CollectionUtils.isEmpty(userDetailsMicroRespList)) {
				userDetailMap = userDetailsMicroRespList.stream().collect(Collectors.toMap(UserDetailsMicroResp::getId, user -> user));
			}
			if (!CollectionUtils.isEmpty(customerSourceRespList)) {
				customerResourceMap = customerSourceRespList.stream().collect(Collectors.toMap(CustomerSourceResp::getId, customer -> customer));
			}
			if (!CollectionUtils.isEmpty(houseInfoVoList)) {
				houseInfoMap = houseInfoVoList.stream().collect(Collectors.toMap(HouseInfoVo::getId, house -> house));
			}
			if (!CollectionUtils.isEmpty(parkSpaceRespList)) {
				parkSpaceMap = parkSpaceRespList.stream().collect(Collectors.toMap(ParkingSpaceResp::getId, parkSpace -> parkSpace));
			}
			resultPage = PageUtil.copy(page, ContractPageVo.class);
			List<ContractPageVo> contractPageVoList = resultPage.getRecords();
			List<CompanyVo> companyVoList = companyService.listCompanys();
			Map<Long, CompanyVo> companyMap = companyVoList.stream().collect(Collectors.toMap(CompanyVo::getCompanyId, vo -> vo));
			//获取权限
			List<Long> generateUserIdList = authorServerClient.getUserPermissionByCode(PermissionEnum.ESIGN_CONTRACT_MANAGE_GENERATE_REPORT.getCode());
			List<Long> archiveUserIdList = authorServerClient.getUserPermissionByCode(PermissionEnum.ESIGN_CONTRACT_MANAGE_ARCHIVE.getCode());
			
			for (ContractPageVo contractPageVo : contractPageVoList) {
				//获取按钮权限
				List<String> permissions = new ArrayList<>();
				if (PermissionUtil.isPermit(generateUserIdList, contractPageVo.getCreateUser())) {
					permissions.add(PermissionEnum.ESIGN_CONTRACT_MANAGE_GENERATE_REPORT.getCode());
				}
				if (PermissionUtil.isPermit(archiveUserIdList, contractPageVo.getCreateUser())) {
					permissions.add(PermissionEnum.ESIGN_CONTRACT_MANAGE_ARCHIVE.getCode());
				}
				permissions.add(PermissionEnum.ESIGN_CONTRACT_MANAGE_VIEW.getCode());
				contractPageVo.setPermissions(permissions);
				contractPageVo.setLeaserMobileEncode(Base64Util.encodeData(contractPageVo.getLeaserMobile()));
				contractPageVo.setLeaserMobile(PersonDataEncryptUtil.mobileEncrypt(contractPageVo.getLeaserMobile()));
				contractPageVo.setCustomerMobileEncode(Base64Util.encodeData(contractPageVo.getCustomerMobile()));
				contractPageVo.setCustomerMobile(PersonDataEncryptUtil.mobileEncrypt(contractPageVo.getCustomerMobile()));
				BigDecimal totalCommission = new BigDecimal("0");
				if (!ObjectUtils.isEmpty(contractPageVo.getLeaserCommission())) {
					totalCommission = totalCommission.add(contractPageVo.getLeaserCommission());
				}
				if (!ObjectUtils.isEmpty(contractPageVo.getCustomerCommission())) {
					totalCommission = totalCommission.add(contractPageVo.getCustomerCommission());
				}
				contractPageVo.setCommission(totalCommission);
				//居间方公司
				if (!ObjectUtils.isEmpty(contractPageVo.getIntermediaryCompanyId()) && MapUtils.isNotEmpty(companyMap)) {
					CompanyVo vo = companyMap.get(contractPageVo.getIntermediaryCompanyId());
					contractPageVo.setIntermediaryCompanyName(vo == null ? null : vo.getCompanyName());
				}
				if (!ObjectUtils.isEmpty(contractPageVo.getContractType())
						&& !ObjectUtils.isEmpty(contractTypeMap.get(contractPageVo.getContractType()))) {
					contractPageVo.setContractTypeName(contractTypeMap.get(contractPageVo.getContractType()).getContractTypeName());
				}
				
				if (!ObjectUtils.isEmpty(userDetailMap.get(contractPageVo.getCreateUser()))) {
					contractPageVo.setCreator(userDetailMap.get(contractPageVo.getCreateUser()).getEmpName());//创建人
				}
				if (!ObjectUtils.isEmpty(customerResourceMap.get(contractPageVo.getCustomerId()))) {
					contractPageVo.setCustomerName(customerResourceMap.get(contractPageVo.getCustomerId()).getName());//乙方姓名
				}
				if (!ObjectUtils.isEmpty(contractPageVo.getHouseId())) {//房源
					HouseInfoVo house = houseInfoMap.get(contractPageVo.getHouseId());
					if (!ObjectUtils.isEmpty(house)) {
//						contractPageVo.setLeaserUserName(house == null ? null : house.getConcatUser()); //甲方姓名
						if (!ObjectUtils.isEmpty(house)) { //房源信息
							contractPageVo.setContractHouseInfo(house.getLocation() + RsmsConstant.SPLIT_VERTICAL_LINE + 
									house.getHouseNumber() + RsmsConstant.SPLIT_VERTICAL_LINE + house.getCommunityName());
						}
					}
				}
				if (!ObjectUtils.isEmpty(contractPageVo.getParkingSpaceId())) {//车位
					ParkingSpaceResp park = parkSpaceMap.get(contractPageVo.getParkingSpaceId());
					if (!ObjectUtils.isEmpty(park)) {
						if (!ObjectUtils.isEmpty(park)) { //车位信息
							contractPageVo.setContractHouseInfo(DropdownEnum.getDictValue("parking_space_type", park.getType()) + RsmsConstant.SPLIT_VERTICAL_LINE + 
									park.getNumber() + RsmsConstant.SPLIT_VERTICAL_LINE + park.getCommunityName());
						}
					}
				}
			}
		}
		return resultPage;
	}
	
	@Override
	public Page<ContractPageVo> appPageContractSign(ContractSignQueryParams queryParams) {
		//获取权限
		List<Long> permissionUserIdList = authorServerClient
				.getUserPermissionByCode(PermissionEnum.ESIGN_CONTRACT_MANAGE_VIEW.getCode());
		log.info("合同签约列表权限查询到的用户ID信息为：{}", permissionUserIdList);
		LambdaQueryWrapper<TbBusinessContract> queryWrapper = new LambdaQueryWrapper<TbBusinessContract>();
		queryWrapper.orderByDesc(TbBusinessContract::getCreateTime);
		String queryStr = queryParams.getQueryStr();
		if (!ObjectUtils.isEmpty(queryStr)) {
			long start = System.currentTimeMillis();
			List<Long> houseIdList = dataServerClient.getHouseIdByCommunityKeyWord(queryStr);
			log.info("房源查询耗时：{}", (System.currentTimeMillis() - start));
			List<CustomerSourceResp> customerQueryList = dataServerClient.innerQueryCustomerByName(queryStr);
			List<Long> customerIdList = customerQueryList.stream().map(CustomerSourceResp::getId).collect(Collectors.toList());
			//合同编号、业主
			queryWrapper.or(item -> item.like(TbBusinessContract::getContractNumber, queryStr))
					.or(item -> item.like(TbBusinessContract::getLeaserUserName, queryStr));
			//客户
			if (!CollectionUtils.isEmpty(customerIdList)) {
				queryWrapper.or(item -> item.in(TbBusinessContract::getCustomerId, customerIdList));
			}
			if (!CollectionUtils.isEmpty(houseIdList)) {
				queryWrapper.or(item -> item.in(TbBusinessContract::getHouseId, houseIdList));
			}
		}
		//权限条件
		if (!CollectionUtils.isEmpty(permissionUserIdList)) {
			if (permissionUserIdList.get(0).longValue() != RsmsConstant.PERMISSION_FOR_ALL) {
				queryWrapper.in(TbBusinessContract::getCreateUser, permissionUserIdList);
			}
		}
		//合同类型和状态
		if (!ObjectUtils.isEmpty(queryParams.getContractType())) {
			queryWrapper.eq(TbBusinessContract::getContractType, queryParams.getContractType());
		}
		if (!ObjectUtils.isEmpty(queryParams.getContractStatus())) {
			queryWrapper.eq(TbBusinessContract::getContractStatus, queryParams.getContractStatus());
		}
		Page<ContractPageVo> resultPage = new Page<>();
		Page<TbBusinessContract> page = bizContractMapper.selectPage(new Page<>(queryParams.getPageNum(), queryParams.getPageSize()), queryWrapper);
		List<TbBusinessContract> bizContractList = page.getRecords();
		if (!CollectionUtils.isEmpty(bizContractList)) {
			List<Long> createUserIdList = bizContractList.stream().map(TbBusinessContract::getCreateUser).collect(Collectors.toList());
			List<Long> customerIdList = bizContractList.stream().map(TbBusinessContract::getCustomerId).collect(Collectors.toList());
			List<Long> houseIdList = bizContractList.stream().map(TbBusinessContract::getHouseId).collect(Collectors.toList());
			List<Long> parkSpaceIdList = bizContractList.stream()
					.filter(item -> !(ObjectUtils.isEmpty(item.getParkingSpaceId()))).map(TbBusinessContract::getParkingSpaceId).collect(Collectors.toList());
			//获取创建人
			List<UserDetailsMicroResp> userDetailsMicroRespList = authorServerClient.getUserDetailsMicro(createUserIdList);
			List<CustomerSourceResp> customerSourceRespList = dataServerClient.innerQueryCustomerByIdList(customerIdList);
			List<HouseInfoVo> houseInfoVoList = dataServerClient.listHouseInfoByIds(houseIdList);
			List<ParkingSpaceResp> parkSpaceRespList = dataServerClient.getParkingSpaceByIdList(parkSpaceIdList);
			Map<Long, UserDetailsMicroResp> userDetailMap = new HashMap<>();
			Map<Long, CustomerSourceResp> customerResourceMap = new HashMap<>();
			Map<Long, HouseInfoVo> houseInfoMap = new HashMap<>();
			Map<Long, ParkingSpaceResp> parkSpaceMap = new HashMap<>();
			if (!CollectionUtils.isEmpty(userDetailsMicroRespList)) {
				userDetailMap = userDetailsMicroRespList.stream().collect(Collectors.toMap(UserDetailsMicroResp::getId, user -> user));
			}
			if (!CollectionUtils.isEmpty(customerSourceRespList)) {
				customerResourceMap = customerSourceRespList.stream().collect(Collectors.toMap(CustomerSourceResp::getId, customer -> customer));
			}
			if (!CollectionUtils.isEmpty(houseInfoVoList)) {
				houseInfoMap = houseInfoVoList.stream().collect(Collectors.toMap(HouseInfoVo::getId, house -> house));
			}
			if (!CollectionUtils.isEmpty(parkSpaceRespList)) {
				parkSpaceMap = parkSpaceRespList.stream().collect(Collectors.toMap(ParkingSpaceResp::getId, parkSpace -> parkSpace));
			}
			resultPage = PageUtil.copy(page, ContractPageVo.class);
			List<ContractPageVo> contractPageVoList = resultPage.getRecords();
			//获取按钮权限
			List<Long> editUserIdList = authorServerClient.getUserPermissionByCode(PermissionEnum.ESIGN_CONTRACT_MANAGE_EDIT.getCode());
			List<Long> submitUserIdList = authorServerClient.getUserPermissionByCode(PermissionEnum.ESIGN_CONTRACT_MANAGE_SUBMIT.getCode());
			List<Long> approvalPassUserIdList = authorServerClient.getUserPermissionByCode(PermissionEnum.ESIGN_CONTRACT_MANAGE_APPROVAL_PASS.getCode());
			List<Long> rejectUserIdList = authorServerClient.getUserPermissionByCode(PermissionEnum.ESIGN_CONTRACT_MANAGE_REJECT.getCode());
			List<Long> genReportUserIdList = authorServerClient.getUserPermissionByCode(PermissionEnum.ESIGN_CONTRACT_MANAGE_GENERATE_REPORT.getCode());
			List<Long> deleteUserIdList = authorServerClient.getUserPermissionByCode(PermissionEnum.ESIGN_CONTRACT_MANAGE_DELETE.getCode());
			List<Long> resignUserIdList = authorServerClient.getUserPermissionByCode(PermissionEnum.ESIGN_CONTRACT_MANAGE_RESIGN.getCode());
			List<Long> archiveUserIdList = authorServerClient.getUserPermissionByCode(PermissionEnum.ESIGN_CONTRACT_MANAGE_ARCHIVE.getCode());
			
			for (ContractPageVo contractPageVo : contractPageVoList) {
				List<String> permissions = new ArrayList<>();
				//获取按钮权限
				if (PermissionUtil.isPermit(editUserIdList, contractPageVo.getCreateUser())) {
					permissions.add(PermissionEnum.ESIGN_CONTRACT_MANAGE_EDIT.getCode());
				}
				if (PermissionUtil.isPermit(submitUserIdList, contractPageVo.getCreateUser())) {
					permissions.add(PermissionEnum.ESIGN_CONTRACT_MANAGE_SUBMIT.getCode());
				}
				if (PermissionUtil.isPermit(approvalPassUserIdList, contractPageVo.getCreateUser())) {
					permissions.add(PermissionEnum.ESIGN_CONTRACT_MANAGE_APPROVAL_PASS.getCode());
				}
				if (PermissionUtil.isPermit(rejectUserIdList, contractPageVo.getCreateUser())) {
					permissions.add(PermissionEnum.ESIGN_CONTRACT_MANAGE_REJECT.getCode());
				}
				if (PermissionUtil.isPermit(genReportUserIdList, contractPageVo.getCreateUser())) {
					permissions.add(PermissionEnum.ESIGN_CONTRACT_MANAGE_GENERATE_REPORT.getCode());
				}
				if (PermissionUtil.isPermit(deleteUserIdList, contractPageVo.getCreateUser())) {
					permissions.add(PermissionEnum.ESIGN_CONTRACT_MANAGE_DELETE.getCode());
				}
				if (PermissionUtil.isPermit(resignUserIdList, contractPageVo.getCreateUser())) {
					permissions.add(PermissionEnum.ESIGN_CONTRACT_MANAGE_RESIGN.getCode());
				}
				if (PermissionUtil.isPermit(archiveUserIdList, contractPageVo.getCreateUser())) {
					permissions.add(PermissionEnum.ESIGN_CONTRACT_MANAGE_ARCHIVE.getCode());
				}
				contractPageVo.setPermissions(permissions);
				contractPageVo.setLeaserMobileEncode(Base64Util.encodeData(contractPageVo.getLeaserMobile()));
				contractPageVo.setLeaserMobile(PersonDataEncryptUtil.mobileEncrypt(contractPageVo.getLeaserMobile()));
				contractPageVo.setCustomerMobileEncode(Base64Util.encodeData(contractPageVo.getCustomerMobile()));
				contractPageVo.setCustomerMobile(PersonDataEncryptUtil.mobileEncrypt(contractPageVo.getCustomerMobile()));
				if (!ObjectUtils.isEmpty(contractPageVo.getLeaserCommission()) 
						&& !ObjectUtils.isEmpty(contractPageVo.getCustomerCommission())) {
					contractPageVo.setCommission(contractPageVo.getLeaserCommission().add(contractPageVo.getCustomerCommission()));
				} else if (ObjectUtils.isEmpty(contractPageVo.getLeaserCommission()) 
						&& !ObjectUtils.isEmpty(contractPageVo.getCustomerCommission())) {
					contractPageVo.setCommission(contractPageVo.getCustomerCommission());
				} else if (!ObjectUtils.isEmpty(contractPageVo.getLeaserCommission()) 
						&& ObjectUtils.isEmpty(contractPageVo.getCustomerCommission())) {
					contractPageVo.setCommission(contractPageVo.getLeaserCommission());
				} 
				if (!ObjectUtils.isEmpty(companyService.listCompanys())) {
					companyService.listCompanys()
							.stream()
							.findAny()
							.ifPresent(consumer -> {
								contractPageVo.setIntermediaryCompanyName(consumer.getCompanyName());
							});
				}
				if (!ObjectUtils.isEmpty(userDetailMap.get(contractPageVo.getCreateUser()))) {
					contractPageVo.setCreator(userDetailMap.get(contractPageVo.getCreateUser()).getEmpName());//创建人
				}
				if (!ObjectUtils.isEmpty(customerResourceMap.get(contractPageVo.getCustomerId()))) {
					contractPageVo.setCustomerName(customerResourceMap.get(contractPageVo.getCustomerId()).getName());//乙方姓名
				}
				HouseInfoVo house = houseInfoMap.get(contractPageVo.getHouseId());
				if (ObjectUtils.isEmpty(contractPageVo.getLeaserUserName())) {
					contractPageVo.setLeaserUserName(house == null ? null : house.getConcatUser()); //甲方姓名
				}
				if (!ObjectUtils.isEmpty(house)) {
					if (!ObjectUtils.isEmpty(house)) { //房源信息
						contractPageVo.setContractHouseInfo(house.getLocation() + RsmsConstant.SPLIT_VERTICAL_LINE + 
								house.getHouseNumber() + RsmsConstant.SPLIT_VERTICAL_LINE + house.getCommunityName());
					}
				}
				if (!ObjectUtils.isEmpty(contractPageVo.getParkingSpaceId())) {//车位
					ParkingSpaceResp park = parkSpaceMap.get(contractPageVo.getParkingSpaceId());
					if (!ObjectUtils.isEmpty(park)) {
						if (!ObjectUtils.isEmpty(park)) { //车位信息
							contractPageVo.setContractParkSpaceInfo(DropdownEnum.getDictValue("parking_space_type", park.getType()) + RsmsConstant.SPLIT_VERTICAL_LINE + 
									park.getNumber() + RsmsConstant.SPLIT_VERTICAL_LINE + park.getCommunityName());
						}
					}
				}
			}
		}
		return resultPage;
	}

	/**
	 * 组装查询条件
	 * @param queryParams 查询参数
	 * @return
	 */
	private LambdaQueryWrapper<TbBusinessContract> assembleQueryConditions(ContractSignQueryParams queryParams) {
		LambdaQueryWrapper<TbBusinessContract> queryWrapper = new LambdaQueryWrapper<TbBusinessContract>();
		//合同编号
		if (!ObjectUtils.isEmpty(queryParams.getContractNumber())) {
			queryWrapper.like(TbBusinessContract::getContractNumber, queryParams.getContractNumber());
		}
		//合同状态
		if (!ObjectUtils.isEmpty(queryParams.getContractStatus())) {
			queryWrapper.eq(TbBusinessContract::getContractStatus, queryParams.getContractStatus());
		}
		//合同类型
		if (!ObjectUtils.isEmpty(queryParams.getContractType())) {
			queryWrapper.eq(TbBusinessContract::getContractType, queryParams.getContractType());
		}
		//业主姓名
		if (!ObjectUtils.isEmpty(queryParams.getLeaserUserName())) {
			queryWrapper.like(TbBusinessContract::getLeaserUserName, queryParams.getLeaserUserName());
		}
		//居间方公司ID
		if (!ObjectUtils.isEmpty(queryParams.getIntermediaryCompanyId())) {
			queryWrapper.eq(TbBusinessContract::getIntermediaryCompanyId, queryParams.getIntermediaryCompanyId());
		}
		//合同签署日期
		if (!ObjectUtils.isEmpty(queryParams.getContractSignDateStart())) {
			queryWrapper.ge(TbBusinessContract::getContractSignDate, queryParams.getContractSignDateStart());
		}
		if (!ObjectUtils.isEmpty(queryParams.getContractSignDateEnd())) {
			queryWrapper.le(TbBusinessContract::getContractSignDate, queryParams.getContractSignDateEnd());
		}
		return queryWrapper;
	}

	@Override
	public TbBusinessContract bizContractDetail(Long contractId) {
		return bizContractMapper.selectById(contractId);
	}

	@Override
	
	public void updateContract(ContractSignAddParams updateParams) {
		if (ObjectUtils.isEmpty(updateParams)) {
			throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
		}
		if (ObjectUtils.isEmpty(updateParams.getId())) {
			throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
		}
		TbBusinessContract bizContract = bizContractConvert.convert(updateParams);
		String contractNumber = updateParams.getContractBaseInfo().getContractNumber();
		TbBusinessContract queryContract = bizContractMapper
				.selectOne(new LambdaQueryWrapper<TbBusinessContract>()
						.eq(TbBusinessContract::getContractNumber, contractNumber)
						.ne(TbBusinessContract::getId, updateParams.getId()).last("limit 1"));
		if (!ObjectUtils.isEmpty(queryContract)) {
			throw new BusinessException(BusinessEnum.DUPLICATE_CONTRACT_NUMBER.getCode(), BusinessEnum.DUPLICATE_CONTRACT_NUMBER.getMessage());
		}
		bizContract.setUpdateUser(AuthUtil.getLoginIdAsLong());
		//校验合同号是否可用
//		ContractNumberResp contractNumberResp = dataServerClient.getByContractNumber(
//				contractNumber, updateParams.getContractBaseInfo().getIntermediaryCompanyId());
//		if (!ObjectUtils.isEmpty(contractNumberResp)) {
//			String type = contractNumberResp.getType();
//			log.info("合同号：{} 状态为：{} ", contractNumber, type);
//			if (!DropdownEnum.CONTRACT_NUMBER_TYPE_NEW.getDictKey().equals(type)) {
//				throw new BusinessException(BusinessEnum.CONTRACT_NUMBER_STATUS_ERROR);
//			}
//		}
		bizContractMapper.updateById(bizContract);
//		//更新合同号状态
//		ContractNumberParam updateParam = new ContractNumberParam();
//		updateParam.setCompanyId(updateParams.getContractBaseInfo().getIntermediaryCompanyId());
//		updateParam.setContractNumber(contractNumber);
//		if (!ObjectUtils.isEmpty(contractNumberResp)) {
//			Integer flag = dataServerClient.updateContractNumber(updateParam);
//			if (!ObjectUtils.isEmpty(flag) && flag.intValue() != 1) {
//				throw new BusinessException(BusinessEnum.CONTRACT_NUMBER_HAS_USED);
//			}
//		} else {
//			Integer flag = dataServerClient.updateContractNumberRule(updateParam);
//			if (!ObjectUtils.isEmpty(flag) && flag.intValue() != 1) {
//				throw new BusinessException(BusinessEnum.CONTRACT_NUMBER_HAS_USED);
//			}
//		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResultVo<String> contractApproval(Long contractId, String approvalType) {
		if (ObjectUtils.isEmpty(contractId)) {
			throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
		}
		TbBusinessContract contract = bizContractMapper.selectById(contractId);
		if (ObjectUtils.isEmpty(contract)) {
			throw new BusinessException(BusinessEnum.CONTRACT_ID_NOT_EXIST);
		}
		contract.setUpdateUser(AuthUtil.getLoginIdAsLong());
		if (approvalType.equals(RsmsConstant.APPROVAL_PASS)) {
			contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_REVIEWED.getDictKey());
			//调用朝昔接口生成契约锁合同文件
			String joyContractId = contract.getJoyContractId();
			log.info("审批通过，朝昔合同id:{}", joyContractId);
			String tokenValue = StpEmployeeUtil.getTokenValue();
			if (!stringRedisTemplate.hasKey(tokenValue)) {
				throw new UnAuthException(ResultEnum.AUTH_DENY);
			} 
			String accessToken = stringRedisTemplate.opsForValue().get(tokenValue);
			JoyResultResp<String> resultVo = thirdServerClient.generateContrackLock(joyContractId, accessToken);
			log.info("审批通过，调用朝昔接口返回结果：{}", resultVo);
			if (ResultEnum.OK.getCode().equals(resultVo.getCode())) {
				contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNING.getDictKey());
			} else if (RsmsConstant.JOY_ACCESS_TOKEN_EXPIRED_CODE.equals(resultVo.getCode())) {
				authorServerClient.empLogout();
				throw new UnAuthException(ResultEnum.AUTH_DENY);
			} else {
				return ResultVoUtil.error(resultVo.getCode(), resultVo.getMessage());
			}
		} else if (approvalType.equals(RsmsConstant.APPROVAL_REJECT)) {
			contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_DRAFT.getDictKey());
		}
		bizContractMapper.updateById(contract);
		return ResultVoUtil.success();
	}

	@Override
	public void submitApproval(Long contractId) {
		if (ObjectUtils.isEmpty(contractId)) {
			throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
		}
		TbBusinessContract contract = bizContractMapper.selectById(contractId);
		if (ObjectUtils.isEmpty(contract)) {
			throw new BusinessException(BusinessEnum.CONTRACT_ID_NOT_EXIST);
		}
		contract.setUpdateUser(AuthUtil.getLoginIdAsLong());
		contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_REVIEWING.getDictKey());
		bizContractMapper.updateById(contract);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResultVo<String> deleteContract(Long contractId) {
		if (ObjectUtils.isEmpty(contractId)) {
			throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
		}
		TbBusinessContract contract = bizContractMapper.selectById(contractId);
		if (ObjectUtils.isEmpty(contract)) {
			throw new BusinessException(BusinessEnum.CONTRACT_ID_NOT_EXIST);
		}
		if (!DropdownEnum.CONTRACT_STATUS_DRAFT.getDictKey().equals(contract.getContractStatus())) {
			throw new BusinessException(BusinessEnum.CONTRACT_DELETE_NOT_ALLOWED);
		}
		String joyContractId = contract.getJoyContractId();
		if (!ObjectUtils.isEmpty(joyContractId)) {
			String tokenValue = StpEmployeeUtil.getTokenValue();
			if (!stringRedisTemplate.hasKey(tokenValue)) {
				throw new UnAuthException(ResultEnum.AUTH_DENY);
			} 
			String accessToken = stringRedisTemplate.opsForValue().get(tokenValue);
			//删除朝昔合同
			JoyRecallParam joyRecallParam = new JoyRecallParam();
			joyRecallParam.setAccessToken(accessToken);
			joyRecallParam.setJoyContractId(contract.getJoyContractId());
			JoyResultResp<String> resultVo = thirdServerClient.recallJoyContract(joyRecallParam);
			log.info("删除合同接口调用朝昔撤回合同接口返回结果：{}", resultVo);
			if (ResultEnum.OK.getCode().equals(resultVo.getCode())) {
				bizContractMapper.deleteById(contractId);
			} else if (RsmsConstant.JOY_ACCESS_TOKEN_EXPIRED_CODE.equals(resultVo.getCode())) {
				authorServerClient.empLogout();
				throw new UnAuthException(ResultEnum.AUTH_DENY);
			} else {
				throw new BizException(ResultEnum.ERROR);
			}
		} else {
			bizContractMapper.deleteById(contractId);
		}
		return ResultVoUtil.success();
	}

	@Override
	public void resignContract(Long contractId) {
		if (ObjectUtils.isEmpty(contractId)) {
			throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
		}
		TbBusinessContract contract = bizContractMapper.selectById(contractId);
		if (ObjectUtils.isEmpty(contract)) {
			throw new BusinessException(BusinessEnum.CONTRACT_ID_NOT_EXIST);
		}
		String contractStatus = contract.getContractStatus();
		if (!DropdownEnum.CONTRACT_STATUS_REVIEWED.getDictKey().equals(contractStatus) 
				&& !DropdownEnum.CONTRACT_STATUS_SIGNING.getDictKey().equals(contractStatus)
				&& !DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_A.getDictKey().equals(contractStatus)
				&& !DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_B.getDictKey().equals(contractStatus)) {
			throw new BusinessException(BusinessEnum.CONTRACT_RESIGN_NOT_ALLOWED);
		}
		String accessToken = getAccessToken();
		//删除朝昔合同
		JoyRecallParam joyRecallParam = new JoyRecallParam();
		joyRecallParam.setAccessToken(accessToken);
		joyRecallParam.setJoyContractId(contract.getJoyContractId());
		JoyResultResp<String> resultVo = thirdServerClient.recallJoyContract(joyRecallParam);
		log.info("重新签约调用朝昔撤回合同接口返回结果：{}", resultVo);
		if (ResultEnum.OK.getCode().equals(resultVo.getCode())) {
			contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_TERMINATION.getDictKey());
			bizContractMapper.updateById(contract);
		} else if (RsmsConstant.JOY_ACCESS_TOKEN_EXPIRED_CODE.equals(resultVo.getCode())) {
			authorServerClient.empLogout();
			throw new UnAuthException(ResultEnum.AUTH_DENY);
		} else {
			throw new BusinessException(resultVo.getCode(), resultVo.getMessage());
		}
	}

	@Override
	public List<CustomerAssetResp> listCustomerAssets(String customerMobile) {
		if (ObjectUtils.isEmpty(customerMobile)) {
			throw new BusinessException(BusinessEnum.CUSTOMER_MOBILE_NOT_NULL);
		}
		List<CustomerAssetResp> resultList = new ArrayList<>();
		LambdaQueryWrapper<TbBusinessContract> queryWrapper = new LambdaQueryWrapper<TbBusinessContract>();
		queryWrapper.and(wrapper -> {
			wrapper.eq(TbBusinessContract::getCustomerMobile, customerMobile);
			wrapper.or().eq(TbBusinessContract::getLeaserMobile, customerMobile);
		});
		queryWrapper.in(TbBusinessContract::getContractStatus, 
				List.of(DropdownEnum.CONTRACT_STATUS_SIGNING.getDictKey(), 
						DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_A.getDictKey(), 
						DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_B.getDictKey(), 
						DropdownEnum.CONTRACT_STATUS_SIGNED_FINISH.getDictKey(),
						DropdownEnum.CONTRACT_STATUS_GENERATED_REPORT.getDictKey(),
						DropdownEnum.CONTRACT_STATUS_PAYMENT_FOR_ALL.getDictKey(),
						DropdownEnum.CONTRACT_STATUS_PAYMENT_FOR_PARTIAL.getDictKey()));
		queryWrapper.orderByDesc(TbBusinessContract::getCreateTime);
		Set<Long> houseIdSet = new HashSet<>();
		Set<Long> parkIdSet = new HashSet<>();
		//合同信息
		List<TbBusinessContract> contractList = bizContractMapper.selectList(queryWrapper);
		//从意向金查询房源
		List<TbBusinessDeposit> depositList = depositMapper
				.selectList(new LambdaQueryWrapper<TbBusinessDeposit>().eq(TbBusinessDeposit::getCustomerMobile, customerMobile));
		List<CustomerAssetResp> houseAssetList = getCustomerHouseAsset(houseIdSet, contractList, depositList);
		List<CustomerAssetResp> parkAssetList = getCustomerParkAsset(parkIdSet, contractList, depositList);
		resultList.addAll(houseAssetList);
		resultList.addAll(parkAssetList);
		return resultList;
	}

	/**
	 * 客户房源资产获取
	 * @param houseIdSet 房源ID集合
	 * @param contractList 合同列表
	 * @param depositList 意向金列表
	 * @return
	 */
	private List<CustomerAssetResp> getCustomerHouseAsset(Set<Long> houseIdSet,
			List<TbBusinessContract> contractList, List<TbBusinessDeposit> depositList) {
		List<CustomerAssetResp> resultList = new ArrayList<>();
		Map<Long, List<TbBusinessContract>> contractMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(contractList)) {
			houseIdSet.addAll(contractList.stream().filter(item -> (!ObjectUtils.isEmpty(item.getHouseId()))).map(TbBusinessContract::getHouseId).collect(Collectors.toSet()));
			contractMap = contractList.stream().filter(item -> (!ObjectUtils.isEmpty(item.getHouseId()))).collect(Collectors.groupingBy(TbBusinessContract::getHouseId));
		}
		if (!CollectionUtils.isEmpty(depositList)) {
			for (TbBusinessDeposit tbd : depositList) {
				String houseDesc = tbd.getHouseDesc();
				if (!ObjectUtils.isEmpty(houseDesc)) {
					String[] houseDescArr = houseDesc.split(RsmsConstant.SPLIT_COMMA);
					for (String houseDescStr : houseDescArr) {
						houseIdSet.add(Long.parseLong(houseDescStr));
					}
				}
			}
		} 
		if (!CollectionUtils.isEmpty(houseIdSet)) {
			List<HouseInfoVo> houseInfoList = dataServerClient.listHouseInfoByIds(new ArrayList<>(houseIdSet));
			//封面图片获取
			Map<Long, List<DataFileInfoVo>> coverUrlMap = dataServerClient.listHouseCoverUrl(new ArrayList<>(houseIdSet));
			Map<Long, HouseInfoVo> houseMap = new HashMap<>();
			if (!CollectionUtils.isEmpty(houseInfoList)) {
				houseMap = houseInfoList.stream().collect(Collectors.toMap(HouseInfoVo::getId, house -> house));
			}
			for (Long houseId : houseIdSet) {
				CustomerAssetResp customerAssetResp = new CustomerAssetResp();
				customerAssetResp.setAssetsId(houseId);
				HouseInfoVo house = houseMap.get(houseId);
				if (!ObjectUtils.isEmpty(house)) {
					List<DataFileInfoVo> coverUrlList = coverUrlMap.get(houseId);
					if (!CollectionUtils.isEmpty(coverUrlList)) {
						customerAssetResp.setCoverUrl(coverUrlList.get(0).getPresignedUrl() == null ? null : coverUrlList.get(0).getPresignedUrl().toString());
					}
					List<TbBusinessContract> groupContractList = contractMap.get(houseId);
					if (!CollectionUtils.isEmpty(groupContractList)) {
						groupContractList.sort(Comparator.comparing(TbBusinessContract::getId).reversed());
						customerAssetResp.setContractId(groupContractList.get(0).getId());
					}
					customerAssetResp.setArea(house.getArea());
					customerAssetResp.setLayoutCode(house.getLayoutCode());
					customerAssetResp.setOrient(house.getOrient());
					customerAssetResp.setRentStatus(house.getRentStatus());
					customerAssetResp.setAssetsInfo(house.getLocation() 
							+ RsmsConstant.SPLIT_VERTICAL_LINE + house.getHouseNumber() + RsmsConstant.SPLIT_VERTICAL_LINE + house.getCommunityName());
					customerAssetResp.setAssetsType(DropdownEnum.CUSTOMER_ASSETS_TYPE_HOUSE.getDictKey());
				}
				resultList.add(customerAssetResp);
			}
		}
		return resultList;
	}
	
	/**
	 * 客户车位资产获取
	 * @param parkIdSet 车位ID集合
	 * @param contractList 合同列表
	 * @param depositList 意向金列表
	 * @return
	 */
	private List<CustomerAssetResp> getCustomerParkAsset(Set<Long> parkIdSet,
			List<TbBusinessContract> contractList, List<TbBusinessDeposit> depositList) {
		List<CustomerAssetResp> resultList = new ArrayList<>();
		Map<Long, List<TbBusinessContract>> contractMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(contractList)) {
			parkIdSet.addAll(contractList.stream().filter(item -> (!ObjectUtils.isEmpty(item.getParkingSpaceId()))).map(TbBusinessContract::getParkingSpaceId).collect(Collectors.toSet()));
			contractMap = contractList.stream().filter(item -> (!ObjectUtils.isEmpty(item.getParkingSpaceId()))).collect(Collectors.groupingBy(TbBusinessContract::getParkingSpaceId));
		}
		if (!CollectionUtils.isEmpty(depositList)) {
			for (TbBusinessDeposit tbd : depositList) {
				String parkSpaceDesc = tbd.getParkingSpaceDesc();
				if (!ObjectUtils.isEmpty(parkSpaceDesc)) {
					String[] parkSpaceDescArr = parkSpaceDesc.split(RsmsConstant.SPLIT_COMMA);
					for (String parkSpaceDescStr : parkSpaceDescArr) {
						parkIdSet.add(Long.parseLong(parkSpaceDescStr));
					}
				}
			}
		} 
		parkIdSet = parkIdSet.stream().filter(item -> !ObjectUtils.isEmpty(item)).collect(Collectors.toSet());
		List<ParkingSpaceResp> parkingSpaceRespList = dataServerClient.getParkingSpaceByIdList(new ArrayList<>(parkIdSet));
		Map<Long, ParkingSpaceResp> parkSpaceMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(parkingSpaceRespList)) {
			parkSpaceMap = parkingSpaceRespList.stream().collect(Collectors.toMap(ParkingSpaceResp::getId, park -> park));
		}
		for (Long parkId : parkIdSet) {
			CustomerAssetResp customerAssetResp = new CustomerAssetResp();
			customerAssetResp.setAssetsId(parkId);
			ParkingSpaceResp parkSpace = parkSpaceMap.get(parkId);
			if (!ObjectUtils.isEmpty(parkSpace)) {
				List<FileInfoResp> fileList = parkSpace.getPictureList();
				if (!CollectionUtils.isEmpty(fileList)) {
					fileList = fileList.stream().filter(item -> item.getCovered() == 1).collect(Collectors.toList());
					if (!CollectionUtils.isEmpty(fileList)) {
						customerAssetResp.setCoverUrl(fileList.get(0).getPresignedUrl() == null ? null : fileList.get(0).getPresignedUrl().toString());
					}
				}
				List<TbBusinessContract> groupContractList = contractMap.get(parkId);
				if (!CollectionUtils.isEmpty(groupContractList)) {
					groupContractList.sort(Comparator.comparing(TbBusinessContract::getId).reversed());
					customerAssetResp.setContractId(groupContractList.get(0).getId());
				}
				customerAssetResp.setArea(parkSpace.getArea());
				customerAssetResp.setParkType(parkSpace.getType()); //车位类型
				customerAssetResp.setParkOrient(parkSpace.getLocation()); //车位位置
				customerAssetResp.setParkRentStatus(parkSpace.getSaleStatus());;
				customerAssetResp.setAssetsInfo(parkSpace.getBuildingName() 
						+ RsmsConstant.SPLIT_VERTICAL_LINE + parkSpace.getNumber() + RsmsConstant.SPLIT_VERTICAL_LINE + parkSpace.getCommunityName());
				customerAssetResp.setAssetsType(DropdownEnum.CUSTOMER_ASSETS_TYPE_PARK.getDictKey());
			}
			resultList.add(customerAssetResp);
		}
		return resultList;
	}

	@Override
	public CustomerAssetDetailResp customerAssetsDetail(Long assetsId, String customerMobile, String assetsType) {
		if (ObjectUtils.isEmpty(assetsId) || ObjectUtils.isEmpty(customerMobile)) {
			throw new BusinessException(BusinessEnum.ASSETS_ID_NOT_NULL);
		}
		CustomerAssetDetailResp customerAssetDetailResp = getCustomerHouseAssetsDetail(assetsId, assetsType);
		//合同信息获取
		LambdaQueryWrapper<TbBusinessContract> queryWrapper = new LambdaQueryWrapper<TbBusinessContract>();
		queryWrapper.and(wrapper -> {
			wrapper.eq(TbBusinessContract::getCustomerMobile, customerMobile);
			wrapper.or().eq(TbBusinessContract::getLeaserMobile, customerMobile);
		});
		if (DropdownEnum.CUSTOMER_ASSETS_TYPE_HOUSE.getDictKey().equals(assetsType)) {
			queryWrapper.eq(TbBusinessContract::getHouseId, assetsId);
		} else {
			queryWrapper.eq(TbBusinessContract::getParkingSpaceId, assetsId);
		}
		queryWrapper.in(TbBusinessContract::getContractStatus, List.of(
				DropdownEnum.CONTRACT_STATUS_SIGNING.getDictKey(), 
				DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_A.getDictKey(),
				DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_B.getDictKey(),
				DropdownEnum.CONTRACT_STATUS_SIGNED_FINISH.getDictKey(),
				DropdownEnum.CONTRACT_STATUS_GENERATED_REPORT.getDictKey(),
				DropdownEnum.CONTRACT_STATUS_PAYMENT_FOR_ALL.getDictKey(),
				DropdownEnum.CONTRACT_STATUS_PAYMENT_FOR_PARTIAL.getDictKey()));
		queryWrapper.orderByDesc(TbBusinessContract::getCreateTime);
		List<TbBusinessContract> contractList = bizContractMapper.selectList(queryWrapper);
		List<ContractResp> contractRespList = bizContractConvert.bizContractList2ConractRespList(contractList);
		if (!CollectionUtils.isEmpty(contractRespList)) {
			//客户信息
			List<Long> customerIdList = contractList.stream().map(TbBusinessContract::getCustomerId).collect(Collectors.toList());
			List<CustomerSourceResp> customerQueryList = dataServerClient.innerQueryCustomerByIdList(customerIdList);
			Map<Long, CustomerSourceResp> customerMap = new HashMap<>();
			if (!CollectionUtils.isEmpty(customerQueryList)) {
				customerMap = customerQueryList.stream().collect(Collectors.toMap(CustomerSourceResp::getId, customer -> customer));
			}
			for (ContractResp item : contractRespList) {
				item.setCustomerMobileEncode(Base64Util.encodeData(item.getCustomerMobile()));
				item.setCustomerMobile(PersonDataEncryptUtil.mobileEncrypt(item.getCustomerMobile()));
				item.setLeaserMobileEncode(Base64Util.encodeData(item.getLeaserMobile()));
				item.setLeaserMobile(PersonDataEncryptUtil.mobileEncrypt(item.getLeaserMobile()));
				item.setCustomerName(customerMap.get(item.getCustomerId()) == null ? null : customerMap.get(item.getCustomerId()).getName());
//				item.setContractHouseInfo(house.getLocation() 
//						+ RsmsConstant.SPLIT_VERTICAL_LINE + house.getHouseNumber() + RsmsConstant.SPLIT_VERTICAL_LINE + house.getCommunityName());
			}
		}
		//合同信息
		customerAssetDetailResp.setContractInfos(contractRespList);
		return customerAssetDetailResp;
	}

	private CustomerAssetDetailResp getCustomerHouseAssetsDetail(Long assetsId, String assetsType) {
		CustomerAssetDetailResp customerAssetDetailResp = new CustomerAssetDetailResp();
		if (DropdownEnum.CUSTOMER_ASSETS_TYPE_HOUSE.getDictKey().equals(assetsType)) {
			List<HouseInfoVo> houseInfoList = dataServerClient.listHouseInfoByIds(List.of(assetsId));
			if (CollectionUtils.isEmpty(houseInfoList)) {
				log.info("资产信息：{} 不存在", assetsId);
				throw new BusinessException(BusinessEnum.ASSETS_INFO_NOT_EXIST);
			}
			HouseInfoVo house = houseInfoList.get(0);
			customerAssetDetailResp = bizContractConvert.houseInfo2AssetDetail(house);
			//封面图片获取
			Map<Long, List<DataFileInfoVo>> coverUrlMap = dataServerClient.listHouseCoverUrl(List.of(assetsId));
			customerAssetDetailResp.setContractHouseInfo(house.getLocation() 
					+ RsmsConstant.SPLIT_VERTICAL_LINE + house.getHouseNumber() + RsmsConstant.SPLIT_VERTICAL_LINE + house.getCommunityName());
			List<DataFileInfoVo> coverUrlList = coverUrlMap.get(assetsId);
			if (!CollectionUtils.isEmpty(coverUrlList)) {
				customerAssetDetailResp.setConverUrl(coverUrlList.get(0).getPresignedUrl() == null ? null : coverUrlList.get(0).getPresignedUrl().toString());
			}
		} else {
			List<ParkingSpaceResp> parkingSpaceRespList = dataServerClient.getParkingSpaceByIdList(List.of(assetsId));
			if (!CollectionUtils.isEmpty(parkingSpaceRespList)) {
				ParkingSpaceResp parkSpaceResp = parkingSpaceRespList.get(0);
				if (!ObjectUtils.isEmpty(parkSpaceResp)) {
					List<FileInfoResp> fileList = parkSpaceResp.getPictureList();
					if (!CollectionUtils.isEmpty(fileList)) {
						fileList = fileList.stream().filter(item -> !ObjectUtils.isEmpty(item.getCovered()) && item.getCovered() == 1).collect(Collectors.toList());
						customerAssetDetailResp.setConverUrl(fileList.get(0).getPresignedUrl() == null ? null : fileList.get(0).getPresignedUrl().toString());
					}
					customerAssetDetailResp.setParkRentStatus(parkSpaceResp.getSaleStatus());
					customerAssetDetailResp.setArea(parkSpaceResp.getArea());
					customerAssetDetailResp.setParkNumber(parkSpaceResp.getNumber());
					customerAssetDetailResp.setParkType(parkSpaceResp.getType()); //车位类型
					customerAssetDetailResp.setParkOrient(parkSpaceResp.getLocation()); //车位位置
					customerAssetDetailResp.setCommunityName(parkSpaceResp.getCommunityName());
					customerAssetDetailResp.setBuildingName(parkSpaceResp.getBuildingName());
				}
			}
		}
		return customerAssetDetailResp;
	}

	@Override
	public void updateJoyContractId(Long contractId, String joyContractId) {
		TbBusinessContract bizContract = bizContractMapper.selectById(contractId);
		bizContract.setJoyContractId(joyContractId);
		bizContract.setUpdateUser(AuthUtil.getLoginIdAsLong());
		bizContractMapper.updateById(bizContract);
	}

	@Override
	public void syncJoyContractStatusToRsms() {
		log.info("=============朝昔合同状态定时任务开始执行==============");
		List<String> contractStatusList = List.of(
				DropdownEnum.CONTRACT_STATUS_SIGNING.getDictKey(),
				DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_A.getDictKey(),
				DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_B.getDictKey());
		List<TbBusinessContract> contractList = bizContractMapper
				.selectList(new LambdaQueryWrapper<TbBusinessContract>().in(TbBusinessContract::getContractStatus, contractStatusList));
		if (!CollectionUtils.isEmpty(contractList)) {
			for (TbBusinessContract contract: contractList) {
				if (DropdownEnum.CONTRACT_TYPE_HOUSE_RENT_RESIDENT.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RENT_COMMERCIAL.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_OWNERSHIP.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_RENT.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_DELEGATE_HANDLER_REAL_ESTATE.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENTIAL_CD.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_COMMERCIAL_RENTIAL_CD.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_CD.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_TRIPARTITE_TERMINATION_AGREEMENT_CD.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENTIAL_SZ.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_COMMERCIAL_RENTIAL_SZ.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_RENT_SZ.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_SZ.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_WH.getDictKey().equals(contract.getContractType())) {
					dealThirdContract(contract);
				} else if (DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_DELEGATE_HANDLER_REAL_ESTATE.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_SERVICE_CD.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_SERVICE_SZ.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENTIAL_GZ.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_COMMERCIAL_RENTIAL_GZ.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_SERVICE_GZ.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_LEASE_CONTRACT_NEW_SZ.getDictKey().equals(contract.getContractType())) {
					//甲乙方
					dealPartAAndPartBContract(contract);
				} else if (DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENT_SELL_REAL_ESTATE_AGENCY.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_DELEGATE_HANDLER_BANK_LOAN.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_INTERMEDIARY_SERVICE_FEE_CONFIRM_CD.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_EARNEST_MONEY_TO_INTERMEDIARY_SERVICE_FEE_CD.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_LOOK_BUY_RENT_REAL_CD.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENT_SELL_REAL_ESTATE_AGENCY_CD.getDictKey().equals(contract.getContractType())) {
					//甲丙方
					dealTwoContract(contract);
				} else if (DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_LOOK_BUY_RENT_SELL_REAL_ESTATE_AGENCY.getDictKey().equals(contract.getContractType())) {
					//乙丙方
					dealTwoContract(contract);
				}
			}
			List<TbBusinessContract> updateContractList = new ArrayList<>();
			for (TbBusinessContract contract: contractList) {
				updateContractList.add(contract);
				if (updateContractList.size() >= RsmsConstant.JOY_CONTRACT_BATCH_SIZE) {
					bizContractMapper.updateById(updateContractList);
					updateContractList.clear();
				}
			}
			bizContractMapper.updateById(updateContractList);
		}
	}

	/**
	 * 处理三方合同：甲乙丙
	 * @param contract
	 */
	private void dealThirdContract(TbBusinessContract contract) {
		try {
			log.info("---------甲乙丙方合同签署状态更新------------");
			String joyContractId = contract.getJoyContractId();
			//乙方
			String customerId = contract.getCustomerMobile();
			//甲方
			String leaserId = contract.getLeaserMobile();
			log.info("合同编号：{}，朝昔合同ID：{} 甲方为：{}，乙方为：{}", contract.getContractNumber(), joyContractId, leaserId, customerId);
			//调用朝昔接口获取合同状态
			JoyContractSignedResp joyContractSignedResp = thirdServerClient.joyContractDetail(joyContractId);
			JoyContractSigned joyContractSigned = joyContractSignedResp.getData();
			if (!ObjectUtils.isEmpty(joyContractSigned)) {
				List<JoyContractSignedRecord> signedRecordList = joyContractSigned.getSignedList();
				if (!CollectionUtils.isEmpty(signedRecordList)) {
					signedRecordList.stream().forEach(item -> {
						String customerMobile = item.getSignCustId();
						Integer signedStatus = item.getSignState();
						log.info("签约详情手机号：{}，签署状态：{}", customerMobile, signedStatus);
						if (!ObjectUtils.isEmpty(customerMobile) && leaserId.equals(customerMobile)
								&& !ObjectUtils.isEmpty(signedStatus) && signedStatus.intValue() == RsmsConstant.SIGNED_FINISHED) {
							contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_A.getDictKey());
						} else if (!ObjectUtils.isEmpty(customerMobile) && customerId.equals(customerMobile)
								&& !ObjectUtils.isEmpty(signedStatus) && signedStatus.intValue() == RsmsConstant.SIGNED_FINISHED) {
							contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_B.getDictKey());
						}
					});
					int signedStateSum = signedRecordList.stream().mapToInt(JoyContractSignedRecord::getSignState).sum();
					if (signedStateSum == RsmsConstant.BOTH_SIGNED_FINISHED) {
						contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FINISH.getDictKey());
					}
				} else {
					log.info("合同编号：{}，朝昔合同ID：{} 还没有签约记录", contract.getContractNumber(), joyContractId);
				}
			}
		} catch (Exception e) {
			log.error("同步朝昔合同：{} 状态出错，出错原因：{}", contract.getContractNumber(), e);
		}
	}
	
	/**
	 * 处理两方合同：甲乙
	 * @param contract
	 */
	private void dealPartAAndPartBContract(TbBusinessContract contract) {
		try {
			log.info("---------甲乙方合同签署状态更新------------");
			String joyContractId = contract.getJoyContractId();
			//乙方
			String customerId = contract.getCustomerMobile();
			//甲方
			String leaserId = contract.getLeaserMobile();
			log.info("合同编号：{}，朝昔合同ID：{} 甲方为：{}，乙方为：{}", contract.getContractNumber(), joyContractId, leaserId, customerId);
			//调用朝昔接口获取合同状态
			JoyContractSignedResp joyContractSignedResp = thirdServerClient.joyContractDetail(joyContractId);
			JoyContractSigned joyContractSigned = joyContractSignedResp.getData();
			if (!ObjectUtils.isEmpty(joyContractSigned)) {
				List<JoyContractSignedRecord> signedRecordList = joyContractSigned.getSignedList();
				if (!CollectionUtils.isEmpty(signedRecordList)) {
					signedRecordList.stream().forEach(item -> {
						String customerMobile = item.getSignCustId();
						Integer signedStatus = item.getSignState();
						log.info("签约详情手机号：{}，签署状态：{}", customerMobile, signedStatus);
						if (!ObjectUtils.isEmpty(customerMobile) && leaserId.equals(customerMobile)
								&& !ObjectUtils.isEmpty(signedStatus) && signedStatus.intValue() == RsmsConstant.SIGNED_FINISHED) {
							contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_A.getDictKey());
						} else if (!ObjectUtils.isEmpty(customerMobile) && customerId.equals(customerMobile)
								&& !ObjectUtils.isEmpty(signedStatus) && signedStatus.intValue() == RsmsConstant.SIGNED_FINISHED) {
							contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_B.getDictKey());
						}
					});
					int signedStateSum = signedRecordList.stream().mapToInt(JoyContractSignedRecord::getSignState).sum();
					if (signedStateSum == RsmsConstant.TWO_SIGNED_FINISHED) {
						contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FINISH.getDictKey());
					}
				} else {
					log.info("合同编号：{}，朝昔合同ID：{} 还没有签约记录", contract.getContractNumber(), joyContractId);
				}
			}
		} catch (Exception e) {
			log.error("同步朝昔合同：{} 状态出错，出错原因：{}", contract.getContractNumber(), e);
		}
	}
	
	/**
	 * 处理两方合同:甲丙或乙丙
	 * @param contract
	 */
	private void dealTwoContract(TbBusinessContract contract) {
		try {
			log.info("----------甲丙或乙丙合同签署状态更新---------");
			String joyContractId = contract.getJoyContractId();
			//乙方
			String customerId = contract.getCustomerMobile();
			//甲方
			String leaserId = contract.getLeaserMobile();
			log.info("合同编号：{}，朝昔合同ID：{} 甲方为：{}，乙方为：{}", contract.getContractNumber(), joyContractId, leaserId, customerId);
			//调用朝昔接口获取合同状态
			JoyContractSignedResp joyContractSignedResp = thirdServerClient.joyContractDetail(joyContractId);
			JoyContractSigned joyContractSigned = joyContractSignedResp.getData();
			if (!ObjectUtils.isEmpty(joyContractSigned)) {
				List<JoyContractSignedRecord> signedRecordList = joyContractSigned.getSignedList();
				if (!CollectionUtils.isEmpty(signedRecordList)) {
					signedRecordList.stream().forEach(item -> {
						String customerMobile = item.getSignCustId();
						Integer signedStatus = item.getSignState();
						log.info("签约详情手机号：{}，签署状态：{}", customerMobile, signedStatus);
						if (!ObjectUtils.isEmpty(customerMobile) && !ObjectUtils.isEmpty(leaserId) && leaserId.equals(customerMobile)
								&& !ObjectUtils.isEmpty(signedStatus) &&  signedStatus.intValue() == RsmsConstant.SIGNED_FINISHED) {
							contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FINISH.getDictKey());
						} else if (!ObjectUtils.isEmpty(customerMobile) && !ObjectUtils.isEmpty(customerId) && customerId.equals(customerMobile)
								&& !ObjectUtils.isEmpty(signedStatus) && signedStatus.intValue() == RsmsConstant.SIGNED_FINISHED) {
							contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FINISH.getDictKey());
						}
					});
				} else {
					log.info("合同编号：{}，朝昔合同ID：{} 还没有签约记录", contract.getContractNumber(), joyContractId);
				}
			}
		} catch (Exception e) {
			log.error("同步朝昔合同：{} 状态出错，出错原因：{}", contract.getContractNumber(), e);
		}
	}

	@Override
	public ResultVo<String> getJoyContractLockUrl(String zxContractId, String customerMobile) {
		//判断此手机号是否签署
		TbBusinessContract contract = bizContractMapper
				.selectOne(new LambdaQueryWrapper<TbBusinessContract>().eq(TbBusinessContract::getJoyContractId, zxContractId).last("limit 1"));
		if (!ObjectUtils.isEmpty(contract)) {
			String leaserMobile = contract.getLeaserMobile();
			String custMobile = contract.getCustomerMobile();
			String contractStatus = contract.getContractStatus();
			if (DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_A.getDictKey().equals(contractStatus) && customerMobile.equals(leaserMobile)) {
				throw new BusinessException(BusinessEnum.LEASER_HAD_SIGNED);
			} else if (DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_B.getDictKey().equals(contractStatus) && customerMobile.equals(custMobile)) {
				throw new BusinessException(BusinessEnum.CUSTOMER_HAD_SIGNED);
			}
		}
		String accessToken = thirdServerClient.getJoyAccessToken();
//		String accessToken = getAccessToken();
		log.info("朝昔合同：{} 签署获取到的token为：{}", zxContractId, accessToken);
		StringBuilder signUrl = new StringBuilder();
		signUrl.append(joySignPageUrl).append("?contractId=").append(zxContractId)
			.append("&customerId=").append(customerMobile).append("#access_token=").append(accessToken);
		log.info("租售合同签署后端返回地址为：{}", signUrl.toString());
		return ResultVoUtil.success(signUrl.toString());
	}

	@Override
	public ResultVo<String> getJoyContractDetailUrl(String zxContractId, String source) {
		String accessToken = thirdServerClient.getJoyAccessToken();
		log.info("获取朝昔合同:{} 详情地址获取到的token为：{}", zxContractId, accessToken);
		StringBuilder signUrl = new StringBuilder();
		signUrl.append(joyContractDetailUrl).append("?contractId=").append(zxContractId)
			.append("&customerId=").append("#access_token=").append(accessToken);
		log.info("朝昔合同详情signUrl:{}", signUrl.toString());
		return ResultVoUtil.success(signUrl.toString());
	}

	private String getAccessToken() {
		String tokenValue = StpEmployeeUtil.getTokenValue();
		if (!stringRedisTemplate.hasKey(tokenValue)) {
			throw new UnAuthException(ResultEnum.AUTH_DENY);
		} 
		String accessToken = stringRedisTemplate.opsForValue().get(tokenValue);
		if (ObjectUtils.isEmpty(accessToken)) {
			throw new UnAuthException(ResultEnum.AUTH_DENY);
		}
		return accessToken;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void archiveContract(ContractArchiveParams archiveParams) {
		Long contractId = archiveParams.getContractId();
		if (ObjectUtils.isEmpty(contractId)) {
			throw new BusinessException(CommonEnum.REQUEST_ID_PARAMS_ERROR);
		}
		List<FileInfoParam> attachmentList = new ArrayList<>();
		//房屋交接附件
		List<FileInfoParam> houseHandoverList = archiveParams.getHouseHandoverAttachment();
		if (!CollectionUtils.isEmpty(houseHandoverList)) {
			houseHandoverList = houseHandoverList.stream()
					.filter(item -> RsmsConstant.CONTRACT_ARCHIVE.equals(item.getFileTag())).collect(Collectors.toList());
			attachmentList.addAll(houseHandoverList);
		}
		//甲乙方身份证明复印件
		List<FileInfoParam> identityList = archiveParams.getIdentityAttachment();
		if (!CollectionUtils.isEmpty(identityList)) {
			identityList = identityList.stream()
					.filter(item -> RsmsConstant.CONTRACT_ARCHIVE.equals(item.getFileTag())).collect(Collectors.toList());
			attachmentList.addAll(identityList);
		}
		//甲方房屋产权资料复印件
		List<FileInfoParam> leaserOwnershipList = archiveParams.getLeaserOwnershipAttachment();
		if (!CollectionUtils.isEmpty(leaserOwnershipList)) {
			leaserOwnershipList = leaserOwnershipList.stream()
					.filter(item -> RsmsConstant.CONTRACT_ARCHIVE.equals(item.getFileTag())).collect(Collectors.toList());
			attachmentList.addAll(leaserOwnershipList);
		} 
		//乙方身份证明证件
		List<FileInfoParam> customerIdList = archiveParams.getCustomerIdentityAttachment();
		if (!CollectionUtils.isEmpty(customerIdList)) {
			customerIdList = customerIdList.stream()
					.filter(item -> RsmsConstant.CONTRACT_ARCHIVE.equals(item.getFileTag())).collect(Collectors.toList());
			attachmentList.addAll(customerIdList);
		} 
		//甲方车位产权资料复印件
		List<FileInfoParam> leaserCarOwnershipList = archiveParams.getLeaserCarOwnershipAttachment();
		if (!CollectionUtils.isEmpty(leaserCarOwnershipList)) {
			leaserCarOwnershipList = leaserCarOwnershipList.stream()
					.filter(item -> RsmsConstant.CONTRACT_ARCHIVE.equals(item.getFileTag())).collect(Collectors.toList());
			attachmentList.addAll(leaserCarOwnershipList);
		} 
		//乙方车辆资料复印件
		List<FileInfoParam> customerCarInfoList = archiveParams.getCustomerCarInfoAttachment();
		if (!CollectionUtils.isEmpty(customerCarInfoList)) {
			customerCarInfoList = customerCarInfoList.stream()
					.filter(item -> RsmsConstant.CONTRACT_ARCHIVE.equals(item.getFileTag())).collect(Collectors.toList());
			attachmentList.addAll(customerCarInfoList);
		} 
		//补充条款
		List<FileInfoParam> supplementaryTermsList = archiveParams.getSupplementaryTermsAttachment();
		if (!CollectionUtils.isEmpty(supplementaryTermsList)) {
			supplementaryTermsList = supplementaryTermsList.stream()
					.filter(item -> RsmsConstant.CONTRACT_ARCHIVE.equals(item.getFileTag())).collect(Collectors.toList());
			attachmentList.addAll(supplementaryTermsList);
		} 
		//房屋平面图、装修一览表
		List<FileInfoParam> houseDesignAndDecorationList = archiveParams.getHouseDesignAndDecorationAttachment();
		if (!CollectionUtils.isEmpty(houseDesignAndDecorationList)) {
			houseDesignAndDecorationList = houseDesignAndDecorationList.stream()
					.filter(item -> RsmsConstant.CONTRACT_ARCHIVE.equals(item.getFileTag())).collect(Collectors.toList());
			attachmentList.addAll(houseDesignAndDecorationList);
		} 
		attachmentList.forEach(item -> {
			item.setBusinessId(contractId);
		});
		TbBusinessContract contract = bizContractMapper.selectById(contractId);
		contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_ARCHIVE.getDictKey());
		bizContractMapper.updateById(contract);
		//保存归档附件
		ContractFileInfoParam fileInfoParam = new ContractFileInfoParam();
		fileInfoParam.setContractId(contractId);
		fileInfoParam.setHouseFileList(attachmentList);
		//保存附件
		dataServerClient.innerSaveContractFile(fileInfoParam);
	}

	@Override
	public void realTimeUpdateJoyContractStatus(JoyContractCallbackParam callbackParam) {
		if (ObjectUtils.isEmpty(callbackParam)) {
			log.error("回调参数为空，请检查参数");
			return;
		}
		if (ObjectUtils.isEmpty(callbackParam.getResource())) {
			log.error("回调业务参数为空，请检查业务参数");
			return;
		}
		ObjectMapper objectMapper = new ObjectMapper();
		String joyContractId = callbackParam.getResource().getContractId();
		List<ContractSign> signedRecordList = callbackParam.getResource().getContractSignObjectList();
		try {
			log.info("合同状态实时更新，回调签署状态记录数据为：{}", objectMapper.writeValueAsString(signedRecordList));
		} catch (JsonProcessingException e) {
			throw new BusinessException(CommonEnum.OTHER_ERROR);
		}
		List<TbBusinessContract> contractList = bizContractMapper
				.selectList(new LambdaQueryWrapper<TbBusinessContract>().in(TbBusinessContract::getJoyContractId, joyContractId));
		if (!CollectionUtils.isEmpty(contractList)) {
			for (TbBusinessContract contract: contractList) {
				//乙方
				String customerId = contract.getCustomerMobile();
				//甲方
				String leaserId = contract.getLeaserMobile();
				if (DropdownEnum.CONTRACT_TYPE_HOUSE_RENT_RESIDENT.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RENT_COMMERCIAL.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_OWNERSHIP.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_RENT.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_DELEGATE_HANDLER_REAL_ESTATE.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENTIAL_CD.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_COMMERCIAL_RENTIAL_CD.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_CD.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_TRIPARTITE_TERMINATION_AGREEMENT_CD.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENTIAL_SZ.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_COMMERCIAL_RENTIAL_SZ.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_PARK_SPACE_RENT_SZ.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_SZ.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_WH.getDictKey().equals(contract.getContractType())) {
					//甲乙丙三方合同状态更新
					if (!CollectionUtils.isEmpty(signedRecordList)) {
						signedRecordList.stream().forEach(item -> {
							String customerMobile = item.getSignObjectMobile();
							Integer signedStatus = item.getSignStatus();
							log.info("签约详情手机号：{}，签署状态：{}", customerMobile, signedStatus);
							if (!ObjectUtils.isEmpty(customerMobile) && leaserId.equals(customerMobile)
									&& !ObjectUtils.isEmpty(signedStatus) && signedStatus.intValue() == RsmsConstant.SIGNED_FINISHED) {
								contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_A.getDictKey());
							} else if (!ObjectUtils.isEmpty(customerMobile) && customerId.equals(customerMobile)
									&& !ObjectUtils.isEmpty(signedStatus) && signedStatus.intValue() == RsmsConstant.SIGNED_FINISHED) {
								contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_B.getDictKey());
							}
						});
						int signedStateSum = signedRecordList.stream().mapToInt(ContractSign::getSignStatus).sum();
						if (signedStateSum == RsmsConstant.BOTH_SIGNED_FINISHED) {
							contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FINISH.getDictKey());
						}
					} else {
						log.info("合同编号：{}，朝昔合同ID：{} 还没有签约记录", contract.getContractNumber(), joyContractId);
					}
				} else if (DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_DELEGATE_HANDLER_REAL_ESTATE.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_SERVICE_CD.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_SERVICE_SZ.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENTIAL_GZ.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_COMMERCIAL_RENTIAL_GZ.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_SERVICE_GZ.getDictKey().equals(contract.getContractType())
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_LEASE_CONTRACT_NEW_SZ.getDictKey().equals(contract.getContractType())) {
					//甲乙方
//					dealPartAAndPartBContract(contract);
					if (!CollectionUtils.isEmpty(signedRecordList)) {
						signedRecordList.stream().forEach(item -> {
							String customerMobile = item.getSignObjectMobile();
							Integer signedStatus = item.getSignStatus();
							log.info("签约详情手机号：{}，签署状态：{}", customerMobile, signedStatus);
							if (!ObjectUtils.isEmpty(customerMobile) && leaserId.equals(customerMobile)
									&& !ObjectUtils.isEmpty(signedStatus) && signedStatus.intValue() == RsmsConstant.SIGNED_FINISHED) {
								contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_A.getDictKey());
							} else if (!ObjectUtils.isEmpty(customerMobile) && customerId.equals(customerMobile)
									&& !ObjectUtils.isEmpty(signedStatus) && signedStatus.intValue() == RsmsConstant.SIGNED_FINISHED) {
								contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FOR_B.getDictKey());
							}
						});
						int signedStateSum = signedRecordList.stream().mapToInt(ContractSign::getSignStatus).sum();
						if (signedStateSum == RsmsConstant.TWO_SIGNED_FINISHED) {
							contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FINISH.getDictKey());
						}
					} else {
						log.info("合同编号：{}，朝昔合同ID：{} 还没有签约记录", contract.getContractNumber(), joyContractId);
					}
				} else if (DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENT_SELL_REAL_ESTATE_AGENCY.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_DELEGATE_HANDLER_BANK_LOAN.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_INTERMEDIARY_SERVICE_FEE_CONFIRM_CD.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_EARNEST_MONEY_TO_INTERMEDIARY_SERVICE_FEE_CD.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_LOOK_BUY_RENT_REAL_CD.getDictKey().equals(contract.getContractType()) 
						|| DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_RENT_SELL_REAL_ESTATE_AGENCY_CD.getDictKey().equals(contract.getContractType())) {
					//甲丙方
//					dealTwoContract(contract);
					realTimeUpdatePartBAndPartC(joyContractId, signedRecordList, contract, customerId, leaserId);
				} else if (DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_LOOK_BUY_RENT_SELL_REAL_ESTATE_AGENCY.getDictKey().equals(contract.getContractType())) {
					realTimeUpdatePartBAndPartC(joyContractId, signedRecordList, contract, customerId, leaserId);
				}
			}
			List<TbBusinessContract> updateContractList = new ArrayList<>();
			for (TbBusinessContract contract: contractList) {
				updateContractList.add(contract);
				if (updateContractList.size() >= RsmsConstant.JOY_CONTRACT_BATCH_SIZE) {
					bizContractMapper.updateById(updateContractList);
					updateContractList.clear();
				}
			}
			bizContractMapper.updateById(updateContractList);
		}
	}

	/**
	 * 实时更新乙丙或甲丙合同
	 * @param joyContractId
	 * @param signedRecordList
	 * @param contract
	 * @param customerId
	 * @param leaserId
	 */
	private void realTimeUpdatePartBAndPartC(String joyContractId, List<ContractSign> signedRecordList,
			TbBusinessContract contract, String customerId, String leaserId) {
		//乙丙方
		if (!CollectionUtils.isEmpty(signedRecordList)) {
			signedRecordList.stream().forEach(item -> {
				String customerMobile = item.getSignObjectMobile();
				Integer signedStatus = item.getSignStatus();
				log.info("签约详情手机号：{}，签署状态：{}", customerMobile, signedStatus);
				if (!ObjectUtils.isEmpty(customerMobile) && !ObjectUtils.isEmpty(leaserId) && leaserId.equals(customerMobile)
						&& !ObjectUtils.isEmpty(signedStatus) &&  signedStatus.intValue() == RsmsConstant.SIGNED_FINISHED) {
					contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FINISH.getDictKey());
				} else if (!ObjectUtils.isEmpty(customerMobile) && !ObjectUtils.isEmpty(customerId) && customerId.equals(customerMobile)
						&& !ObjectUtils.isEmpty(signedStatus) && signedStatus.intValue() == RsmsConstant.SIGNED_FINISHED) {
					contract.setContractStatus(DropdownEnum.CONTRACT_STATUS_SIGNED_FINISH.getDictKey());
				}
			});
		} else {
			log.info("合同编号：{}，朝昔合同ID：{} 还没有签约记录", contract.getContractNumber(), joyContractId);
		}
	}
	

	/**
	 * 检查合同租赁到期定时任务
	 */
	public void inspectContractLeaseTask(String jobDate) {
		log.info("==============开始执行合同租赁到期定时任务================");;
		int pageNo = 1, pageSize = 100;
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		if (ObjectUtils.isEmpty(jobDate)) {
			jobDate = LocalDateTime.now().format(formatter);
		}
		final String jobExeDate = jobDate;
		while (true) {
			List<TbBusinessContract> contractList = bizContractMapper.selectList(Page.of(pageNo, pageSize), Wrappers.lambdaQuery(TbBusinessContract.class)
					.select(TbBusinessContract::getId, TbBusinessContract::getJoyContractId, TbBusinessContract::getCreateUser,
							TbBusinessContract::getContractNumber, TbBusinessContract::getHouseId, TbBusinessContract::getParkingSpaceId)
					.notIn(TbBusinessContract::getContractType, List.of(
							DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL.getDictKey(),
							DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY.getDictKey(),
							DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_CD.getDictKey(),
							DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_SERVICE_CD.getDictKey(),
							DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_SERVICE_SZ.getDictKey(),
							DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_SZ.getDictKey(),
							DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_SERVICE_GZ.getDictKey(),
							DropdownEnum.CONTRACT_TYPE_HOUSE_RESIDENT_SELL_INTERMEDIARY_WH.getDictKey()
					))
					.isNotNull(TbBusinessContract::getJoyContractId)
					.notIn(TbBusinessContract::getContractStatus, List.of(
							DropdownEnum.CONTRACT_STATUS_DRAFT.getDictKey(),
							DropdownEnum.CONTRACT_STATUS_CANCEL.getDictKey()))
//					.eq(TbBusinessContract::getJoyContractId, "664491810653829")
			);
			if (ObjectUtils.isEmpty(contractList)) {
				log.info("当前第：{} 页获取到的数据为空", pageNo);
				break;
			}
			contractList.forEach(contract -> {
				JoyContractFieldInfoResp contractFieldInfo = thirdServerClient.getContractFieldInfo(contract.getJoyContractId());
				if (ObjectUtils.isEmpty(contractFieldInfo) || ObjectUtils.isEmpty(contractFieldInfo.getData())) {
					log.info("合同ID:{} 未查询到填写字段属性信息....", contract.getJoyContractId());
					return;
				}
				if (ObjectUtils.isEmpty(contractFieldInfo.getData().getBaseFieldList())) {
					log.info("合同ID:{} 基础字段列表 信息为空....", contract.getJoyContractId());
					return;
				}
				Optional<JoyContractBaseFields> any = Arrays.stream(contractFieldInfo.getData().getBaseFieldList())
						.filter(joyContractBaseFields -> !ObjectUtils.isEmpty(joyContractBaseFields.getDataLabel()) 
								&& joyContractLeaseDateValue.equals(joyContractBaseFields.getDataLabel()))
						.findAny();
				if (any.isEmpty()) {
					log.info("合同ID:{} 未查询到合同租约结束日期标签，合同信息属性内容：{}......", contract.getJoyContractId(), contractFieldInfo);
				} else {
					JoyContractBaseFields joyContractBaseFields = any.get();
					String dataValue = joyContractBaseFields.getDataValue();
					log.info("当前合同ID：{} 获取到的租约结束日期为：{}", contract.getJoyContractId(), dataValue);
					if (ObjectUtils.isEmpty(dataValue)) {
						return;
					}
					LocalDate contractEndDate = DateUtil.stringToLocalDate(dataValue, RsmsConstant.DATE_PATTERN_MIX);
					long daysBetween = ChronoUnit.DAYS.between(LocalDate.now(), contractEndDate);
					if (Math.abs(daysBetween) == RsmsConstant.ONE_MONTH_DAYS || Math.abs(daysBetween) == RsmsConstant.HALF_MONTH_DAYS) {
						// 通知 成交人、成交人的店长、成交人的区域经理
						Long houseId = contract.getHouseId();
						Long parkingId = contract.getParkingSpaceId();
						log.info("合同编号：{} 对应的房源ID：{}，车位ID：{}", contract.getContractNumber(), houseId, parkingId);
						//成交人信息
						Long createUser = contract.getCreateUser();
						Map<Long, UserDetailsMicroResp> userDetailsMicroRespMap = authorServerClient.getUserDetailsMicro(
								List.of(createUser)).stream().collect(Collectors.toMap(UserDetailsMicroResp::getId, Function.identity(), (existing, next) -> existing));
						List<Long> storeIdList = userDetailsMicroRespMap.values()
								.stream().map(UserDetailsMicroResp::getStoreId)
								.filter(Objects::nonNull)
								.map(Long::valueOf)
								.toList();
						Map<Long, List<Long>> roleStoreIdMap = dataServerClient.batchFindTargetRoleUserIdByOrgId(storeIdList, "店长");
						Map<Long, List<Long>> roleAreaIdMap = dataServerClient.batchFindTargetRoleUserIdByOrgId(storeIdList, "区域经理");
						//成交人店长
						List<Long> roleUserIdList = new LinkedList<>(
								roleStoreIdMap.values().stream().flatMap(Collection::stream).toList());
						//成交人区域经理
						List<Long> roleAreaIdList = roleAreaIdMap.values().stream().flatMap(Collection::stream).toList();
						roleUserIdList.addAll(roleAreaIdList);
						if (!ObjectUtils.isEmpty(houseId)) {
							HouseInfoVo houseInfoVo = dataServerClient.listHouseInfoByIds(List.of(houseId)).stream()
									.collect(Collectors.toMap(HouseInfoVo::getId, Function.identity(), (existing, next) -> existing)).get(houseId);
							String tip = TemplateUtil.processTemplate(UserTipTemplateEnum.CONTRACT_HOUSE_LEASE.getMsg(), Map.of(
									"location", houseInfoVo.getLocation(),
									"houseNumber", houseInfoVo.getHouseNumber(),
									"community", houseInfoVo.getCommunityName(),
									"date", jobExeDate
							));
							List<UserTipParam> userTipParams = roleUserIdList.stream()
									.map(userId -> {
										UserTipParam userTipParam = new UserTipParam();
										userTipParam.setUserId(userId);
										userTipParam.setMsg(tip);
										return userTipParam;
									}).toList();
							dataServerClient.batchPushTip(userTipParams);
						}
						if (!ObjectUtils.isEmpty(parkingId)) {
							ParkingSpaceResp parkingSpaceResp = dataServerClient.getParkingSpaceByIdList(List.of(parkingId)).stream()
									.collect(Collectors.toMap(ParkingSpaceResp::getId, Function.identity(), (existing, next) -> existing)).get(parkingId);
							String tip = TemplateUtil.processTemplate(UserTipTemplateEnum.CONTRACT_PARKING_LEASE.getMsg(), Map.of(
									"houseNumber", parkingSpaceResp.getNumber(),
									"community", parkingSpaceResp.getCommunityName(),
									"date", jobExeDate
							));
							List<UserTipParam> userTipParams = roleUserIdList.stream()
									.map(userId -> {
										UserTipParam userTipParam = new UserTipParam();
										userTipParam.setUserId(userId);
										userTipParam.setMsg(tip);
										return userTipParam;
									}).toList();
							dataServerClient.batchPushTip(userTipParams);
						}
					}
				}
			});
			pageNo++;
		}
	}
}
