package mixc.be.rsms.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import mixc.be.rsms.business.domain.dto.PreviewPerformanceParams;
import mixc.be.rsms.business.domain.dto.TransactionReportAddParams;
import mixc.be.rsms.business.domain.dto.TransactionReportEditParams;
import mixc.be.rsms.business.domain.dto.TransactionReportQueryParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionReport;
import mixc.be.rsms.business.vo.PreviewPerformanceVo;
import mixc.be.rsms.business.vo.ReceiveCommunityVo;
import mixc.be.rsms.business.vo.TransactionReportVo;
import mixc.be.rsms.pojo.business.TransactionReportResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 成交报告表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
public interface ITransactionReportService {


    /**
     * 分页查询
     * @param params
     * @return
     */
    IPage<TransactionReportVo> page(TransactionReportQueryParams params);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Integer deleteById(Long id);

    /**
     * 提交
     *
     * @param params
     * @return
     */
    Long submit(TransactionReportEditParams params);

    /**
     * 结案
     *
     * @param params
     * @return
     */
    Long close(TransactionReportEditParams params);

    /**
     * 审核或者驳回
     *
     * @param params
     * @return
     */
    Long auditOrReject(TransactionReportEditParams params);

    /**
     * 复核或者驳回
     *
     * @param params
     * @return
     */
    Long reviewOrReject(TransactionReportEditParams params);

    /**
     * 子订单状态转 收佣行订单状态
     *
     * @param orderStatus
     * @return
     */
    String subOrderStatus2CommissionPlanOrderStatus(String orderStatus);

    /**
     * 反审核
     *
     * @param params
     * @return
     */
    Long reverseAudit(TransactionReportEditParams params);

    /**
     * 新增/修改
     *
     * @param params
     * @return
     */
    Long saveOrUpdate(TransactionReportAddParams params);


    /**
     * 返回详情
     *
     * @param id
     * @return
     */
    TransactionReportVo getDetail(Long id);
    /**
     * 根据客户ID 查询成交报告
     * @param customerIds
     * @return
     */
    List<TransactionReportResp> queryTransactionReportByCustomerId(List<Long> customerIds);

    /**
     * 根据id 查询成交报告
     *
     * @param ids
     * @return
     */
    List<TransactionReportResp> queryTransactionReportIdById(List<Long> ids);

    /**
     * 根据业务ID 查询成交报告
     * @param businessIdList
     * @return
     */
    List<TransactionReportResp> selectInfoOnOperateLog(List<Long> businessIdList);

    /**
     * 根据房源Id 查询成交报告
     * @param assetsId
     * @return
     */
    List<TransactionReportResp> queryTransactionReportByAssetsId(Long assetsId);

    /**
     * 获取收费项（调用租售系统获取）
     *
     * @param communityId         二手房项目ID
     * @param newHouseCommunityId 新房项目Id
     * @return
     */
    List<ReceiveCommunityVo> getReceiveCommunityList(Long communityId, Long newHouseCommunityId, String transactionContractNum);

    PreviewPerformanceVo previewPerformance(PreviewPerformanceParams params);

    void generateInfo(String contractNum);

    /**
     * 本月业绩 统计
     * @param userId
     * @return
     */
    BigDecimal achievementStatistics(Long userId);

    /**
     * 成交量统计
     */
    Long turnoverStatistics(Long userId);

    /**
     * @description 查询截止前一天运营复核通过（复核通过时间）且签约业绩结算状态为空的成交报告数据
     * <AUTHOR>
     * @throws
     * @return List<TbBusinessTransactionReport>
     * @time 2025/6/4 14:51
     */
    List<TbBusinessTransactionReport> queryTransactionReportBySettleStatus();
}
