package mixc.be.rsms.business.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import mixc.be.rsms.business.domain.dto.ContractArchiveParams;
import mixc.be.rsms.business.domain.dto.ContractSignAddParams;
import mixc.be.rsms.business.domain.dto.ContractSignQueryParams;
import mixc.be.rsms.business.domain.dto.JoyContractCallbackParam;
import mixc.be.rsms.business.domain.pojo.TbBusinessContract;
import mixc.be.rsms.business.vo.ContractPageVo;
import mixc.be.rsms.business.vo.ResultVo;
import mixc.be.rsms.pojo.mobile.CustomerAssetDetailResp;
import mixc.be.rsms.pojo.mobile.CustomerAssetResp;

/**
 * @ClassName IBizContractService
 * @Description 合同基本信息业务接口
 * <AUTHOR>
 * @date 2024-09-13
 */
public interface IBizContractService {

	/**
	 * 保存合同基本信息
	 * @param addParams 合同基本信息
	 * @return
	 */
	Long addContractSignBaseInfo(ContractSignAddParams addParams);
	
	/**
	 * 更新合同基础信息
	 * @param updateParams 更新参数
	 */
	void updateContract(ContractSignAddParams updateParams);
	
	/**
	 * 根据合同编号查询合同详情
	 * @param contractId 合同ID
	 * @return
	 */
	TbBusinessContract bizContractDetail(Long contractId);
	
	/**
	 * 合同签约分页查询
	 * @param queryParams
	 * @return
	 */
	Page<ContractPageVo> pageContractSign(ContractSignQueryParams queryParams);
	
	/**
	 * 合同签约分页查询
	 * @param queryParams
	 * @return
	 */
	Page<ContractPageVo> appPageContractSign(ContractSignQueryParams queryParams);
	
	/**
	 * 合同审批
	 * @param contractId 合同ID
	 * @param contractType 合同类型
	 */
	void submitApproval(Long contractId);
	
	/**
	 * 合同审批
	 * @param contractId 合同ID
	 * @param contractType 合同类型
	 */
	ResultVo<String> contractApproval(Long contractId, String contractType);
	
	/**
	 * 合同删除
	 * @param contractId 合同ID
	 */
	ResultVo<String> deleteContract(Long contractId);
	
	/**
	 * 合同重新签约
	 * @param contractId 合同ID
	 */
	void resignContract(Long contractId);
	
	/**
	 * 根据客户手机号获取客户资产
	 * @param customerMobile 客户手机号
	 * @return
	 */
	List<CustomerAssetResp> listCustomerAssets(String customerMobile);
	
	/**
	 * 根据资产ID获取客户资产详情
	 * @param assetsId 资产ID
	 * @param customerMobile 客户手机号
	 * @param assetsType 资产类型
	 * @return
	 */
	CustomerAssetDetailResp customerAssetsDetail(Long assetsId, String customerMobile, String assetsType);
	
	/**
	 * 更新朝昔合同ID
	 * @param contractId 租售合同ID
	 * @param joyContractId 朝昔合同ID
	 */
	void updateJoyContractId(Long contractId, String joyContractId);
	
	/**
	 * 朝昔合同签署状态同步到租售
	 */
	void syncJoyContractStatusToRsms();
	
	/**
	 * 获取契约锁地址页面
	 * @param zxContractId 朝昔合同ID
	 * @param customerMobile 客户手机号
	 * @return
	 */
	ResultVo<String> getJoyContractLockUrl(String zxContractId, String customerMobile);
	
	/**
	 * 获取朝昔合同详情地址页面
	 * @param zxContractId 朝昔合同ID
	 * @param source 来源
	 * @return
	 */
	ResultVo<String> getJoyContractDetailUrl(String zxContractId, String source);
	
	/**
	 * 合同归档
	 * @param archiveParams
	 */
	void archiveContract(ContractArchiveParams archiveParams);
	
	/**
	 * 根据回调参数实时更新合同状态
	 * @param callbackParam 回调参数
	 */
	void realTimeUpdateJoyContractStatus(JoyContractCallbackParam callbackParam);
	
	/** 
	 * 合同租约到期提醒
	 * @param jobDate 执行日期
	 */
	void inspectContractLeaseTask(String jobDate);
}
