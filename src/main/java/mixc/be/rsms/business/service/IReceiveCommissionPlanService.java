package mixc.be.rsms.business.service;

import mixc.be.rsms.business.domain.dto.CommissionPlanDto;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 收佣计划表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
public interface IReceiveCommissionPlanService {

    /**
     * @description 按成交报告id分组统计已收佣金和实收佣金
     * <AUTHOR>
     * @param[1] reportIds
     * @throws
     * @return List<CommissionPlanDto>
     * @time 2025/6/4 14:24
     */
    Map<Long, CommissionPlanDto> getReceivedCommissionByReportId(List<Long> reportIds);


}
