package mixc.be.rsms.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.domain.dto.CommissionPlanDto;
import mixc.be.rsms.business.domain.pojo.TbBusinessReceiveCommissionPlan;
import mixc.be.rsms.business.mapper.TbBusinessReceiveCommissionPlanMapper;
import mixc.be.rsms.business.service.IReceiveCommissionPlanService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 收佣计划表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
@Slf4j
public class ReceiveCommissionPlanServiceImpl implements IReceiveCommissionPlanService {

    private final TbBusinessReceiveCommissionPlanMapper commissionPlanMapper;

    @Override
    public Map<Long, CommissionPlanDto> getReceivedCommissionByReportId(List<Long> reportIds) {
        if (ObjectUtils.isEmpty(reportIds)) {
            return Map.of();
        }
        log.info("按成交报告id分组统计数据总条数，reportIds:{}", reportIds);
        //按成交报告id分组汇总实收佣金个应收佣金之和
        QueryWrapper<TbBusinessReceiveCommissionPlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("report_id", "sum(receivable_commission) as receivable_commission", "sum(received_commission) as received_commission")
                .in("report_id", reportIds)
                .groupBy("report_id");
        return commissionPlanMapper.selectList(queryWrapper)
                .stream()
                .collect(Collectors.toMap(TbBusinessReceiveCommissionPlan::getReportId, item -> {
                    CommissionPlanDto commissionPlanDto = new CommissionPlanDto();
                    commissionPlanDto.setReportId(item.getReportId());
                    commissionPlanDto.setReceivableCommissionTotal(item.getReceivableCommission());
                    commissionPlanDto.setReceivedCommissionTotal(item.getReceivedCommission());
                    return commissionPlanDto;
                }));
    }
}
