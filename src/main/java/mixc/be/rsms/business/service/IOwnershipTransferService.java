package mixc.be.rsms.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import mixc.be.rsms.business.domain.dto.OwnershipTransferQueryParams;
import mixc.be.rsms.business.domain.dto.OwnershipTransferUpdateParams;
import mixc.be.rsms.business.vo.OwnershipTransferVo;
import mixc.be.rsms.pojo.business.OwnershipTransferResp;

import java.util.List;

/**
 * <p>
 * 权证过户 业务接口类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-20
 */
public interface IOwnershipTransferService {
	
	/**
	 * 新增或更新
	 * @param params 权证过户更新参数
	 * @return
	 */
	void saveOrUpdate(OwnershipTransferUpdateParams params);

	/**
	 * 权证过户详情
	 *
	 * @param id 主键
	 * @return
	 */
	OwnershipTransferVo queryOwnershipTransferDetail(Long id);

	/**
	 * 移动端查看详情
	 * @param transactionContractNums 成交合同编号列表
	 * @return
	 */
	List<OwnershipTransferResp> mobileGetDetail(List<String> transactionContractNums);

    /**
     * 房源跟进分页查询
     * @param queryParams 分页查询参数
     * @return
     */
    IPage<OwnershipTransferVo> pageOwnershipTransfer(OwnershipTransferQueryParams queryParams);


	/**
	 * 移动端 查询客户权证过户信息
	 * @param mobile
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	IPage<OwnershipTransferVo> pageOwnershipByMobile(String mobile, Integer pageNum, Integer pageSize);

}
